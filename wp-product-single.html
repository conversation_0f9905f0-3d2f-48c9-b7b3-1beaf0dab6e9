<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US" wp-template wp-template-master="wp-index.html" wp-template-export-as="woocommerce/single-product.php">
    <head>
        <meta charset="utf-8"/>
        <title>KARUCA</title>
        <meta name="author" content="themesflat.com"/>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1"/>
        <!-- font -->
        <link rel="stylesheet" href="fonts/fonts.css"/>
        <link rel="stylesheet" href="fonts/font-icons.css"/>
        <link rel="stylesheet" href="css/bootstrap.min.css"/>
        <link rel="stylesheet" href="css/swiper-bundle.min.css"/>
        <link rel="stylesheet" href="css/animate.css"/>
        <link rel="stylesheet" ype="text/css" href="css/styles.css"/>
        <!-- Favicon and Touch Icons  -->
        <link rel="shortcut icon" href="images/logo/favicon.png"/>
        <link rel="apple-touch-icon-precomposed" href="images/logo/favicon.png"/>
    </head>
    <body class="preload-wrapper popup-loader">
        <wpcontent wp-site-content>
            <div id="wrapper" class="">
                <div>
                    <div class="mt-5 tf-page-title">
                        <div class="container-full">
                            <div class="row">
                                <div class="col-12">
                                    <div class="font-druk heading mb-4 text-center" cms-post-title>
                                        Sample Page
</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-5">
                        <div class="container">
                            <div class="align-items-center d-flex flex-wrap mb-3 px-4 py-3 rounded-0 woonotice" wc-notice wc-notice-item="this" wc-notice-text="[data-pg-name=&quot;Show Notice Text&quot;]" wc-notice-add-classes="false" wc-notice-type="success">
                                <p class="fw-6 m-0 me-2">Success</p>
                                <div class="message" data-pg-name="Show Notice Text">Best seller</div>
                            </div>
                            <div class="align-items-center d-flex flex-wrap mb-3 px-4 py-3 rounded-0 text-danger woonoticeerr" wc-notice wc-notice-item="this" wc-notice-text="[data-pg-name=&quot;Show Notice Text&quot;]" wc-notice-add-classes="false" wc-notice-type="error">
                                <p class="fw-6 m-0 me-2">Failed</p>
                                <div class="message" data-pg-name="Show Notice Text">Best seller</div>
                            </div>
                            <div class="align-items-center d-flex flex-wrap mb-3 px-4 py-3 rounded-0 woonotice" wc-notice wc-notice-item="this" wc-notice-text="[data-pg-name=&quot;Show Notice Text&quot;]" wc-notice-add-classes="false">
                                <div class="message" data-pg-name="Show Notice Text">Best seller</div>
                            </div>
                        </div>
                    </div>
                    <singleproduct wc-product wc-product-mode="freestyle">
                        <section class="flat-spacing-4 pt_0">
                            <div class="tf-main-product section-image-zoom">
                                <div class="container">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="tf-product-media-wrap sticky-top">
                                                <div class="thumbs-slider">
                                                    <div class="swiper tf-product-media-thumbs other-image-zoom" data-direction="vertical">
                                                        <div class="swiper-wrapper stagger-wrap">
                                                            <div class="swiper-slide stagger-item" cms-no-export>
                                                                <div class="item">
                                                                    <img class="lazyload" data-src="https://dummyimage.com/1080x1280/000/fff" src="https://dummyimage.com/1080x1280/000/fff" alt="">
                                                                </div>
                                                            </div>
                                                            <?php
$main_image_id = get_post_thumbnail_id($product->get_id());
$attachment_ids = $product->get_gallery_image_ids();
$main_image_url = wp_get_attachment_image_src($main_image_id, 'full');

if ($main_image_url) {
    echo '<div class="swiper-slide stagger-item">';
    echo '<div class="item">';
    echo '<img class="lazyload" data-src="' . esc_url($main_image_url[0]) . '" src="' . esc_url($main_image_url[0]) . '" alt="' . esc_attr($product->get_name()) . '">';
    echo '</div>';
    echo '</div>';
}

if (!empty($attachment_ids)) {
    foreach ($attachment_ids as $attachment_id) {
        $gallery_image_url = wp_get_attachment_image_src($attachment_id, 'full');

        if ($gallery_image_url) {
            echo '<div class="swiper-slide stagger-item">';
            echo '<div class="item">';
            echo '<img class="lazyload" data-src="' . esc_url($gallery_image_url[0]) . '" src="' . esc_url($gallery_image_url[0]) . '" alt="' . esc_attr($product->get_name()) . '">';
            echo '</div>';
            echo '</div>';
        }
    }
}
?>
                                                        </div>
                                                    </div>
                                                    <div class="swiper tf-product-media-main" id="gallery-swiper-started">
                                                        <div class="swiper-wrapper">
                                                            <div class="swiper-slide" cms-no-export>
                                                                <a href="images/shop/products/hmgoepprod31.jpg" target="_blank" class="item" data-pswp-width="770px" data-pswp-height="1075px"> <img class="tf-image-zoom lazyload" data-zoom="https://dummyimage.com/1080x1280/000/fff" data-src="https://dummyimage.com/1080x1280/000/fff" src="https://dummyimage.com/1080x1280/000/fff" alt=""> </a>
                                                            </div>
                                                            <?php

$main_image_id = get_post_thumbnail_id($product->get_id());
$attachment_ids = $product->get_gallery_image_ids();
$main_image_url = wp_get_attachment_image_src($main_image_id, 'full');

if ($main_image_url) {
    echo '<div class="swiper-slide">';
    echo '<a href="' . esc_url($main_image_url[0]) . '" target="_blank" class="item" data-pswp-width="414" data-pswp-height="450">';
    echo '<img class="lazyload" data-src="' . esc_url($main_image_url[0]) . '" src="' . esc_url($main_image_url[0]) . '" alt="xx1' . esc_attr($product->get_name()) . '">';
    echo '</a>';
    echo '</div>';
}

if (!empty($attachment_ids)) {
    foreach ($attachment_ids as $attachment_id) {
        $gallery_image_url = wp_get_attachment_image_src($attachment_id, 'full');

        if ($gallery_image_url) {
            echo '<div class="swiper-slide">';
            echo '<a href="' . esc_url($gallery_image_url[0]) . '" target="_blank" class="item" data-pswp-width="770" data-pswp-height="1075">';
            echo '<img class="lazyload" data-src="' . esc_url($gallery_image_url[0]) . '" src="' . esc_url($gallery_image_url[0]) . '" alt="xx2' . esc_attr($product->get_name()) . '">';
            echo '</a>';
            echo '</div>';
        }
    }
}
?>
                                                        </div>
                                                        <div class="swiper-button-next button-style-arrow thumbs-next"></div>
                                                        <div class="swiper-button-prev button-style-arrow thumbs-prev"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="tf-product-info-wrap position-relative">
                                                <div class="tf-zoom-main"></div>
                                                <div class="tf-product-info-list other-image-zoom">
                                                    <div class="tf-product-info-title">
                                                        <h5 wc-product-title wc-product-title-template-variant="side_single" class="font-haas_mediu">Cotton jersey top</h5>
                                                    </div>
                                                    <div class="" role="img" aria-label="Rated 4.00 out of 5" wc-product-rating wc-product-rating-template-variant="single" wc-product-rating-mode="stars" wc-product-rating-active-star="[data-pg-name=&quot;Active Star&quot;]" wc-product-rating-half-star="[data-pg-name=&quot;Partial Star&quot;]" wc-product-rating-inactive-star="[data-pg-name=&quot;Inactive Star&quot;]" wc-product-rating-label-element="[data-pg-name=&quot;Display Label&quot;]" wc-product-rating-reviews-count="[data-pg-name=&quot;Reviews Count&quot;]" wc-product-rating-label-with-total="Rated {stars} out of 5."><span class="user-rating rating-stars"><span class="ecomus-svg-icon ecomus-svg-icon--star ecomus-svg-icon__inline"><i class="icon-start" data-pg-name="Active Star"></i></span><span class="ecomus-svg-icon ecomus-svg-icon--star ecomus-svg-icon__inline"><i class="icon-start" style="color: #7b7b7b;" data-pg-name="Partial Star"></i></span><span class="ecomus-svg-icon ecomus-svg-icon--star ecomus-svg-icon__inline"><i class="icon-start" style="color: #dbdbdb;" data-pg-name="Inactive Star"></i></span><span style="display: block;"><strong data-pg-name="Display Label"></strong></span><a href="#reviews" class="woocommerce-review-link" rel="nofollow" style="display: block;"><span class="count" data-pg-name="Reviews Count"></span></a></span>
                                                    </div>
                                                    <div class="tf-product-info-badges">
                                                        <div class="badges" wc-product-stock wc-product-stock-text="this">Best seller</div>
                                                        <p class="fw-6" wc-product-short-description wc-product-short-description-text="this">Selling fast! 56 people have  this in their carts.</p>
                                                    </div>
                                                    <div class="tf-product-info-price" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]">
                                                        <div class="price-on-sale" style="font-weight: 700;" data-pg-name="Regular Price Sale">$8.00</div>
                                                        <div class="price font-haas_mediu" data-pg-name="Regular Price No Sale">$18.00</div>
                                                        <div class="compare-at-price" data-pg-name="Sale Price">$10.00</div>
                                                        <div class="badges-on-sale bg-danger" wc-product-sale wc-product-sale-amount-sel="span" wc-product-sale-amount-type="amount" wc-product-sale-template-variant="savesingle">Save&nbsp;<span>&nbsp;20</span>
                                                        </div>
                                                    </div>
                                                    <?php
// Make sure you are within the WooCommerce context, such as a product page

global $product;

// Check if the current product is a simple product or variable product
if ($product->is_on_sale()) {
    // Get the sale end date
    $sale_end_date = $product->get_date_on_sale_to();

    // Check if the sale end date exists and is in the future
    if ($sale_end_date && $sale_end_date->getTimestamp() > time()) {
        ?>
                                                        <div class="tf-product-info-countdown">
                                                            <div class="countdown-wrap">
                                                                <div class="countdown-title"><i class="icon-time tf-ani-tada"></i>
                                                                    <p>HURRY UP! SALE ENDS IN:</p>
                                                                </div>
                                                                <div class="tf-countdown style-1">
                                                                    <div class="js-countdown" data-timer="<?php echo esc_attr(($sale_end_date->getTimestamp() - time())); ?>" data-labels="Days :,Hours :,Mins :,Secs"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php
    }
}
?>
                                                    <form class="variations_form cart swatches-support mb-5" wc-product-cart wc-product-cart-type="variable" wc-product-cart-variation-attribute="[data-pg-name=&quot;Repeated Attribute&quot;]" wc-product-cart-variation-attribute-label="[data-pg-name=&quot;Attribute Label&quot;]" wc-product-cart-variation-value="[data-pg-name=&quot;Attribute Selector&quot;]" wc-product-cart-variation-clear="[data-pg-name=&quot;Attribute Clear&quot;]" wc-product-cart-template-variant="single" wc-product-cart-variation-single-wrapper="[data-pg-name=&quot;Selected Variation Wrapper&quot;]" wc-product-cart-variation-add-to-cart="[data-pg-name=&quot;Quantity and Button Wrapper&quot;]" wc-product-cart-quantity="[data-pg-name=&quot;Quantity Field&quot;]" wc-product-cart-button="[data-pg-name=&quot;Add to Cart Button&quot;]"> 
                                                        <div class="tf-product-info-variant-picker"> 
                                                            <div class="variant-picker-item"> 
                                                                <div class="form-variant" id="variantform" action="./variant/variant-process.php" method="post"> 
                                                                    <div class="d-flex flex-column gap-15"> 
                                                                        <fieldset data-pg-name="Repeated Attribute"> 
                                                                            <label for="color" class="form-label" data-pg-name="Attribute Label">Color *</label>                                                                             
                                                                            <select class="form-control shadow-none border-dark rounded rounded-1 " required data-pg-name="Attribute Selector"> 
                                                                                <option value="" disabled selected>Select Color</option>                                                                                 
                                                                                <option value="orange">Orange</option>                                                                                 
                                                                                <option value="black">Black</option>                                                                                 
                                                                                <option value="white">White</option>                                                                                 
                                                                                <option value="red">Red</option>                                                                                 
                                                                            </select>
                                                                            <a class="fw-7 mt-3" data-pg-name="Attribute Clear"><i class="bi bi-trash3"></i> CLEAR</a> 
                                                                        </fieldset>                                                                         
                                                                    </div>                                                                     
                                                                </div>                                                                 
                                                            </div>                                                             
                                                        </div>                                                         
                                                        <div class="wraper_selected"> 
                                                            <div class="row" data-pg-name="Quantity and Button Wrapper">
                                                                <div class="row variant-item" data-pg-name="Selected Variation Wrapper">
</div>
                                                                <div class="tf-product-info-quantity mb-1 mt-3"> 
                                                                    <div class="quantity-title fw-6">Quantity</div>                                                                     
                                                                </div>
                                                                <div class="row pe-0"> 
                                                                    <div class="col-3 pe-0"> 
                                                                        <div class="wg-quantity" data-pg-name="Quantity Field"> <span class="btn-quantity minus-btn">-</span> 
                                                                            <input type="text" name="quantity" value="1"> <span class="btn-quantity plus-btn">+</span> 
                                                                        </div>                                                                         
                                                                    </div>                                                                     
                                                                    <div class="col-9 pe-0"> 
                                                                        <div class="tf-product-info-buy-button"> 
                                                                            <button class="tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart" style="width: 100%;" data-pg-name="Add to Cart Button"><span>Add to cart -&nbsp;</span><span class="tf-qty-price">$8.00</span> 
                                                                            </button>                                                                             
                                                                        </div>                                                                         
                                                                    </div>                                                                     
                                                                </div>
                                                            </div>                                                             
                                                        </div>                                                         
                                                    </form>
                                                    <form class="variations_form cart swatches-support mb-5" wc-product-cart wc-product-cart-variation-attribute="[data-pg-name=&quot;Repeated Attribute&quot;]" wc-product-cart-variation-attribute-label="[data-pg-name=&quot;Attribute Label&quot;]" wc-product-cart-variation-value="[data-pg-name=&quot;Attribute Selector&quot;]" wc-product-cart-variation-clear="[data-pg-name=&quot;Attribute Clear&quot;]" wc-product-cart-variation-single-wrapper="[data-pg-name=&quot;Selected Variation Wrapper&quot;]" wc-product-cart-variation-add-to-cart="[data-pg-name=&quot;Quantity and Button Wrapper&quot;]" wc-product-cart-quantity="[data-pg-name=&quot;Quantity Field&quot;]" wc-product-cart-button="[data-pg-name=&quot;Add to Cart Button&quot;]" wc-product-cart-template-variant="single" wc-product-cart-template="save" wc-product-cart-quantity-no-export="true"> 
                                                        <div class="wraper_selected" data-pg-name="Selected Variation Wrapper"> 
                                                            <div class="tf-product-info-quantity mb-3"> 
                                                                <div class="quantity-title fw-6">Quantity</div>
                                                            </div>
                                                            <div class="row" data-pg-name="Quantity and Button Wrapper">
                                                                <div class="col-3"> 
                                                                    <div class="wg-quantity" data-pg-name="Quantity Field"> <span class="btn-quantity minus-btn">-</span> 
                                                                        <input type="text" name="quantity" value="1"> <span class="btn-quantity plus-btn">+</span> 
                                                                    </div>                                                                     
                                                                </div>
                                                                <div class="col-9"> 
                                                                    <div class="tf-product-info-buy-button"> 
                                                                        <button class="tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart" style="width: 100%;" data-pg-name="Add to Cart Button"><span>Add to cart -&nbsp;</span><span class="tf-qty-price">$8.00</span> 
                                                                        </button>                                                                         
                                                                    </div>                                                                     
                                                                </div>
                                                            </div>                                                             
                                                        </div>                                                         
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="flat-spacing-17 pt_0">
                            <div class="container">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="widget-tabs style-has-border" wc-product-tabs wc-product-tabs-tabs="[data-pg-name=&quot;Tab List&quot;]" wc-product-tabs-panel="[data-pg-name=&quot;Repeated Panel&quot;]" wc-product-tabs-panel-content="[data-pg-name=&quot;Panel Content&quot;]" wc-product-tabs-nojs="true">
                                            <ul class="widget-menu-tab" data-pg-name="Tab List">
                                                <li class="item-title active"><span class="inner">Description</span>
                                                </li>
                                                <li class="item-title"><span class="inner">Review</span>
                                                </li>
                                                <li class="item-title"><span class="inner">Shipping</span>
                                                </li>
                                                <li class="item-title"><span class="inner">Return Policies</span>
                                                </li>
                                            </ul>
                                            <div class="widget-content-tab">
                                                <div class="widget-content-inner active" data-pg-name="Repeated Panel">
                                                    <div class="" data-pg-name="Panel Content">
                                                        <p class="mb_30">
                                            Button-up shirt sleeves and a relaxed silhouette. It’s tailored with drapey,
                                            crinkle-texture fabric that’s made from LENZING™ ECOVERO™ Viscose — responsibly
                                            sourced wood-based
                                            fibres produced through a process that reduces impact on forests, biodiversity and
                                            water supply. </p>
                                                        <div class="tf-product-des-demo">
                                                            <div class="right">
                                                                <h3 class="fs-16 fw-5">Features</h3>
                                                                <ul>
                                                                    <li>Front button placket</li>
                                                                    <li> Adjustable sleeve tabs</li>
                                                                    <li>Babaton embroidered crest at placket and hem</li>
                                                                </ul>
                                                                <h3 class="fs-16 fw-5">Materials Care</h3>
                                                                <ul class="mb-0">
                                                                    <li>Content: 100% LENZING™ ECOVERO™ Viscose</li>
                                                                    <li>Care: Hand wash</li>
                                                                    <li>Imported</li>
                                                                </ul>
                                                            </div>
                                                            <div class="left">
                                                                <h3 class="fs-16 fw-5">Materials Care</h3>
                                                                <div class="d-flex gap-10 mb_15 align-items-center">
                                                                    <div class="icon"><i class="icon-machine"></i>
                                                                    </div><span>Machine wash max. 30ºC. Short spin.</span>
                                                                </div>
                                                                <div class="d-flex gap-10 mb_15 align-items-center">
                                                                    <div class="icon"><i class="icon-iron"></i>
                                                                    </div><span>Iron maximum 110ºC.</span>
                                                                </div>
                                                                <div class="d-flex gap-10 mb_15 align-items-center">
                                                                    <div class="icon"><i class="icon-bleach"></i>
                                                                    </div><span>Do not bleach/bleach.</span>
                                                                </div>
                                                                <div class="d-flex gap-10 mb_15 align-items-center">
                                                                    <div class="icon"><i class="icon-dry-clean"></i>
                                                                    </div><span>Do not dry clean.</span>
                                                                </div>
                                                                <div class="d-flex gap-10 align-items-center">
                                                                    <div class="icon"><i class="icon-tumble-dry"></i>
                                                                    </div><span>Tumble dry, medium hear.</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="widget-content-inner">
                                                    <table class="tf-pr-attrs">
                                                        <tbody>
                                                            <tr class="tf-attr-pa-color">
                                                                <th class="tf-attr-label">Color</th>
                                                                <td class="tf-attr-value"> <p>White, Pink, Black</p> </td>
                                                            </tr>
                                                            <tr class="tf-attr-pa-size">
                                                                <th class="tf-attr-label">Size</th>
                                                                <td class="tf-attr-value"> <p>S, M, L, XL</p> </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <div class="widget-content-inner">
                                                    <div class="tf-page-privacy-policy">
                                                        <div class="title">The Company Private Limited Policy</div>
                                                        <p>The Company Private Limited and each of their respective subsidiary, parent and affiliated companies is deemed to operate this Website (“we” or “us”) recognizes that you care how information about you is used and shared. We have created this Privacy Policy to inform you what information we collect on the Website, how we use your information and the choices you have about the way your information is collected and used. Please read this Privacy Policy carefully. Your use of the Website indicates that you have read and accepted our privacy practices, as outlined in this Privacy Policy.</p>
                                                        <p>Please be advised that the practices described in this Privacy Policy apply to information gathered by us or our subsidiaries, affiliates or agents: (i) through this Website, (ii) where applicable, through our Customer Service Department in connection with this Website, (iii) through information provided to us in our free standing retail stores, and (iv) through information provided to us in conjunction with marketing promotions and sweepstakes.</p>
                                                        <p>We are not responsible for the content or privacy practices on any websites.</p>
                                                        <p>We reserve the right, in our sole discretion, to modify, update, add to, discontinue, remove or otherwise change any portion of this Privacy Policy, in whole or in part, at any time. When we amend this Privacy Policy, we will revise the “last updated” date located at the top of this Privacy Policy.</p>
                                                        <p>If you provide information to us or access or use the Website in any way after this Privacy Policy has been changed, you will be deemed to have unconditionally consented and agreed to such changes. The most current version of this Privacy Policy will be available on the Website and will supersede all previous versions of this Privacy Policy.</p>
                                                        <p>If you have any questions regarding this Privacy Policy, you should contact our Customer Service Department by <NAME_EMAIL></p>
                                                    </div>
                                                </div>
                                                <div class="widget-content-inner">
                                                    <ul class="d-flex justify-content-center mb_18">
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M8.7 30.7h22.7c.3 0 .6-.2.7-.6l4-25.3c-.1-.4-.3-.7-.7-.8s-.7.2-.8.6L34 8.9l-3-1.1c-2.4-.9-5.1-.5-7.2 1-2.3 1.6-5.3 1.6-7.6 0-2.1-1.5-4.8-1.9-7.2-1L6 8.9l-.7-4.3c0-.4-.4-.7-.7-.6-.4.1-.6.4-.6.8l4 25.3c.1.3.3.6.7.6zm.8-21.6c2-.7 4.2-.4 6 .8 1.4 1 3 1.5 4.6 1.5s3.2-.5 4.6-1.5c1.7-1.2 4-1.6 6-.8l3.3 1.2-3 19.1H9.2l-3-19.1 3.3-1.2zM32 32H8c-.4 0-.7.3-.7.7s.3.7.7.7h24c.4 0 .7-.3.7-.7s-.3-.7-.7-.7zm0 2.7H8c-.4 0-.7.3-.7.7s.3.6.7.6h24c.4 0 .7-.3.7-.7s-.3-.6-.7-.6zm-17.9-8.9c-1 0-1.8-.3-2.4-.6l.1-2.1c.6.4 1.4.6 2 .6.8 0 1.2-.4 1.2-1.3s-.4-1.3-1.3-1.3h-1.3l.2-1.9h1.1c.6 0 1-.3 1-1.3 0-.8-.4-1.2-1.1-1.2s-1.2.2-1.9.4l-.2-1.9c.7-.4 1.5-.6 2.3-.6 2 0 3 1.3 3 2.9 0 1.2-.4 1.9-1.1 2.3 1 .4 1.3 1.4 1.3 2.5.3 1.8-.6 3.5-2.9 3.5zm4-5.5c0-3.9 1.2-5.5 3.2-5.5s3.2 1.6 3.2 5.5-1.2 5.5-3.2 5.5-3.2-1.6-3.2-5.5zm4.1 0c0-2-.1-3.5-.9-3.5s-1 1.5-1 3.5.1 3.5 1 3.5c.8 0 .9-1.5.9-3.5zm4.5-1.4c-.9 0-1.5-.8-1.5-2.1s.6-2.1 1.5-2.1 1.5.8 1.5 2.1-.5 2.1-1.5 2.1zm0-.8c.4 0 .7-.5.7-1.2s-.2-1.2-.7-1.2-.7.5-.7 1.2.3 1.2.7 1.2z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M36.7 31.1l-2.8-1.3-4.7-9.1 7.5-3.5c.4-.2.6-.6.4-1s-.6-.5-1-.4l-7.5 3.5-7.8-15c-.3-.5-1.1-.5-1.4 0l-7.8 15L4 15.9c-.4-.2-.8 0-1 .4s0 .8.4 1l7.5 3.5-4.7 9.1-2.8 1.3c-.4.2-.6.6-.4 1 .1.3.4.4.7.4.1 0 .2 0 .3-.1l1-.4-1.5 2.8c-.1.2-.1.5 0 .8.1.2.4.3.7.3h31.7c.3 0 .5-.1.7-.4.1-.2.1-.5 0-.8L35.1 32l1 .4c.1 0 .2.1.3.1.3 0 .6-.2.7-.4.1-.3 0-.8-.4-1zm-5.1-2.3l-9.8-4.6 6-2.8 3.8 7.4zM20 6.4L27.1 20 20 23.3 12.9 20 20 6.4zm-7.8 15l6 2.8-9.8 4.6 3.8-7.4zm22.4 13.1H5.4L7.2 31 20 25l12.8 6 1.8 3.5z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M5.9 5.9v28.2h28.2V5.9H5.9zM19.1 20l-8.3 8.3c-2-2.2-3.2-5.1-3.2-8.3s1.2-6.1 3.2-8.3l8.3 8.3zm-7.4-9.3c2.2-2 5.1-3.2 8.3-3.2s6.1 1.2 8.3 3.2L20 19.1l-8.3-8.4zM20 20.9l8.3 8.3c-2.2 2-5.1 3.2-8.3 3.2s-6.1-1.2-8.3-3.2l8.3-8.3zm.9-.9l8.3-8.3c2 2.2 3.2 5.1 3.2 8.3s-1.2 6.1-3.2 8.3L20.9 20zm8.4-10.2c-1.2-1.1-2.6-2-4.1-2.6h6.6l-2.5 2.6zm-18.6 0L8.2 7.2h6.6c-1.5.6-2.9 1.5-4.1 2.6zm-.9.9c-1.1 1.2-2 2.6-2.6 4.1V8.2l2.6 2.5zM7.2 25.2c.6 1.5 1.5 2.9 2.6 4.1l-2.6 2.6v-6.7zm3.5 5c1.2 1.1 2.6 2 4.1 2.6H8.2l2.5-2.6zm18.6 0l2.6 2.6h-6.6c1.4-.6 2.8-1.5 4-2.6zm.9-.9c1.1-1.2 2-2.6 2.6-4.1v6.6l-2.6-2.5zm2.6-14.5c-.6-1.5-1.5-2.9-2.6-4.1l2.6-2.6v6.7z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M35.1 33.6L33.2 6.2c0-.4-.3-.7-.7-.7H13.9c-.4 0-.7.3-.7.7s.3.7.7.7h18l.7 10.5H20.8c-8.8.2-15.9 7.5-15.9 16.4 0 .4.3.7.7.7h28.9c.2 0 .4-.1.5-.2s.2-.3.2-.5v-.2h-.1zm-28.8-.5C6.7 25.3 13 19 20.8 18.9h11.9l1 14.2H6.3zm11.2-6.8c0 1.2-1 2.1-2.1 2.1s-2.1-1-2.1-2.1 1-2.1 2.1-2.1 2.1 1 2.1 2.1zm6.3 0c0 1.2-1 2.1-2.1 2.1-1.2 0-2.1-1-2.1-2.1s1-2.1 2.1-2.1 2.1 1 2.1 2.1z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M20 33.8c7.6 0 13.8-6.2 13.8-13.8S27.6 6.2 20 6.2 6.2 12.4 6.2 20 12.4 33.8 20 33.8zm0-26.3c6.9 0 12.5 5.6 12.5 12.5S26.9 32.5 20 32.5 7.5 26.9 7.5 20 13.1 7.5 20 7.5zm-.4 15h.5c1.8 0 3-1.1 3-3.7 0-2.2-1.1-3.6-3.1-3.6h-2.6v10.6h2.2v-3.3zm0-5.2h.4c.6 0 .9.5.9 1.7 0 1.1-.3 1.7-.9 1.7h-.4v-3.4z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M30.2 29.3c2.2-2.5 3.6-5.7 3.6-9.3s-1.4-6.8-3.6-9.3l3.6-3.6c.3-.3.3-.7 0-.9-.3-.3-.7-.3-.9 0l-3.6 3.6c-2.5-2.2-5.7-3.6-9.3-3.6s-6.8 1.4-9.3 3.6L7.1 6.2c-.3-.3-.7-.3-.9 0-.3.3-.3.7 0 .9l3.6 3.6c-2.2 2.5-3.6 5.7-3.6 9.3s1.4 6.8 3.6 9.3l-3.6 3.6c-.3.3-.3.7 0 .9.1.1.3.2.5.2s.3-.1.5-.2l3.6-3.6c2.5 2.2 5.7 3.6 9.3 3.6s6.8-1.4 9.3-3.6l3.6 3.6c.1.1.3.2.5.2s.3-.1.5-.2c.3-.3.3-.7 0-.9l-3.8-3.6z">
</path>
                                                            </svg>
                                                        </li>
                                                        <li class="">
                                                            <svg viewBox="0 0 40 40" width="35px" height="35px" color="#222" margin="5px">
                                                                <path fill="currentColor" d="M34.1 34.1H5.9V5.9h28.2v28.2zM7.2 32.8h25.6V7.2H7.2v25.6zm13.5-18.3a.68.68 0 0 0-.7-.7.68.68 0 0 0-.7.7v10.9a.68.68 0 0 0 .7.7.68.68 0 0 0 .7-.7V14.5z">
</path>
                                                            </svg>
                                                        </li>
                                                    </ul>
                                                    <p class="text-center text-paragraph">LT01: 70% wool, 15% polyester, 10% polyamide, 5% acrylic 900 Grms/mt</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="bg_grey-5 flat-spacing-27" wc-product-related wc-product-related-heading="[data-pg-name=&quot;Heading&quot;]" wc-product-related-item="[data-pg-name=&quot;Repeated Item&quot;]" wc-product-related-posts-per-page="10">
                            <div class="container">
                                <div class="flat-title title-upper flex-row justify-content-between px-0"><span class="title wow fadeInUp font-druk" data-wow-delay="0s" data-pg-name="Heading">SKATEBOARDS</span>
                                    <div class="box-sw-navigation">
                                        <div class="nav-sw round nav-next-product-3 nav-next-slider">
                                            <span class="icon icon-arrow-left"></span>
                                        </div>
                                        <div class="nav-sw round nav-prev-product-3 nav-prev-slider">
                                            <span class="icon icon-arrow-right"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="container-full slider-layout-right">
                                <div class="swiper sw-wrapper-right tf-sw-product-sell-3 wrap-sw-over" data-preview="4.5" data-tablet="3.5" data-mobile="1.5" data-space-lg="30" data-space-md="15" data-pagination="1" data-pagination-md="3" data-pagination-lg="4">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" lazy="true" data-pg-name="Repeated Item">
                                            <div class="card-product style-line-hover">
                                                <div class="card-product-wrapper" data-pg-name="Gallery Wrapper" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'woocommerce_single' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"> <a href="product-detail.html" class="product-img" data-pg-name="Main image" wc-product-link> <img class="lazyload img-product" data-srcx="images/products/stakeboard-greenblack.jpg" src="images/products/stakeboard-greenblack.jpg" alt="image-product" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-yellowblack.jpg" src="images/products/stakeboard-yellowblack.jpg" alt="image-product" data-pg-name="Thumbnails Container" wp-call-function="esc_url( $hover_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                                    <div class="list-product-btn absolute-2"> <a href="#" class="box-icon bg_white quick-add tf-btn-loading quickaddlink" data-product-id="" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" onclick="quickadd()" wp-call-function-1="'quickadd(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a><a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" wp-call-function-1="'quickview(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                                    </div>
                                                    <div class="on-sale-wrap">
                                                        <div class="bg-danger on-sale-item rounded-0" wc-product-sale wc-product-sale-amount-sel="span" wc-product-sale-template-variant="discsingle">
                                                            Discount &nbsp;<span>20</span>%
                                                        </div>
                                                    </div>
                                                    <?php
if (!$product->is_in_stock()) {
    echo '<div class="countdown-box soldout">';
    echo '<div class="font-haas_mediu text-danger">SOLD OUT</div>';
    echo '</div>';
}
?>
                                                </div>
                                                <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu" wc-product-link><span wc-product-title wc-product-title-template-variant="single">Habitat Skateboards Ellipse Complete
                                                Skateboard</span></a> <span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template-variant="slidesingleprice"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mb-2 ">$9,999.00</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through ">
                                                            $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">
                                                            $9,999.00
</normal> </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide" lazy="true" cms-no-export>
                                            <div class="card-product style-line-hover">
                                                <div class="card-product-wrapper"> <a href="product-detail.html" class="product-img"> <img class="lazyload img-product" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> </a>
                                                    <div class="list-product-btn absolute-2"> <a href="#quick_add" data-product-id="666" data-bs-toggle="modal" data-bs-target="" class="box-icon bg_white quick-add tf-btn-loading"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a> <a href="javascript:void(0);" class="box-icon bg_white wishlist btn-icon-action"> <span class="icon icon-heart"></span> <span class="tooltip">Add to
                                                    Wishlist</span> <span class="icon icon-delete"></span> </a> <a href="#compare" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="box-icon bg_white compare btn-icon-action"> <span class="icon icon-compare"></span> <span class="tooltip">Add to
                                                    Compare</span> <span class="icon icon-check"></span> </a> <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                                    </div>
                                                </div>
                                                <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu">Skateboards
                                            Thrasher Complete Skateboard</a> <span class="price font-haas_mediu">$89.99</span>
                                                    <ul class="list-color-product">
                                                        <li class="list-color-item color-swatch active"> <span class="tooltip">Blue
                                                    Black</span> <span class="swatch-value blue-black bg-multiple-color"></span>
                                                            <img class="lazyload" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product">
                                                        </li>
                                                        <li class="list-color-item color-swatch"> <span class="tooltip">Blue
                                                    White</span> <span class="swatch-value blue-white bg-multiple-color"></span>
                                                            <img class="lazyload" data-srcx="images/products/stakeboard-bluewhite.jpg" src="images/products/stakeboard-bluewhite.jpg" alt="image-product">
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        <section class="flat-spacing-27 bg_grey-5 pt-0" wc-product-upsells wc-product-upsells-heading="[data-pg-name=&quot;Heading&quot;]" wc-product-upsells-item="[data-pg-name=&quot;Repeated Item&quot;]" wc-product-upsells-limit="10">
                            <div class="container">
                                <div class="flat-title title-upper flex-row justify-content-between px-0"><span class="title wow fadeInUp font-druk" data-wow-delay="0s" data-pg-name="Heading">SKATEBOARDS</span>
                                    <div class="box-sw-navigation">
                                        <div class="nav-sw round nav-next-product-4 nav-next-slider">
                                            <span class="icon icon-arrow-left"></span>
                                        </div>
                                        <div class="nav-sw round nav-prev-product-4 nav-prev-slider">
                                            <span class="icon icon-arrow-right"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="container-full slider-layout-right">
                                <div class="swiper sw-wrapper-right tf-sw-product-sell-4 wrap-sw-over" data-preview="4.5" data-tablet="3.5" data-mobile="1.5" data-space-lg="30" data-space-md="15" data-pagination="1" data-pagination-md="3" data-pagination-lg="4">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide" lazy="true" data-pg-name="Repeated Item">
                                            <div class="card-product style-line-hover">
                                                <div class="card-product-wrapper" data-pg-name="Gallery Wrapper" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'woocommerce_single' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"> <a href="product-detail.html" class="product-img" data-pg-name="Main image" wc-product-link> <img class="lazyload img-product" data-srcx="images/products/stakeboard-greenblack.jpg" src="images/products/stakeboard-greenblack.jpg" alt="image-product" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-yellowblack.jpg" src="images/products/stakeboard-yellowblack.jpg" alt="image-product" data-pg-name="Thumbnails Container" wp-call-function="esc_url( $hover_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                                    <div class="list-product-btn absolute-2"> <a href="#" class="box-icon bg_white quick-add tf-btn-loading quickaddlink" data-product-id="" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" onclick="quickadd()" wp-call-function-1="'quickadd(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a>
                                                        <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" wp-call-function-1="'quickview(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                                    </div>
                                                    <div class="on-sale-wrap">
                                                        <div class="bg-danger on-sale-item rounded-0" wc-product-sale wc-product-sale-amount-sel="span" wc-product-sale-template="use" wc-product-sale-template-variant="discsingle">
                                                            Discount &nbsp;<span>20</span>%
                                                        </div>
                                                    </div>
                                                    <?php
if (!$product->is_in_stock()) {
    echo '<div class="countdown-box soldout">';
    echo '<div class="font-haas_mediu text-danger">SOLD OUT</div>';
    echo '</div>';
}
?>
                                                </div>
                                                <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu" wc-product-link><span wc-product-title wc-product-title-template="use" wc-product-title-template-variant="single">Habitat Skateboards Ellipse Complete
                                                Skateboard</span></a> <span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template-variant="slidesingleprice" wc-product-price-template="use"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mb-2 ">$9,999.00</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through ">
                                                            $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">
                                                            $9,999.00
</normal> </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide" lazy="true" cms-no-export>
                                            <div class="card-product style-line-hover">
                                                <div class="card-product-wrapper"> <a href="product-detail.html" class="product-img"> <img class="lazyload img-product" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> </a>
                                                    <div class="list-product-btn absolute-2"> <a href="#quick_add" data-product-id="666" data-bs-toggle="modal" data-bs-target="" class="box-icon bg_white quick-add tf-btn-loading"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a> <a href="javascript:void(0);" class="box-icon bg_white wishlist btn-icon-action"> <span class="icon icon-heart"></span> <span class="tooltip">Add to
                                                    Wishlist</span> <span class="icon icon-delete"></span> </a> <a href="#compare" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="box-icon bg_white compare btn-icon-action"> <span class="icon icon-compare"></span> <span class="tooltip">Add to
                                                    Compare</span> <span class="icon icon-check"></span> </a> <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                                    </div>
                                                </div>
                                                <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu">Skateboards
                                            Thrasher Complete Skateboard</a> <span class="price font-haas_mediu">$89.99</span>
                                                    <ul class="list-color-product">
                                                        <li class="list-color-item color-swatch active"> <span class="tooltip">Blue
                                                    Black</span> <span class="swatch-value blue-black bg-multiple-color"></span>
                                                            <img class="lazyload" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product">
                                                        </li>
                                                        <li class="list-color-item color-swatch"> <span class="tooltip">Blue
                                                    White</span> <span class="swatch-value blue-white bg-multiple-color"></span>
                                                            <img class="lazyload" data-srcx="images/products/stakeboard-bluewhite.jpg" src="images/products/stakeboard-bluewhite.jpg" alt="image-product">
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </singleproduct>
                </div>
            </div>
        </wpcontent>
        <script type="text/javascript" src="js/bootstrap.min.js"></script>
        <script type="text/javascript" src="js/jquery.min.js"></script>
        <script type="text/javascript" src="js/swiper-bundle.min.js"></script>
        <script type="text/javascript" src="js/carousel.js"></script>
        <script type="text/javascript" src="js/bootstrap-select.min.js"></script>
        <script type="text/javascript" src="js/lazysize.min.js"></script>
        <script type="text/javascript" src="js/count-down.js"></script>
        <script type="text/javascript" src="js/wow.min.js"></script>
        <script type="text/javascript" src="js/multiple-modal.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
