/* Product Details Modal - Enhanced Styles */

/* <PERSON><PERSON> Styling */
.product-details-button-container {
    text-align: center;
    margin-bottom: 15px;
}

.product-details-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3) !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.product-details-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%) !important;
}

.product-details-btn:active {
    transform: translateY(0) !important;
}

.product-details-btn i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.product-details-btn:hover i {
    transform: scale(1.1);
}

/* Modal Base */
.product-modal {
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.product-modal[style*="flex"] {
    opacity: 1;
    visibility: visible;
}

.product-modal-content {
    background: #fff;
    border-radius: 12px;
    width: 90%;
    max-width: 950px;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    transform: translateY(30px) scale(0.95);
    transition: all 0.3s ease;
    position: relative;
}

.product-modal[style*="flex"] .product-modal-content {
    transform: translateY(0) scale(1);
}

/* Header */
.product-modal-header {
    padding: 25px 30px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.product-modal-header h3 {
    margin: 0;
    font-size: 1.5em;
    color: #2c3e50;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.product-modal-close {
    background: #fff;
    border: 1px solid #dee2e6;
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-modal-close:hover {
    background: #f8f9fa;
    color: #dc3545;
    transform: rotate(90deg);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Body */
.product-modal-body {
    padding: 25px 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.product-modal-body::-webkit-scrollbar {
    width: 8px;
}

.product-modal-body::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.product-modal-body::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
}

.product-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}

/* Product Items */
.product-details-list {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.product-detail-item {
    display: flex;
    gap: 25px;
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.product-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #dee2e6;
}

.product-image {
    flex-shrink: 0;
    width: 140px;
    position: relative;
    overflow: hidden;
    border-radius: 8px;
}

.product-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.product-image:hover img {
    transform: scale(1.05);
}

.product-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    pointer-events: none;
}

/* Product Info */
.product-info {
    flex: 1;
}

.product-name {
    margin: 0 0 15px 0;
    font-size: 1.2em;
    font-weight: 700;
    line-height: 1.3;
}

.product-name a {
    color: #2c3e50;
    text-decoration: none;
    transition: color 0.3s ease;
}

.product-name a:hover {
    color: #667eea;
}

.product-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
    padding: 15px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.product-meta > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.product-meta .label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9em;
}

.product-meta .value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 0.95em;
}

.product-attributes {
    margin: 15px 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    font-size: 0.9em;
    color: #6c757d;
    border-left: 3px solid #28a745;
}

.product-description {
    margin-top: 15px;
    padding: 12px;
    background: #fff3cd;
    border-radius: 6px;
    font-size: 0.9em;
    color: #856404;
    line-height: 1.5;
    border-left: 3px solid #ffc107;
}

/* Order Summary */
.order-summary {
    margin-top: 35px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.order-summary h4 {
    margin: 0 0 20px 0;
    font-size: 1.3em;
    color: white;
    text-align: center;
    font-weight: 700;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    border-top: 2px solid rgba(255, 255, 255, 0.3);
    margin-top: 15px;
    padding-top: 15px;
    font-weight: 700;
    font-size: 1.2em;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 15px 10px;
}

.summary-row .label {
    font-weight: 500;
}

.summary-row .value {
    font-weight: 700;
}

/* Footer */
.product-modal-footer {
    padding: 20px 30px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
    text-align: center;
}

.product-modal-footer .button {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.product-modal-footer .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5a6268 0%, #3d4245 100%);
}

/* Empty State */
.empty-cart {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-cart::before {
    content: '🛒';
    display: block;
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-modal-content {
        width: 95%;
        max-height: 90vh;
        border-radius: 8px;
    }
    
    .product-modal-header,
    .product-modal-body,
    .product-modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .product-detail-item {
        flex-direction: column;
        gap: 20px;
        padding: 15px;
    }
    
    .product-image {
        width: 100%;
        max-width: 200px;
        margin: 0 auto;
    }
    
    .product-meta {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .order-summary {
        margin-top: 25px;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .product-modal-header h3 {
        font-size: 1.2em;
    }
    
    .product-details-btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px !important;
    }
    
    .product-modal-body {
        padding: 20px 15px;
    }
    
    .product-detail-item {
        padding: 12px;
    }
    
    .order-summary {
        padding: 15px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-detail-item {
    animation: fadeInUp 0.5s ease forwards;
}

.product-detail-item:nth-child(2) {
    animation-delay: 0.1s;
}

.product-detail-item:nth-child(3) {
    animation-delay: 0.2s;
}

.product-detail-item:nth-child(4) {
    animation-delay: 0.3s;
}
