<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US" wp-template wp-template-export-as="index.php" wp-template-define-master-page="true">
    <head>
        <meta charset="utf-8">
        <title>Cablester</title>
        <meta name="author" content="rawstudio.id">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
        <!-- font -->
        <link rel="stylesheet" href="fonts/fonts.css">
        <link rel="stylesheet" href="fonts/font-icons.css">
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <link rel="stylesheet" href="css/drift-basic.min.css">
        <link rel="stylesheet" href="css/photoswipe.css">
        <link rel="stylesheet" href="css/swiper-bundle.min.css">
        <link rel="stylesheet" href="css/animate.css">
        <link rel="stylesheet" type="text/css" href="woocommerce.css"/>
        <link rel="stylesheet" type="text/css" href="css/styles.css"/>
        <!-- Favicon and Touch Icons  -->
        <link rel="shortcut icon" href="images/logo/favicon.png">
        <link rel="apple-touch-icon-precomposed" href="images/logo/favicon.png">
    </head>
    <body class="preload-wrapper popup-loader">
        <div id="ajax-loading-bar"></div>
        <wpheader cms-template-part="parts/header" cms-template-part-define>
            <header id="header" class="header-default header-style-3 header-absolute header-uppercase">
                <div class="container-full px_15 lg-px_40">
                    <div class="row wrapper-header align-items-center">
                        <div class="col-md-4 col-3 tf-lg-hidden">
                            <a href="#mobileMenu" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="16" viewBox="0 0 24 16" fill="none">
                                    <path d="M2.00056 2.28571H16.8577C17.1608 2.28571 17.4515 2.16531 17.6658 1.95098C17.8802 1.73665 18.0006 1.44596 18.0006 1.14286C18.0006 0.839753 17.8802 0.549063 17.6658 0.334735C17.4515 0.120408 17.1608 0 16.8577 0H2.00056C1.69745 0 1.40676 0.120408 1.19244 0.334735C0.978109 0.549063 0.857702 0.839753 0.857702 1.14286C0.857702 1.44596 0.978109 1.73665 1.19244 1.95098C1.40676 2.16531 1.69745 2.28571 2.00056 2.28571ZM0.857702 8C0.857702 7.6969 0.978109 7.40621 1.19244 7.19188C1.40676 6.97755 1.69745 6.85714 2.00056 6.85714H22.572C22.8751 6.85714 23.1658 6.97755 23.3801 7.19188C23.5944 7.40621 23.7148 7.6969 23.7148 8C23.7148 8.30311 23.5944 8.59379 23.3801 8.80812C23.1658 9.02245 22.8751 9.14286 22.572 9.14286H2.00056C1.69745 9.14286 1.40676 9.02245 1.19244 8.80812C0.978109 8.59379 0.857702 8.30311 0.857702 8ZM0.857702 14.8571C0.857702 14.554 0.978109 14.2633 1.19244 14.049C1.40676 13.8347 1.69745 13.7143 2.00056 13.7143H12.2863C12.5894 13.7143 12.8801 13.8347 13.0944 14.049C13.3087 14.2633 13.4291 14.554 13.4291 14.8571C13.4291 15.1602 13.3087 15.4509 13.0944 15.6653C12.8801 15.8796 12.5894 16 12.2863 16H2.00056C1.69745 16 1.40676 15.8796 1.19244 15.6653C0.978109 15.4509 0.857702 15.1602 0.857702 14.8571Z" fill="currentColor"></path>
                                </svg> </a>
                        </div>
                        <div class="col-xl-9 col-md-4 col-6">
                            <div class="wrap-header-left">
                                <a href="index.html" class="logo-header" cms-site-link="home"> <img src="images/logo/cablester1.jpg" alt="logo" class="logo" wp-call-function="get_field('dark_logo','option')" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                <nav class="box-navigation text-center">
                                    <ul class="box-nav-ul d-flex align-items-center justify-content-center gap-30 font-druk" wp-nav-menu="primary" wp-nav-menu-walker="new PrimaryMenu()" wp-nav-menu-class="box-nav-ul d-flex align-items-center justify-content-center gap-30 font-druk" wp-nav-menu-depth="4" wp-nav-menu-theme-location="primary">
                                        <li class="menu-items"> <a href="#" class="item-link">Home</a>
                                        </li>
                                        <li class="menu-items">
                                            <a href="#" class="item-link">Shop</a>
                                        </li>
                                        <li class="menu-items">
                                            <a href="#" class="item-link">Products</a>
                                        </li>
                                        <li class="menu-items position-relative"> <a href="#" class="item-link">Pages<i class="icon icon-arrow-down"></i></a>
                                            <div class="sub-menu submenu-default">
                                                <ul class="menu-list">
                                                    <li> <a href="about-us.html" class="menu-link-text link text_black-2">Menu</a>
                                                    </li>
                                                    <li class="menu-items-2"><a href="#" class="menu-link-text link text_black-2">Menu</a>
                                                        <div class="sub-menu submenu-default">
                                                            <ul class="menu-list">
                                                                <li> <a href="brands.html" class="menu-link-text link text_black-2 position-relative">Menu <div class="demo-label">
                                                                            <span class="demo-new">New</span>
                                                                        </div> </a>
                                                                </li>
                                                                <li>
                                                                    <a href="brands-v2.html" class="menu-link-text link text_black-2">Menu</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                    <li class="menu-items-2"> <a href="#" class="menu-link-text link text_black-2">Menu</a>
                                                        <div class="sub-menu submenu-default">
                                                            <ul class="menu-list">
                                                                <li>
                                                                    <a href="contact-1.html" class="menu-link-text link text_black-2">Menu</a>
                                                                </li>
                                                                <li>
                                                                    <a href="contact-2.html" class="menu-link-text link text_black-2">Menu</a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </li>
                                        <li class="menu-items position-relative">
                                            <a href="#" class="item-link">Blog</a>
                                        </li>
                                        <li class="menu-items"> <a href="https://themeforest.net/item/ecomus-ultimate-html5-template/53417990?s_rank=3" class="item-link">Book Review</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-4 col-3">
                            <ul class="nav-icon d-flex justify-content-end align-items-center gap-20">
                                <li class="nav-search"><a href="#canvasSearch" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="nav-icon-item"><i class="icon icon-search"></i></a>
                                </li>
                                <li class="nav-account"><a href="#login" class="nav-icon-item" wp-call-function="$page_id = wc_get_page_id('myaccount');

if ($page_id) {
    $page_url = get_permalink($page_id);
    echo esc_url($page_url);
} else {
    echo 'page is not set.';
}" wp-call-function-set="attr" wp-call-function-set-attr="href"><i class="icon icon-account"></i></a>
                                </li>
                                <li class="nav-cart"><a href="#shoppingCart" data-bs-toggle="modal" class="nav-icon-item"><i class="icon icon-bag"></i><span class="count-box bg_dark" wp-call-function="if ( function_exists( 'WC' ) && WC()->cart ) {
    // Get the number of items in the cart
    $cart_count = WC()->cart->get_cart_contents_count();
    echo esc_html( $cart_count );
}" wp-call-function-set="content">2</span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>
            <div class="preload preload-container">
                <div class="preload-logo">
                    <div class="spinner"></div>
                </div>
            </div>
        </wpheader>
        <wpcontent wp-site-content>
            <div id="wrapper">
                <!-- Header -->
                <!-- /Header -->
                <!-- Slider -->
                <div class="tf-slideshow slider-effect-fade position-relative slider-skincare slider-skateboard">
                    <div class="swiper tf-sw-slideshow" data-preview="1" data-tablet="1" data-mobile="1" data-centered="false" data-space="0" data-loop="true" data-auto-play="true" data-delay="0" data-speed="1000">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide" data-pg-name="Items Container">
                                <div class="wrap-slider">
                                    <img src="images/slider/skateboard-slider-01.jpg" alt="fashion-slideshow" wp-call-function="get_field('section_1', 'option')['slider'][0]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    <div class="box-content">
                                        <div class="container">
                                            <div class="card-box-2">
                                                <div class="fade-item fade-item-1 title">
                                                    <a href="product-detail.html" class="link font-haas_mediu" wp-call-function="$featured_posts = get_field('section_1', 'option')['featured_product'];
$product_id = $featured_posts[0]->ID;
$product = wc_get_product($product_id);

if ($product) {
    $regular_price =wc_price($product->get_regular_price());
    $sale_price =wc_price($product->get_sale_price());
    $price =wc_price($product->get_price());
    $product_url = get_permalink($product_id);
    $product_title = $product->get_name();
}" wp-call-function-set="before" wp-call-function-1="$product_title" wp-call-function-1-set="content" wp-call-function-echo-1="true" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href">Metode Jakarta</a>
                                                </div>                                                 <span class="fade-item fade-item-2 price font-haas_mediu" wp-call-function="$price" wp-call-function-echo="true" wp-call-function-set="content">$279.99</span>
                                                <div class="fade-item fade-item-3"> <a href="product-detail.html" class="tf-btn btn-line collection-other-link fw-6 font-haas_mediu" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href"><span>Shop
                                                        now</span><i class="icon icon-arrow1-top-left"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="wrap-slider">
                                    <img src="images/slider/skateboard-slider-02.jpg" alt="fashion-slideshow" wp-call-function="get_field('section_1', 'option')['slider'][1]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    <div class="box-content">
                                        <div class="container">
                                            <div class="card-box-2">
                                                <div class="fade-item fade-item-1 title"><a href="product-detail.html" class="link font-haas_mediu" wp-call-function="$featured_posts = get_field('section_1', 'option')['featured_product'];
$product_id = $featured_posts[1]->ID;
$product = wc_get_product($product_id);

if ($product) {
    $regular_price =wc_price($product->get_regular_price());
    $sale_price =wc_price($product->get_sale_price());
    $price =wc_price($product->get_price());
    $product_url = get_permalink($product_id);
    $product_title = $product->get_name();
}" wp-call-function-set="before" wp-call-function-1="$product_title" wp-call-function-1-set="content" wp-call-function-echo-1="true" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href">Metode Jakarta</a>
                                                </div>                                                 <span class="fade-item fade-item-2 price font-haas_mediu" wp-call-function="$price" wp-call-function-echo="true" wp-call-function-set="content">$279.99</span>
                                                <div class="fade-item fade-item-3"> <a href="product-detail.html" class="tf-btn btn-line collection-other-link fw-6 font-haas_mediu" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href"><span>Shop
                                                        now</span><i class="icon icon-arrow1-top-left"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="wrap-slider">
                                    <img src="images/slider/skateboard-slider-03.jpg" alt="fashion-slideshow" wp-call-function="get_field('section_1', 'option')['slider'][2]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    <div class="box-content">
                                        <div class="container">
                                            <div class="card-box-2">
                                                <div class="fade-item fade-item-1 title"><a href="product-detail.html" class="link font-haas_mediu" wp-call-function="$featured_posts = get_field('section_1', 'option')['featured_product'];
$product_id = $featured_posts[2]->ID;
$product = wc_get_product($product_id);

if ($product) {
    $regular_price =wc_price($product->get_regular_price());
    $sale_price =wc_price($product->get_sale_price());
    $price =wc_price($product->get_price());
    $product_url = get_permalink($product_id);
    $product_title = $product->get_name();
}" wp-call-function-set="before" wp-call-function-1="$product_title" wp-call-function-1-set="content" wp-call-function-echo-1="true" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href">Metode Jakarta</a>
                                                </div>                                                 <span class="fade-item fade-item-2 price font-haas_mediu" wp-call-function="$price" wp-call-function-echo="true" wp-call-function-set="content">$279.99</span>
                                                <div class="fade-item fade-item-3"> <a href="product-detail.html" class="tf-btn btn-line collection-other-link fw-6 font-haas_mediu" wp-call-function-2="$product_url" wp-call-function-echo-2="true" wp-call-function-2-set="href"><span>Shop
                                                        now</span><i class="icon icon-arrow1-top-left"></i></a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="wrap-pagination sw-absolute-3">
                        <div class="sw-dots style-2 sw-pagination-slider justify-content-center"></div>
                    </div>
                </div>
                <!-- /Slider -->
                <!-- Marquee -->
                <section class="flat-spacing-3 line">
                    <div class="tf-marquee type-big">
                        <div class="wrap-marquee speed-1">
                            <div class="marquee-item">
                                <div class="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="44" viewBox="0 0 41 44" fill="none">
                                        <path d="M39.8055 21.092C35.4409 21.0775 30.8593 20.1853 27.411 17.3399C24.5125 14.9478 22.7908 11.5526 21.9565 7.93551C21.4356 5.67845 21.2475 3.36835 21.262 1.05825C21.262 0.575971 20.8762 0.36377 20.5 0.36377C20.1238 0.36377 19.738 0.575971 19.738 1.05825C19.7573 4.99845 19.2027 9.07368 17.3652 12.6136C15.6145 15.9847 12.7305 18.5359 9.15686 19.8333C6.60562 20.7592 3.89523 21.0872 1.19448 21.0968C0.712202 21.0968 0.5 21.4875 0.5 21.8636C0.5 22.2398 0.712202 22.6305 1.19448 22.6305C5.55908 22.6449 10.1407 23.5371 13.589 26.3826C16.4875 28.7747 18.2092 32.1699 19.0435 35.787C19.5644 38.044 19.7476 40.3541 19.738 42.6642C19.738 43.1465 20.1238 43.3587 20.5 43.3587C20.8762 43.3587 21.262 43.1465 21.262 42.6642C21.2427 38.724 21.7973 34.6488 23.6348 31.1089C25.3855 27.7378 28.2695 25.1865 31.8431 23.8892C34.3944 22.9632 37.1048 22.6353 39.8055 22.6256C40.2878 22.6256 40.5 22.235 40.5 21.8588C40.5 21.4826 40.2878 21.092 39.8055 21.092ZM26.5381 25.2299C23.3985 27.7522 21.4838 31.4561 20.5772 35.3288C20.5482 35.4445 20.5289 35.5651 20.5 35.6809C20.0901 33.8193 19.4727 32.0107 18.5854 30.318C16.6659 26.6527 13.4829 23.9423 9.6102 22.5195C8.87714 22.2495 8.12479 22.0324 7.36279 21.854C9.92368 21.2415 12.3544 20.1757 14.4571 18.4877C17.5967 15.9654 19.5113 12.2615 20.418 8.38885C20.447 8.2731 20.4662 8.15253 20.4952 8.03679C20.9051 9.89837 21.5224 11.7069 22.4098 13.3997C24.3293 17.065 27.5123 19.7754 31.385 21.1981C32.118 21.4682 32.8704 21.6852 33.6324 21.8636C31.0715 22.481 28.6408 23.542 26.5381 25.2299Z" fill="black"></path>
                                    </svg>
                                </div>
                                <p class="text fw-8 font-druk" wp-call-function="$featured_posts = get_field('section_2', 'option')['running_text'];
" wp-call-function-echo="true" wp-call-function-set="content">FREE UK DELIVERY AND RETURNS ON ALL ORDERS</p>
                            </div>
                            <div class="marquee-item">
                                <div class="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="44" viewBox="0 0 41 44" fill="none">
                                        <path d="M39.8055 21.092C35.4409 21.0775 30.8593 20.1853 27.411 17.3399C24.5125 14.9478 22.7908 11.5526 21.9565 7.93551C21.4356 5.67845 21.2475 3.36835 21.262 1.05825C21.262 0.575971 20.8762 0.36377 20.5 0.36377C20.1238 0.36377 19.738 0.575971 19.738 1.05825C19.7573 4.99845 19.2027 9.07368 17.3652 12.6136C15.6145 15.9847 12.7305 18.5359 9.15686 19.8333C6.60562 20.7592 3.89523 21.0872 1.19448 21.0968C0.712202 21.0968 0.5 21.4875 0.5 21.8636C0.5 22.2398 0.712202 22.6305 1.19448 22.6305C5.55908 22.6449 10.1407 23.5371 13.589 26.3826C16.4875 28.7747 18.2092 32.1699 19.0435 35.787C19.5644 38.044 19.7476 40.3541 19.738 42.6642C19.738 43.1465 20.1238 43.3587 20.5 43.3587C20.8762 43.3587 21.262 43.1465 21.262 42.6642C21.2427 38.724 21.7973 34.6488 23.6348 31.1089C25.3855 27.7378 28.2695 25.1865 31.8431 23.8892C34.3944 22.9632 37.1048 22.6353 39.8055 22.6256C40.2878 22.6256 40.5 22.235 40.5 21.8588C40.5 21.4826 40.2878 21.092 39.8055 21.092ZM26.5381 25.2299C23.3985 27.7522 21.4838 31.4561 20.5772 35.3288C20.5482 35.4445 20.5289 35.5651 20.5 35.6809C20.0901 33.8193 19.4727 32.0107 18.5854 30.318C16.6659 26.6527 13.4829 23.9423 9.6102 22.5195C8.87714 22.2495 8.12479 22.0324 7.36279 21.854C9.92368 21.2415 12.3544 20.1757 14.4571 18.4877C17.5967 15.9654 19.5113 12.2615 20.418 8.38885C20.447 8.2731 20.4662 8.15253 20.4952 8.03679C20.9051 9.89837 21.5224 11.7069 22.4098 13.3997C24.3293 17.065 27.5123 19.7754 31.385 21.1981C32.118 21.4682 32.8704 21.6852 33.6324 21.8636C31.0715 22.481 28.6408 23.542 26.5381 25.2299Z" fill="black"></path>
                                    </svg>
                                </div>
                                <p class="text fw-8 font-druk" wp-call-function="$featured_posts = get_field('section_2', 'option')['running_text'];
" wp-call-function-echo="true" wp-call-function-set="content">FREE UK DELIVERY AND RETURNS ON ALL ORDERS</p>
                            </div>
                            <div class="marquee-item">
                                <div class="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="44" viewBox="0 0 41 44" fill="none">
                                        <path d="M39.8055 21.092C35.4409 21.0775 30.8593 20.1853 27.411 17.3399C24.5125 14.9478 22.7908 11.5526 21.9565 7.93551C21.4356 5.67845 21.2475 3.36835 21.262 1.05825C21.262 0.575971 20.8762 0.36377 20.5 0.36377C20.1238 0.36377 19.738 0.575971 19.738 1.05825C19.7573 4.99845 19.2027 9.07368 17.3652 12.6136C15.6145 15.9847 12.7305 18.5359 9.15686 19.8333C6.60562 20.7592 3.89523 21.0872 1.19448 21.0968C0.712202 21.0968 0.5 21.4875 0.5 21.8636C0.5 22.2398 0.712202 22.6305 1.19448 22.6305C5.55908 22.6449 10.1407 23.5371 13.589 26.3826C16.4875 28.7747 18.2092 32.1699 19.0435 35.787C19.5644 38.044 19.7476 40.3541 19.738 42.6642C19.738 43.1465 20.1238 43.3587 20.5 43.3587C20.8762 43.3587 21.262 43.1465 21.262 42.6642C21.2427 38.724 21.7973 34.6488 23.6348 31.1089C25.3855 27.7378 28.2695 25.1865 31.8431 23.8892C34.3944 22.9632 37.1048 22.6353 39.8055 22.6256C40.2878 22.6256 40.5 22.235 40.5 21.8588C40.5 21.4826 40.2878 21.092 39.8055 21.092ZM26.5381 25.2299C23.3985 27.7522 21.4838 31.4561 20.5772 35.3288C20.5482 35.4445 20.5289 35.5651 20.5 35.6809C20.0901 33.8193 19.4727 32.0107 18.5854 30.318C16.6659 26.6527 13.4829 23.9423 9.6102 22.5195C8.87714 22.2495 8.12479 22.0324 7.36279 21.854C9.92368 21.2415 12.3544 20.1757 14.4571 18.4877C17.5967 15.9654 19.5113 12.2615 20.418 8.38885C20.447 8.2731 20.4662 8.15253 20.4952 8.03679C20.9051 9.89837 21.5224 11.7069 22.4098 13.3997C24.3293 17.065 27.5123 19.7754 31.385 21.1981C32.118 21.4682 32.8704 21.6852 33.6324 21.8636C31.0715 22.481 28.6408 23.542 26.5381 25.2299Z" fill="black"></path>
                                    </svg>
                                </div>
                                <p class="text fw-8 font-druk" wp-call-function="$featured_posts = get_field('section_2', 'option')['running_text'];
" wp-call-function-echo="true" wp-call-function-set="content">FREE UK DELIVERY AND RETURNS ON ALL ORDERS</p>
                            </div>
                            <div class="marquee-item">
                                <div class="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="44" viewBox="0 0 41 44" fill="none">
                                        <path d="M39.8055 21.092C35.4409 21.0775 30.8593 20.1853 27.411 17.3399C24.5125 14.9478 22.7908 11.5526 21.9565 7.93551C21.4356 5.67845 21.2475 3.36835 21.262 1.05825C21.262 0.575971 20.8762 0.36377 20.5 0.36377C20.1238 0.36377 19.738 0.575971 19.738 1.05825C19.7573 4.99845 19.2027 9.07368 17.3652 12.6136C15.6145 15.9847 12.7305 18.5359 9.15686 19.8333C6.60562 20.7592 3.89523 21.0872 1.19448 21.0968C0.712202 21.0968 0.5 21.4875 0.5 21.8636C0.5 22.2398 0.712202 22.6305 1.19448 22.6305C5.55908 22.6449 10.1407 23.5371 13.589 26.3826C16.4875 28.7747 18.2092 32.1699 19.0435 35.787C19.5644 38.044 19.7476 40.3541 19.738 42.6642C19.738 43.1465 20.1238 43.3587 20.5 43.3587C20.8762 43.3587 21.262 43.1465 21.262 42.6642C21.2427 38.724 21.7973 34.6488 23.6348 31.1089C25.3855 27.7378 28.2695 25.1865 31.8431 23.8892C34.3944 22.9632 37.1048 22.6353 39.8055 22.6256C40.2878 22.6256 40.5 22.235 40.5 21.8588C40.5 21.4826 40.2878 21.092 39.8055 21.092ZM26.5381 25.2299C23.3985 27.7522 21.4838 31.4561 20.5772 35.3288C20.5482 35.4445 20.5289 35.5651 20.5 35.6809C20.0901 33.8193 19.4727 32.0107 18.5854 30.318C16.6659 26.6527 13.4829 23.9423 9.6102 22.5195C8.87714 22.2495 8.12479 22.0324 7.36279 21.854C9.92368 21.2415 12.3544 20.1757 14.4571 18.4877C17.5967 15.9654 19.5113 12.2615 20.418 8.38885C20.447 8.2731 20.4662 8.15253 20.4952 8.03679C20.9051 9.89837 21.5224 11.7069 22.4098 13.3997C24.3293 17.065 27.5123 19.7754 31.385 21.1981C32.118 21.4682 32.8704 21.6852 33.6324 21.8636C31.0715 22.481 28.6408 23.542 26.5381 25.2299Z" fill="black"></path>
                                    </svg>
                                </div>
                                <p class="text fw-8 font-druk" wp-call-function="$featured_posts = get_field('section_2', 'option')['running_text'];
" wp-call-function-echo="true" wp-call-function-set="content">FREE UK DELIVERY AND RETURNS ON ALL ORDERS</p>
                            </div>
                            <div class="marquee-item">
                                <div class="icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="41" height="44" viewBox="0 0 41 44" fill="none">
                                        <path d="M39.8055 21.092C35.4409 21.0775 30.8593 20.1853 27.411 17.3399C24.5125 14.9478 22.7908 11.5526 21.9565 7.93551C21.4356 5.67845 21.2475 3.36835 21.262 1.05825C21.262 0.575971 20.8762 0.36377 20.5 0.36377C20.1238 0.36377 19.738 0.575971 19.738 1.05825C19.7573 4.99845 19.2027 9.07368 17.3652 12.6136C15.6145 15.9847 12.7305 18.5359 9.15686 19.8333C6.60562 20.7592 3.89523 21.0872 1.19448 21.0968C0.712202 21.0968 0.5 21.4875 0.5 21.8636C0.5 22.2398 0.712202 22.6305 1.19448 22.6305C5.55908 22.6449 10.1407 23.5371 13.589 26.3826C16.4875 28.7747 18.2092 32.1699 19.0435 35.787C19.5644 38.044 19.7476 40.3541 19.738 42.6642C19.738 43.1465 20.1238 43.3587 20.5 43.3587C20.8762 43.3587 21.262 43.1465 21.262 42.6642C21.2427 38.724 21.7973 34.6488 23.6348 31.1089C25.3855 27.7378 28.2695 25.1865 31.8431 23.8892C34.3944 22.9632 37.1048 22.6353 39.8055 22.6256C40.2878 22.6256 40.5 22.235 40.5 21.8588C40.5 21.4826 40.2878 21.092 39.8055 21.092ZM26.5381 25.2299C23.3985 27.7522 21.4838 31.4561 20.5772 35.3288C20.5482 35.4445 20.5289 35.5651 20.5 35.6809C20.0901 33.8193 19.4727 32.0107 18.5854 30.318C16.6659 26.6527 13.4829 23.9423 9.6102 22.5195C8.87714 22.2495 8.12479 22.0324 7.36279 21.854C9.92368 21.2415 12.3544 20.1757 14.4571 18.4877C17.5967 15.9654 19.5113 12.2615 20.418 8.38885C20.447 8.2731 20.4662 8.15253 20.4952 8.03679C20.9051 9.89837 21.5224 11.7069 22.4098 13.3997C24.3293 17.065 27.5123 19.7754 31.385 21.1981C32.118 21.4682 32.8704 21.6852 33.6324 21.8636C31.0715 22.481 28.6408 23.542 26.5381 25.2299Z" fill="black"></path>
                                    </svg>
                                </div>
                                <p class="text fw-8 font-druk" wp-call-function="$featured_posts = get_field('section_2', 'option')['running_text'];
" wp-call-function-echo="true" wp-call-function-set="content">FREE UK DELIVERY AND RETURNS ON ALL ORDERS</p>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Marquee -->
                <!-- Collection -->
                <section class="flat-spacing-18 pb-0">
                    <div class="container">
                        <div class="grid-3-layout-md wow fadeInUp" data-wow-delay="0s">
                            <div class="item-1 collection-line-upper hover-img" wp-call-function="$collection = get_field('section_3', 'option')['collection'];
if( is_array($collection) && !empty($collection) ) :
    $first_row = $collection[0];
    $collection_name = $first_row['collection_name'];
    $promo_text = $first_row['promo_text'];
    $image = $first_row['image'];
    $link_to = $first_row['link_to']['url'];
endif;" wp-call-function-set="before">
                                <div class="collection-inner"><a href="shop-collection-sub.html" class="collection-image img-style" wp-call-function="$link_to" wp-call-function-echo="true" wp-call-function-set="href"> <img class="lazyload" data-srcx="images/collections/collection-stakeboard1.jpg" src="./images/collections/collection-stakeboard1.jpg" alt="collection-img" wp-call-function="$image" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                    <div class="collection-content text-center">
                                        <p class="subheading font-haas_mediu" wp-call-function="$promo_text" wp-call-function-echo="true" wp-call-function-set="content">UP TO 30% OFF</p>
                                        <h5 class="heading font-haas_black" wp-call-function-1="$collection_name" wp-call-function-1-set="content" wp-call-function-echo-1="true">MARJIN KIRI</h5>
                                        <a href="shop-collection-sub.html" class="tf-btn btn-fill animate-hover-btn collection-title font-haas_mediu" wp-call-function="$link_to;" wp-call-function-set="href" wp-call-function-echo="true"><span>SHOP
                                            NOW</span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="item-1 collection-line-upper hover-img" wp-call-function="$collection = get_field('section_3', 'option')['collection'];
if( is_array($collection) && !empty($collection) ) :
    $first_row = $collection[1];
    $collection_name = $first_row['collection_name'];
    $promo_text = $first_row['promo_text'];
    $image = $first_row['image'];
    $link_to = $first_row['link_to']['url'];
endif;" wp-call-function-set="before">
                                <div class="collection-inner"><a href="shop-collection-sub.html" class="collection-image img-style" wp-call-function="$link_to" wp-call-function-echo="true" wp-call-function-set="href"> <img class="lazyload" data-srcx="images/collections/collection-stakeboard1.jpg" src="./images/collections/collection-stakeboard1.jpg" alt="collection-img" wp-call-function="$image" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                    <div class="collection-content text-center">
                                        <p class="subheading font-haas_mediu" wp-call-function="$promo_text" wp-call-function-echo="true" wp-call-function-set="content">UP TO 30% OFF</p>
                                        <h5 class="heading font-haas_black" wp-call-function-1="$collection_name" wp-call-function-1-set="content" wp-call-function-echo-1="true">MARJIN KIRI</h5>
                                        <a href="shop-collection-sub.html" class="tf-btn btn-fill animate-hover-btn collection-title font-haas_mediu" wp-call-function="$link_to;" wp-call-function-set="href" wp-call-function-echo="true"><span>SHOP
                                            NOW</span></a>
                                    </div>
                                </div>
                            </div>
                            <div class="item-1 collection-line-upper hover-img" wp-call-function="$collection = get_field('section_3', 'option')['collection'];
if( is_array($collection) && !empty($collection) ) :
    $first_row = $collection[2];
    $collection_name = $first_row['collection_name'];
    $promo_text = $first_row['promo_text'];
    $image = $first_row['image'];
    $link_to = $first_row['link_to']['url'];
endif;" wp-call-function-set="before">
                                <div class="collection-inner"><a href="shop-collection-sub.html" class="collection-image img-style" wp-call-function="$link_to" wp-call-function-echo="true" wp-call-function-set="href"> <img class="lazyload" data-srcx="images/collections/collection-stakeboard1.jpg" src="./images/collections/collection-stakeboard1.jpg" alt="collection-img" wp-call-function="$image" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                    <div class="collection-content text-center">
                                        <p class="subheading font-haas_mediu" wp-call-function="$promo_text" wp-call-function-echo="true" wp-call-function-set="content">UP TO 30% OFF</p>
                                        <h5 class="heading font-haas_black" wp-call-function-1="$collection_name" wp-call-function-1-set="content" wp-call-function-echo-1="true">MARJIN KIRI</h5>
                                        <a href="shop-collection-sub.html" class="tf-btn btn-fill animate-hover-btn collection-title font-haas_mediu" wp-call-function="$link_to;" wp-call-function-set="href" wp-call-function-echo="true"><span>SHOP
                                            NOW</span></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Collection -->
                <!-- Brand -->
                <section class="flat-spacing-9">
                    <div class="container">
                        <div class="swiper tf-sw-brand" data-loop="false" data-play="false" data-preview="6" data-tablet="3" data-mobile="2" data-space-lg="0" data-space-md="0">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][0]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][1]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][2]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][3]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][4]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                                <div class="swiper-slide">
                                    <div class="brand-item">
                                        <img class="lazyload" data-srcx="images/brand/brand-01.png" src="images/brand/brand-01.png" alt="image-brand" wp-call-function="get_field('section_3', 'option')['featured_brand'][5]['url'];" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="sw-dots style-2 sw-pagination-brand justify-content-center"></div>
                    </div>
                </section>
                <!-- /Brand -->
                <!-- Categories -->
                <section class="flat-spacing-17 pt_0">
                    <div class="container">
                        <div class="flat-title title-upper flex-row justify-content-between px-0"><span class="title wow fadeInUp font-druk" data-wow-delay="0s" wp-call-function="get_field('section_4', 'option')['title_shop_by_categories']" wp-call-function-echo="true" wp-call-function-set="content">SHOP BY CATEGORIES</span>
                            <div class="box-sw-navigation">
                                <div class="nav-sw round nav-next-collection nav-next-slider"><span class="icon icon-arrow-left"></span>
                                </div>
                                <div class="nav-sw round nav-prev-collection nav-prev-slider"><span class="icon icon-arrow-right"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-full slider-layout-right">
                        <div class="swiper tf-sw-collection sw-wrapper-right" data-preview="4.6" data-tablet="3.6" data-mobile="1.6" data-space-lg="30" data-space-md="30" data-space="15" data-loop="false" data-auto-play="false">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" lazy="true" wc-cats wc-cats-mode="list" wc-cats-list="is_array(get_field('section_4', 'option')['shop_by_categories']) ? implode(', ', array_map(function($item) { return $item->slug; }, get_field('section_4', 'option')['shop_by_categories'])) : 'uncategorized'" wc-cats-range="0-5">
                                    <div class="collection-item-v3 hover-img"> <a href="shop-collection-sub.html" class="collection-image img-style rounded-0" wc-cat-link> <img class="lazyload" data-srcx="images/collections/categories-stakeboard1.jpg" src="images/collections/categories-stakeboard1.jpg" alt="collection-img" wc-cat-image> <span class="box-icon"> <i class="icon-icon icon-arrow1-top-left"></i> </span> </a>
                                        <div class="collection-content"> <a href="shop-collection-sub.html" class="link title fw-8 fs-16 text-uppercase font-haas_mediu" wc-cat-link wc-cat-name>SKATEBOARDS</a>
                                            <div class="count font-haas_mediu" wc-cat-count wc-cat-count-text="[data-pg-name=&quot;Show Count Text&quot;]" cms-no-export>
                                                <span data-pg-name="Show Count Text">8</span> items
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="swiper-slide" lazy="true" cms-no-export>
                                    <div class="collection-item-v3 hover-img"> <a href="shop-collection-sub.html" class="collection-image img-style rounded-0"> <img class="lazyload" data-srcx="images/collections/categories-stakeboard2.jpg" src="images/collections/categories-stakeboard2.jpg" alt="collection-img"> <span class="box-icon"> <i class="icon-icon icon-arrow1-top-left"></i> </span> </a>
                                        <div class="collection-content"> <a href="shop-collection-sub.html" class="link title fw-8 fs-16 text-uppercase font-haas_mediu">ACCESSORIES</a>
                                            <div class="count font-haas_mediu">8 items</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Categories -->
                <!-- Skateboards -->
                <section class="flat-spacing-27 bg_grey-5">
                    <div class="container">
                        <div class="flat-title title-upper flex-row justify-content-between px-0">
                            <span class="title wow fadeInUp font-druk" data-wow-delay="0s" wp-call-function="get_field('section_4', 'option')['title_featured_category_1']" wp-call-function-echo="true" wp-call-function-set="content">book</span>
                            <div class="box-sw-navigation">
                                <div class="nav-sw round nav-next-product nav-next-slider"><span class="icon icon-arrow-left"></span>
                                </div>
                                <div class="nav-sw round nav-prev-product nav-prev-slider"><span class="icon icon-arrow-right"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-full slider-layout-right">
                        <div class="swiper sw-wrapper-right tf-sw-product-sell wrap-sw-over" data-preview="4.5" data-tablet="3.5" data-mobile="1.5" data-space-lg="30" data-space-md="15" data-pagination="1" data-pagination-md="3" data-pagination-lg="4">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" lazy="true" wc-show-products="query" wc-show-products-mode="freestyle" wc-show-products-type-query-name="product_1" wc-show-products-categories="is_array(get_field('section_4', 'option')['featured_category_1']) ? implode(', ', array_map(function($item) { return $item->slug; }, get_field('section_4', 'option')['featured_category_1'])) : 'uncategorized'" wc-show-products-range="0-5">
                                    <div class="card-product style-line-hover">
                                        <div class="card-product-wrapper" data-pg-name="Gallery Wrapper" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'woocommerce_single' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"> <a href="product-detail.html" class="product-img" data-pg-name="Main image" wc-product-link> <img class="lazyload img-product" data-srcx="images/products/stakeboard-greenblack.jpg" src="images/products/stakeboard-greenblack.jpg" alt="image-product" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-yellowblack.jpg" src="images/products/stakeboard-yellowblack.jpg" alt="image-product" data-pg-name="Thumbnails Container" wp-call-function="esc_url( $hover_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                            <div class="list-product-btn absolute-2"> <a href="#" class="box-icon bg_white quick-add tf-btn-loading quickaddlink" data-product-id="" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" onclick="quickadd()" wp-call-function-1="'quickadd(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a>
                                                <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" wp-call-function-1="'quickview(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                            </div>
                                            <div class="on-sale-wrap">
                                                <div class="bg-danger on-sale-item rounded-0" wc-product-sale wc-product-sale-amount-sel="span" wc-product-sale-template-variant="home">
                                                    Discount &nbsp;<span>20</span>%
                                                </div>
                                            </div>
                                            <?php
if (!$product->is_in_stock()) {
    echo '<div class="countdown-box soldout">';
    echo '<div class="font-haas_mediu text-danger">SOLD OUT</div>';
    echo '</div>';
}
?>
                                        </div>
                                        <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu" wc-product-link><span wc-product-title wc-product-title-template-variant="home">Habitat Skateboards Ellipse Complete
                                                Skateboard</span></a> <span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template-variant="home"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mb-2 ">$9,999.00</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through ">
                                                    $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">
                                                    $9,999.00
</normal> </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="swiper-slide" lazy="true" cms-no-export>
                                    <div class="card-product style-line-hover">
                                        <div class="card-product-wrapper"> <a href="product-detail.html" class="product-img"> <img class="lazyload img-product" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> </a>
                                            <div class="list-product-btn absolute-2"> <a href="#quick_add" data-product-id="666" data-bs-toggle="modal" data-bs-target="" class="box-icon bg_white quick-add tf-btn-loading"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a> <a href="javascript:void(0);" class="box-icon bg_white wishlist btn-icon-action"> <span class="icon icon-heart"></span> <span class="tooltip">Add to
                                                    Wishlist</span> <span class="icon icon-delete"></span> </a> <a href="#compare" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="box-icon bg_white compare btn-icon-action"> <span class="icon icon-compare"></span> <span class="tooltip">Add to
                                                    Compare</span> <span class="icon icon-check"></span> </a> <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                            </div>
                                        </div>
                                        <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu">Skateboards
                                            Thrasher Complete Skateboard</a> <span class="price font-haas_mediu">$89.99</span>
                                            <ul class="list-color-product">
                                                <li class="list-color-item color-swatch active"> <span class="tooltip">Blue
                                                    Black</span> <span class="swatch-value blue-black bg-multiple-color"></span>
                                                    <img class="lazyload" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product">
                                                </li>
                                                <li class="list-color-item color-swatch"> <span class="tooltip">Blue
                                                    White</span> <span class="swatch-value blue-white bg-multiple-color"></span>
                                                    <img class="lazyload" data-srcx="images/products/stakeboard-bluewhite.jpg" src="images/products/stakeboard-bluewhite.jpg" alt="image-product">
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Skateboards -->
                <!-- Banner collection -->
                <section class="flat-spacing-12">
                    <div class="container">
                        <div class="bg_dark md-col-2 rounded-0 style-3 text_white tf-grid-layout tf-img-with-text">
                            <div class="tf-content-wrap w-100 wow fadeInUp" data-wow-delay="0s">
                                <div class="inner">
                                    <div class="heading text-uppercase fw-8 font-druk" wp-call-function="get_field('section_4', 'option')['title_promo']" wp-call-function-echo="true" wp-call-function-set="content">CUSTOM <br> merchandise
                                    </div>
                                    <p class="description font-haas_mediu" wp-call-function="get_field('section_4', 'option')['subtitle_promo']" wp-call-function-echo="true" wp-call-function-set="content">Shop our luxury silk button-up blouses made with
                                    ultra-soft, washable silk.</p>
                                    <a href="shop-collection-list.html" class="btn-line collection-other-link font-haas_mediu fw-6 text_white tf-btn" wp-call-function="get_field('section_4', 'option')['link_to_promo']['url']" wp-call-function-echo="true" wp-call-function-set="href"><span wp-call-function="get_field('section_4', 'option')['link_to_promo']['title']" wp-call-function-echo="true" wp-call-function-set="content">Shop
                                        Collection</span><i class="icon icon-arrow1-top-left"></i></a>
                                </div>
                            </div>
                            <div class="tf-image-wrap wow fadeInUp" data-wow-delay="0s">
                                <img class="lazyload" data-srcx="images/collections/banner-wtext-stakeboard.jpg" src="images/collections/banner-wtext-stakeboard.jpg" alt="collection-img" wp-call-function="get_field('section_4', 'option')['image_promo']" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src">
                            </div>
                        </div>
                    </div>
                </section>
                <section class="flat-spacing-27 bg_grey-5">
                    <div class="container">
                        <div class="flat-title title-upper flex-row justify-content-between px-0"><span class="title wow fadeInUp font-druk" data-wow-delay="0s" wp-call-function="get_field('section_4', 'option')['title_featured_category_2']" wp-call-function-echo="true" wp-call-function-set="content">Merchandise</span>
                            <div class="box-sw-navigation">
                                <div class="nav-sw round nav-next-product-2 nav-next-slider"><span class="icon icon-arrow-left"></span>
                                </div>
                                <div class="nav-sw round nav-prev-product-2 nav-prev-slider"><span class="icon icon-arrow-right"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-full slider-layout-right">
                        <div class="swiper sw-wrapper-right tf-sw-product-sell-2 wrap-sw-over" data-preview="4.5" data-tablet="3.5" data-mobile="1.5" data-space-lg="30" data-space-md="15" data-pagination="1" data-pagination-md="3" data-pagination-lg="4">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" lazy="true" wc-show-products="query" wc-show-products-mode="freestyle" wc-show-products-type-query-name="product_1" wc-show-products-categories="is_array(get_field('section_4', 'option')['featured_category_2']) ? implode(', ', array_map(function($item) { return $item->slug; }, get_field('section_4', 'option')['featured_category_2'])) : 'uncategorized'" wc-show-products-range="0-5" wc-show-products-type-paginate="false">
                                    <div class="card-product style-line-hover">
                                        <div class="card-product-wrapper" data-pg-name="Gallery Wrapper" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'woocommerce_single' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"> <a href="product-detail.html" class="product-img" data-pg-name="Main image" wc-product-link> <img class="lazyload img-product" data-srcx="images/products/stakeboard-greenblack.jpg" src="images/products/stakeboard-greenblack.jpg" alt="image-product" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-yellowblack.jpg" src="images/products/stakeboard-yellowblack.jpg" alt="image-product" data-pg-name="Thumbnails Container" wp-call-function="esc_url( $hover_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                            <div class="list-product-btn absolute-2"> <a href="#" class="box-icon bg_white quick-add tf-btn-loading quickaddlink" data-product-id="" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" onclick="quickadd()" wp-call-function-1="'quickadd(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a><a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading" wp-call-function="$product_id;" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="data-product-id" wp-call-function-1="'quickview(event,'.$product_id.')';" wp-call-function-echo-1="true" wp-call-function-1-set="attr" wp-call-function-1-set-attr="onclick"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                            </div>
                                            <div class="on-sale-wrap">
                                                <div class="bg-danger on-sale-item rounded-0" wc-product-sale wc-product-sale-amount-sel="span" wc-product-sale-template-variant="home" wc-product-sale-template="use">
                                                    Discount &nbsp;<span>20</span>%
                                                </div>
                                            </div>
                                            <?php
if (!$product->is_in_stock()) {
    echo '<div class="countdown-box soldout">';
    echo '<div class="font-haas_mediu text-danger">SOLD OUT</div>';
    echo '</div>';
}
?>
                                        </div>
                                        <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu" wc-product-link><span wc-product-title wc-product-title-template="use" wc-product-title-template-variant="home">Habitat Skateboards Ellipse Complete
                                                Skateboard</span></a> <span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template="use" wc-product-price-template-variant="home"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mb-2 ">$9,999.00</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through ">
                                                    $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">
                                                    $9,999.00
</normal> </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="swiper-slide" lazy="true" cms-no-export>
                                    <div class="card-product style-line-hover">
                                        <div class="card-product-wrapper"> <a href="product-detail.html" class="product-img"> <img class="lazyload img-product" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> <img class="lazyload img-hover" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product"> </a>
                                            <div class="list-product-btn absolute-2"> <a href="#quick_add" data-product-id="666" data-bs-toggle="modal" data-bs-target="" class="box-icon bg_white quick-add tf-btn-loading"> <span class="icon icon-bag"></span> <span class="tooltip">Quick Add</span> </a> <a href="javascript:void(0);" class="box-icon bg_white wishlist btn-icon-action"> <span class="icon icon-heart"></span> <span class="tooltip">Add to
                                                    Wishlist</span> <span class="icon icon-delete"></span> </a> <a href="#compare" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="box-icon bg_white compare btn-icon-action"> <span class="icon icon-compare"></span> <span class="tooltip">Add to
                                                    Compare</span> <span class="icon icon-check"></span> </a> <a href="#quick_view" data-bs-toggle="modal" class="box-icon bg_white quickview tf-btn-loading"> <span class="icon icon-view"></span> <span class="tooltip">Quick
                                                    View</span> </a>
                                            </div>
                                        </div>
                                        <div class="card-product-info"> <a href="product-detail.html" class="title link fs-14 fw-7 text-uppercase font-haas_mediu">Skateboards
                                            Thrasher Complete Skateboard</a> <span class="price font-haas_mediu">$89.99</span>
                                            <ul class="list-color-product">
                                                <li class="list-color-item color-swatch active"> <span class="tooltip">Blue
                                                    Black</span> <span class="swatch-value blue-black bg-multiple-color"></span>
                                                    <img class="lazyload" data-srcx="images/products/stakeboard-blueblack.jpg" src="images/products/stakeboard-blueblack.jpg" alt="image-product">
                                                </li>
                                                <li class="list-color-item color-swatch"> <span class="tooltip">Blue
                                                    White</span> <span class="swatch-value blue-white bg-multiple-color"></span>
                                                    <img class="lazyload" data-srcx="images/products/stakeboard-bluewhite.jpg" src="images/products/stakeboard-bluewhite.jpg" alt="image-product">
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Banner collection -->
                <!-- Accessories -->
                <!-- /Accessories -->
                <!-- Testimonial -->
                <section class="flat-testimonial-bg font-haas_light" style="background-image: url(images/slider/testimonials-sb.jpg);" wp-call-function="echo ' background-image: url(' . get_field('section_5', 'option')['image_background'] . ');';" wp-call-function-set="attr" wp-call-function-set-attr="style">
                    <div class="container">
                        <div class="flat-title mb_1"><span class="title text-white text-uppercase fw-8 fadeInUp font-druk" data-wow-delay="0s" wp-call-function="get_field('section_5', 'option')['title_testimonials']" wp-call-function-echo="true" wp-call-function-set="content">TESTIMONIALS</span>
                            <p class="sub-title text-white wow fadeInUp font-haas_mediu" data-wow-delay="0s" wp-call-function="get_field('section_5', 'option')['subtitle_testimonials']" wp-call-function-echo="true" wp-call-function-set="content">Where wishlists
                            come true. Discover the pieces of their (or your) dreams.</p>
                        </div>
                        <div class="wrap-carousel">
                            <div class="swiper tf-sw-testimonial mb_60" data-preview="4" data-tablet="2" data-mobile="1" data-space-lg="30" data-space-md="15">
                                <div class="swiper-wrapper" wp-call-function="display_product_reviews(5, [4, 5]);">
                                    <div class="swiper-slide">
                                        <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                                            <div class="author">
                                                <div class="name font-haas_mediu">Loretta</div>
                                                <div class="metas font-haas_mediu"><i class="icon-check"></i> Verify customer
                                                </div>
                                            </div>
                                            <div class="rating color-black"><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i>
                                            </div>
                                            <div class="heading font-haas_mediu">A minimalist dream</div>
                                            <div class="line-clamp-3 text">
                                                Exceptional quality at a fraction of the price you would pay for big brands.
                                                Very elegant, discreet and beautiful materials.
</div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                                            <div class="author">
                                                <div class="name font-haas_mediu">Loretta</div>
                                                <div class="metas font-haas_mediu"><i class="icon-check"></i> Verify customer
                                                </div>
                                            </div>
                                            <div class="rating color-black"><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i>
                                            </div>
                                            <div class="heading font-haas_mediu">A minimalist dream</div>
                                            <div class="text">
                                                Exceptional quality at a fraction of the price you would pay for big brands.
                                                Very elegant, discreet and beautiful materials.
</div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                                            <div class="author">
                                                <div class="name font-haas_mediu">Loretta</div>
                                                <div class="metas font-haas_mediu"><i class="icon-check"></i> Verify customer
                                                </div>
                                            </div>
                                            <div class="rating color-black"><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i>
                                            </div>
                                            <div class="heading font-haas_mediu">A minimalist dream</div>
                                            <div class="text">
                                                Exceptional quality at a fraction of the price you would pay for big brands.
                                                Very elegant, discreet and beautiful materials.
</div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                                            <div class="author">
                                                <div class="name font-haas_mediu">Loretta</div>
                                                <div class="metas font-haas_mediu"><i class="icon-check"></i> Verify customer
                                                </div>
                                            </div>
                                            <div class="rating color-black"><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i>
                                            </div>
                                            <div class="heading font-haas_mediu">A minimalist dream</div>
                                            <div class="text">
                                                Exceptional quality at a fraction of the price you would pay for big brands.
                                                Very elegant, discreet and beautiful materials.
</div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                                            <div class="author">
                                                <div class="name font-haas_mediu">Loretta</div>
                                                <div class="metas font-haas_mediu"><i class="icon-check"></i> Verify customer
                                                </div>
                                            </div>
                                            <div class="rating color-black"><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i><i class="icon-start"></i>
                                            </div>
                                            <div class="heading font-haas_mediu">A minimalist dream</div>
                                            <div class="text">
                                                Exceptional quality at a fraction of the price you would pay for big brands.
                                                Very elegant, discreet and beautiful materials.
</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="sw-dots d-flex style-2 medium dots-white sw-pagination-testimonial justify-content-center">
</div>
                        </div>
                    </div>
                </section>
                <!-- /Testimonial -->
                <!-- Icon box -->
                <section class="flat-spacing-1 flat-iconbox wow fadeInUp font-haas_light" data-wow-delay="0s">
                    <div class="container">
                        <div class="wrap-carousel wrap-mobile">
                            <div class="swiper tf-sw-mobile" data-preview="1" data-space="15">
                                <div class="swiper-wrapper wrap-iconbox">
                                    <div class="swiper-slide">
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-shipping" wp-call-function="echo '<img src=&quot; ' . get_field('footer', 'option')['icon_box_1'] . ' &quot;></img>';"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_icon_box_1']" wp-call-function-echo="true" wp-call-function-set="content">Free Shipping</div>
                                                <p wp-call-function="get_field('footer', 'option')['subtitle_icon_box_1']" wp-call-function-echo="true" wp-call-function-set="content">Free shipping over order $120</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-shipping" wp-call-function="echo '<img src=&quot; ' . get_field('footer', 'option')['icon_box_2'] . ' &quot;></img>';"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_icon_box_2']" wp-call-function-echo="true" wp-call-function-set="content">Free Shipping</div>
                                                <p wp-call-function="get_field('footer', 'option')['subtitle_icon_box_2']" wp-call-function-echo="true" wp-call-function-set="content">Free shipping over order $120</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-shipping" wp-call-function="echo '<img src=&quot; ' . get_field('footer', 'option')['icon_box_3'] . ' &quot;></img>';"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_icon_box_3']" wp-call-function-echo="true" wp-call-function-set="content">Free Shipping</div>
                                                <p wp-call-function="get_field('footer', 'option')['subtitle_icon_box_3']" wp-call-function-echo="true" wp-call-function-set="content">Free shipping over order $120</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide">
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-shipping" wp-call-function="echo '<img src=&quot; ' . get_field('footer', 'option')['icon_box_4'] . ' &quot;></img>';"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_icon_box_4']" wp-call-function-echo="true" wp-call-function-set="content">Free Shipping</div>
                                                <p wp-call-function="get_field('footer', 'option')['subtitle_icon_box_4']" wp-call-function-echo="true" wp-call-function-set="content">Free shipping over order $120</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide" cms-no-export>
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-payment fs-22"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu">Flexible Payment</div>
                                                <p>Pay with Multiple Credit Cards</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide" cms-no-export>
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-return fs-20"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu">14 Day Returns</div>
                                                <p>Within 30 days for an exchange</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-slide" cms-no-export>
                                        <div class="tf-icon-box style-row">
                                            <div class="icon"><i class="icon-suport"></i>
                                            </div>
                                            <div class="content">
                                                <div class="title fw-4 font-haas_mediu">Premium Support</div>
                                                <p>Outstanding premium support</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="wrap-pagination">
                                <div class="container-full">
                                    <div class="sw-dots style-2 sw-pagination-mb justify-content-center"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                <!-- /Icon box -->
                <!-- Footer -->
                <!-- /Footer -->
            </div>
        </wpcontent>
        <wpfooter cms-template-part="parts/footer" cms-template-part-define class="mt-5">
            <footer id="footer" class="footer has-all-border has-border-full">
                <div class="footer-wrap">
                    <div class="footer-body">
                        <div class="container font-haas_light">
                            <div class="row">
                                <div class="col-xl-3 col-md-6 col-12">
                                    <div class="footer-col footer-col-1 footer-col-block">
                                        <div class="footer-heading footer-heading-desktop">
                                            <h6 class="fs-14 text-uppercase fw-8 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_menu_1']" wp-call-function-echo="true" wp-call-function-set="content">Help</h6>
                                        </div>
                                        <div class="footer-heading footer-heading-moblie">
                                            <h6 class="fs-14 text-uppercase fw-8">Help</h6>
                                        </div>
                                        <ul class="footer-menu-list tf-collapse-content" wp-register-menu="footer_menu_1" wp-register-menu-description="Footer Menu 1" wp-nav-menu="footer_menu_1" wp-nav-menu-theme-location="footer_menu_1" wp-nav-menu-depth="4" wp-nav-menu-walker="new FooterMenu()" wp-nav-menu-class="footer-menu-list tf-collapse-content">
                                            <li><a href="#" class="footer-menu_item">Privacy Policy</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Returns + Exchanges</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Shipping</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Terms &amp; Conditions</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">FAQ’s</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Compare</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">My Wishlist</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 col-12">
                                    <div class="footer-col footer-col-2 footer-col-block">
                                        <div class="footer-heading footer-heading-desktop">
                                            <h6 class="fs-14 text-uppercase fw-8 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_menu_2']" wp-call-function-echo="true" wp-call-function-set="content">About us</h6>
                                        </div>
                                        <div class="footer-heading footer-heading-moblie">
                                            <h6 class="fs-14 text-uppercase fw-8">About us</h6>
                                        </div>
                                        <ul class="footer-menu-list tf-collapse-content" wp-register-menu="footer_menu_2" wp-register-menu-description="Footer Menu 2" wp-nav-menu="footer_menu_2" wp-nav-menu-theme-location="footer_menu_2" wp-nav-menu-depth="4" wp-nav-menu-walker="new FooterMenu()" wp-nav-menu-class="footer-menu-list tf-collapse-content">
                                            <li><a href="#" class="footer-menu_item">Our Story</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Visit Our Store</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Contact Us</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">Account</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 col-12">
                                    <div class="footer-col footer-col-3 footer-col-block">
                                        <div class="footer-heading footer-heading-desktop">
                                            <h6 class="fs-14 text-uppercase fw-8 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_menu_3']" wp-call-function-echo="true" wp-call-function-set="content">Find us</h6>
                                        </div>
                                        <div class="footer-heading footer-heading-moblie">
                                            <h6 class="fs-14 text-uppercase fw-8">Find us</h6>
                                        </div>
                                        <ul class="footer-menu-list tf-collapse-content" wp-register-menu="footer_menu_3" wp-register-menu-description="Footer Menu 3" wp-nav-menu="footer_menu_3" wp-nav-menu-theme-location="footer_menu_3" wp-nav-menu-depth="4" wp-nav-menu-walker="new FooterMenu()" wp-nav-menu-class="footer-menu-list tf-collapse-content">
                                            <li>
                                                <div class="footer-menu_item">Find a location nearestyou.</div>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item text-decoration-underline">See Our
                                                Stores</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item">(08) 8942 1299</a>
                                            </li>
                                            <li> <a href="#" class="footer-menu_item"><EMAIL></a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-xl-3 col-md-6 col-12">
                                    <div class="footer-newsletter footer-col-block">
                                        <div class="footer-heading footer-heading-desktop">
                                            <h6 class="fs-14 text-uppercase fw-8 font-haas_mediu" wp-call-function="get_field('footer', 'option')['title_menu_4']" wp-call-function-echo="true" wp-call-function-set="content">Sign Up for Email</h6>
                                        </div>
                                        <div class="footer-heading footer-heading-moblie">
                                            <h6 class="fs-14 text-uppercase fw-8">Sign Up for Email</h6>
                                        </div>
                                        <div class="tf-collapse-content">
                                            <div class="footer-menu_item mb-3" wp-call-function="get_field('short_profile','option')" wp-call-function-echo="true" wp-call-function-set="content">Sign up to get first dibs on new arrivals,
                                                sales, exclusive content, events and more!
</div>
                                            <div class="footer-menu_item mb-3" wp-call-function="get_field('footer', 'option')['email']" wp-call-function-echo="true" wp-call-function-set="content">Sign up to get first dibs on new arrivals,
                                                sales, exclusive content, events and more!
</div>
                                            <div class="footer-menu_item mb-3" wp-call-function="get_field('footer', 'option')['address']" wp-call-function-echo="true" wp-call-function-set="content">Sign up to get first dibs on new arrivals,
                                                sales, exclusive content, events and more!
</div>
                                            <a href="contact-1.html" target="_blank" class="tf-btn btn-line" wp-call-function="echo 'https://www.google.com/maps/search/' . urlencode(get_field('footer', 'option')['address']);" wp-call-function-set="attr" wp-call-function-set-attr="href">Get direction<i class="icon icon-arrow1-top-left"></i></a>
                                            <ul class="tf-social-icon d-flex gap-20 style-default">
                                                <li><a href="#" class="box-icon round social-facebook border-line-black" wp-call-function="get_field('facebook','option')" wp-call-function-echo="true" wp-call-function-set="href"><i class="icon fs-14 icon-fb"></i></a>
                                                </li>
                                                <li><a href="#" class="box-icon round social-twiter border-line-black" wp-call-function="get_field('twitter','option')" wp-call-function-echo="true" wp-call-function-set="href"><i class="icon fs-14 icon-Icon-x"></i></a>
                                                </li>
                                                <li><a href="#" class="box-icon round social-instagram border-line-black" wp-call-function="get_field('instagram','option')" wp-call-function-echo="true" wp-call-function-set="href"><i class="icon fs-14 icon-instagram"></i></a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer-bottom">
                        <div class="container font-haas_light">
                            <div class="row">
                                <div class="col-12">
                                    <div class="footer-bottom-wrap d-flex gap-20 flex-wrap justify-content-between align-items-center">
                                        <div class="footer-menu_item">&copy; 2024 Karuca.Red. All Rights Reserved | Built
                                            with all the 💖 in the world by <a href="http://rawstudio.id">Raw Studio
                                            Coop</a>
                                        </div>
                                        <div class="tf-payment" cms-no-export>
                                            <img src="images/payments/visa.png" alt="">
                                            <img src="images/payments/img-1.png" alt="">
                                            <img src="images/payments/img-2.png" alt="">
                                            <img src="images/payments/img-3.png" alt="">
                                            <img src="images/payments/img-4.png" alt="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </footer>
            <div class="progress-wrap">
                <svg class="progress-circle svg-content" width="100%" height="100%" viewBox="-1 -1 102 102">
                    <path d="M50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98" style="transition: stroke-dashoffset 10ms linear 0s; stroke-dasharray: 307.919, 307.919; stroke-dashoffset: 286.138;">
</path>
                </svg>
            </div>
            <div class="tf-toolbar-bottom type-1150 font-haas_mediu">
                <div class="toolbar-item"><a href="#toolbarShopmb" wp-call-function="// Get the shop page ID
$shop_page_id = wc_get_page_id('shop');

// Check if the shop page ID is valid
if ($shop_page_id) {
    // Get the shop page URL
    $shop_page_url = get_permalink($shop_page_id);
    echo esc_url($shop_page_url);
} else {
    echo 'Shop page is not set.';
}" wp-call-function-set="attr" wp-call-function-set-attr="href"> <div class="toolbar-icon"><i class="icon-shop"></i>
                        </div> <div class="toolbar-label">Shop</div> </a>
                </div>
                <div class="toolbar-item"><a href="#canvasSearch" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft"> <div class="toolbar-icon">
                            <i class="icon-search"></i>
                        </div> <div class="toolbar-label">Search</div> </a>
                </div>
                <div class="toolbar-item"><a href="#login" wp-call-function="$page_id = wc_get_page_id('myaccount');

if ($page_id) {
    $page_url = get_permalink($page_id);
    echo esc_url($page_url);
} else {
    echo 'page is not set.';
}" wp-call-function-set="attr" wp-call-function-set-attr="href"> <div class="toolbar-icon"><i class="icon-account"></i>
                        </div> <div class="toolbar-label">Account</div> </a>
                </div>
                <div class="toolbar-item"><a href="#shoppingCart" data-bs-toggle="modal"> <div class="toolbar-icon"><i class="icon-bag"></i>
                            <div class="toolbar-count" wp-call-function="if ( function_exists( 'WC' ) && WC()->cart ) {
    // Get the number of items in the cart
    $cart_count = WC()->cart->get_cart_contents_count();
    echo esc_html( $cart_count );
}" wp-call-function-set="content">1</div>
                        </div> <div class="toolbar-label">Cart</div> </a>
                </div>
            </div>
            <div class="modal fade modalDemo" id="modalDemo">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="header">
                            <h5 class="demo-title">Ultimate HTML Theme</h5><span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                        </div>
                        <div class="mega-menu">
                            <div class="row-demo">
                                <div class="demo-item">
                                    <a href="index.html"> <div class="demo-image position-relative">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                            <div class="demo-label"><span class="demo-new">New</span><span>Trend</span>
                                            </div>
                                        </div> <span class="demo-name">Home Fashion 01</span> </a>
                                </div>
                                <div class="demo-item">
                                    <a href="#"> <div class="demo-image position-relative">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                            <div class="demo-label"><span class="demo-new">New</span><span class="demo-hot">Hot</span>
                                            </div>
                                        </div> <span class="demo-name">Home Multi Brand</span> </a>
                                </div>
                                <div class="demo-item">
                                    <a href="#"> <div class="demo-image position-relative">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                            <div class="demo-label"><span class="demo-hot">Hot</span>
                                            </div>
                                        </div> <span class="demo-name">Home Fashion 02</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Fashion 03</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Fashion 04</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Fashion 05</span> </a>
                                </div>
                                <div class="demo-item">
                                    <a href="#"> <div class="demo-image position-relative">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                            <div class="demo-label"><span class="demo-new">New</span>
                                            </div>
                                        </div> <span class="demo-name">Home Fashion 06</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Fashion 07</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Fashion 08</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Skincare</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Headphone</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Gift Card</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Furniture</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Furniture 2</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Skateboard</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Stroller</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Decor</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Electronic</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Setup Gear</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Dog Accessories</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Kitchen Wear</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Phonecase</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Paddle Boards</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Glasses</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home POD Store</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Activewear</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Handbag</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Tee</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Sock</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Jewelry</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Sneaker</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Accessories</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Grocery</span> </a>
                                </div>
                                <div class="demo-item"><a href="#"> <div class="demo-image">
                                            <img class="lazyload" data-srcx="#" src="#" alt="karuca.red">
                                        </div> <span class="demo-name">Home Baby</span> </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="offcanvas offcanvas-start canvas-mb" id="mobileMenu">
                <span class="icon-close icon-close-popup" data-bs-dismiss="offcanvas" aria-label="Close"></span>
                <div class="mb-canvas-content">
                    <div class="mb-body">
                        <ul class="nav-ul-mb" id="wrapper-menu-navigation" wp-register-menu="mobile_menu" wp-register-menu-description="Mobile Menu" wp-nav-menu="mobile_menu" wp-nav-menu-class="nav-ul-mb" wp-nav-menu-walker="new MobileMenu()" wp-nav-menu-depth="4" wp-nav-menu-theme-location="mobile_menu">
                            <li class="nav-mb-item"> <a href="#dropdown-menu-one" class="mb-menu-link current" data-bs-toggle="collapse" aria-expanded="true" aria-controls="dropdown-menu-one"> <span>Home</span></a>
                            </li>
                            <li class="nav-mb-item"> <a href="#dropdown-menu-five" class="collapsed mb-menu-link current" data-bs-toggle="collapse" aria-expanded="true" aria-controls="dropdown-menu-five"> <span>Blog</span> <span class="btn-open-sub"></span> </a>
                                <div id="dropdown-menu-five" class="collapse">
                                    <ul class="sub-nav-menu">
                                        <li> <a href="blog-grid.html" class="sub-nav-link">Grid layout</a>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                            <li class="nav-mb-item"> <a href="https://themeforest.net/item/ecomus-ultimate-html5-template/53417990?s_rank=3" class="mb-menu-link">Buy now</a>
                            </li>
                        </ul>
                        <div class="mb-other-content">
                            <div class="mb-notice"><a href="contact-1.html" class="text-need">Need help ?</a>
                            </div>
                            <ul class="mb-info">
                                <li>Address: <Span wp-call-function="get_field('footer', 'option')['address']" wp-call-function-echo="true" wp-call-function-set="content">1234 Fashion Street, Suite 567, <br> New York, NY 10001</span>
                                </li>
                                <li>Email: <b wp-call-function="get_field('footer', 'option')['email']" wp-call-function-echo="true" wp-call-function-set="content"><EMAIL></b>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="offcanvas offcanvas-end canvas-search font-haas_light" id="canvasSearch">
                <div class="canvas-wrapper">
                    <header class="tf-search-head">
                        <div class="title fw-5 font-druk">
                            Search our site
                            <div class="close"><span class="icon-close icon-close-popup" data-bs-dismiss="offcanvas" aria-label="Close"></span>
                            </div>
                        </div>
                        <div class="tf-search-sticky mb-2">
                            <form class="tf-mini-search-frm" action="<?php echo esc_url(home_url('/')); ?>">
                                <fieldset class="text">
                                    <input type="text" placeholder="Search Product" tabindex="0" value="" aria-required="true" required="" name="s" id="s" value="<?php the_search_query(); ?>">
                                    <input type="hidden" name="post_type" value="product">
                                </fieldset>
                                <button type="submit">
                                    <i class="icon-search"></i>
                                </button>
                            </form>
                        </div>
                        <div class="tf-search-sticky">
                            <form class="tf-mini-search-frm" action="<?php echo esc_url(home_url('/')); ?>">
                                <fieldset class="text">
                                    <input type="text" placeholder="Search Blog" tabindex="0" value="" aria-required="true" required="" name="s" id="s" value="<?php the_search_query(); ?>">
                                </fieldset>
                                <button type="submit">
                                    <i class="icon-search"></i>
                                </button>
                            </form>
                        </div>
                    </header>
                    <div class="canvas-body p-0">
                        <div class="tf-search-content">
                            <div class="tf-cart-hide-has-results">
                                <div class="tf-col-quicklink">
                                    <div class="tf-search-content-title fw-5 font-druk">Quick link</div>
                                    <ul class="tf-quicklink-list font-haas_mediu" wp-register-menu="quick_menu" wp-register-menu-description="Quick Link" wp-nav-menu="quick_menu" wp-nav-menu-class="tf-quicklink-list font-haas_mediu" wp-nav-menu-depth="4" wp-nav-menu-theme-location="quick_menu" wp-nav-menu-walker="new QuickMenu()">
                                        <li class="tf-quicklink-item"><a href="shop-default.html">Fashion</a>
                                        </li>
                                        <li class="tf-quicklink-item"><a href="shop-default.html">Men</a>
                                        </li>
                                        <li class="tf-quicklink-item"><a href="shop-default.html">Women</a>
                                        </li>
                                        <li class="tf-quicklink-item"><a href="shop-default.html">Accessories</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tf-col-content">
                                    <div class="tf-search-content-title fw-5 font-druk">Need some inspiration?</div>
                                    <div class="tf-search-hidden-inner">
                                        <div class="tf-loop-item" wc-show-products="query" wc-show-products-mode="freestyle" wc-show-products-type-query-name="product_1" wc-show-products-categories="is_array(get_field('section_4', 'option')['featured_category_1']) ? implode(', ', array_map(function($item) { return $item->slug; }, get_field('section_4', 'option')['featured_category_1'])) : 'uncategorized'" wc-show-products-type-count="5" wc-show-products-type-paginate="false">
                                            <div class="image" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'product_carousel' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"><a href="product-detail.html" wc-product-link> <img src="images/products/white-3.jpg" alt="" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                            </div>
                                            <div class="content font-haas_mediu"><a class="title" href="product-detail.html" wc-product-link><span wc-product-title wc-product-title-template-variant="search">Loose
                                                    Fit Sweatshirt</span></a>
                                                <div class="price mt-2"><span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template-variant="search"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mt-2 text-danger">$9,999.00
</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through text-opacity-25">
                                                            $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">$9,999.00</normal> </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fullRight fade modal-shopping-cart" id="shoppingCart">
                <div class="modal-dialog">
                    <div class="font-haas_mediu modal-content">
                        <div class="font-druk header">
                            <div class="title fw-5">Shopping cart</div><span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                        </div>
                        <div class="wrap">
                            <div class="tf-mini-cart-wrap">
                                <div class="tf-mini-cart-main">
                                    <div class="tf-mini-cart-sroll">
                                        <div class="tf-mini-cart-items">
                                            <div class="tf-mini-cart-item" cms-no-export>
                                                <div class="tf-mini-cart-image">
                                                    <a href="product-detail.html"> <img src="images/products/white-2.jpg" alt=""> </a>
                                                </div>
                                                <div class="tf-mini-cart-info"><a class="title link" href="product-detail.html">T-shirt</a>
                                                    <div class="meta-variant">Light gray</div>
                                                    <div class="price fw-6">$25.00</div>
                                                    <div class="tf-mini-cart-btns">
                                                        <div class="wg-quantity small">
                                                            <span class="btn-quantity minus-btn">-</span>
                                                            <input type="text" name="number" value="1">
                                                            <span class="btn-quantity plus-btn">+</span>
                                                        </div>
                                                        <a href="#" class="tf-mini-cart-remove">Remove</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tf-minicart-recommendations">
                                            <div class="font-druk tf-minicart-recommendations-heading">
                                                <div class="tf-minicart-recommendations-title">You may also like</div>
                                                <div class="sw-dots small style-2 cart-slide-pagination"></div>
                                            </div>
                                            <div class="swiper tf-cart-slide">
                                                <div class="swiper-wrapper">
                                                    <div class="swiper-slide" wc-show-products="query" wc-show-products-mode="freestyle" wc-show-products-type-query-name="product_1" wc-show-products-categories="is_array(get_field('section_4', 'option')['featured_category_1']) ? implode(', ', array_map(function($item) { return $item->slug; }, get_field('section_4', 'option')['featured_category_1'])) : 'uncategorized'" wc-show-products-type-count="5" wc-show-products-type-paginate="false">
                                                        <div class="tf-minicart-recommendations-item">
                                                            <div class="tf-minicart-recommendations-item-image" wp-call-function="// Get the product ID
            $product_id = get_the_ID();

            // Get the product images
            $attachment_ids = $product->get_gallery_image_ids(); // Get gallery images

            // Get the main image (featured image)
            $main_image = wp_get_attachment_image_src( get_post_thumbnail_id( $product_id ), 'product_carousel' );
            
            // Get the first image from gallery (if available)
            $hover_image = !empty( $attachment_ids ) ? wp_get_attachment_image_src( $attachment_ids[0], 'product_carousel' ) : $main_image;" wp-call-function-set="before"><a href="product-detail.html" wc-product-link> <img src="images/products/white-3.jpg" alt="" wp-call-function="esc_url( $main_image[0] );" wp-call-function-echo="true" wp-call-function-set="attr" wp-call-function-set-attr="src"> </a>
                                                            </div>
                                                            <div class="tf-minicart-recommendations-item-infos flex-grow-1"><a class="title" href="product-detail.html" wc-product-link><span wc-product-title wc-product-title-template-variant="minicart">Loose
                                                                    Fit Sweatshirt</span></a>
                                                                <div class="price">
                                                                    <span class="price font-haas_mediu" wc-product-price wc-product-price-format="custom" wc-product-price-sale="[data-pg-name=&quot;Regular Price Sale&quot;]" wc-product-price-regular-sale="[data-pg-name=&quot;Sale Price&quot;]" wc-product-price-regular-no-sale="[data-pg-name=&quot;Regular Price No Sale&quot;]" wc-product-price-template-variant="minicart"> <reg data-pg-name="Regular Price Sale" class="d-block font-haas_black fs-6 mb-2 text-danger">
                                                                            $9,999.00
</reg> <sale data-pg-name="Sale Price" class="d-block font-haas_light fs-6 fst-italic text-dark text-decoration-line-through text-opacity-25">
                                                                            $9,999.00
</sale> <normal data-pg-name="Regular Price No Sale" class="font-haas_black fs-6">$9,999.00</normal> </span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tf-mini-cart-bottom">
                                    <div class="tf-mini-cart-bottom-wrap">
                                        <div class="tf-cart-totals-discounts">
                                            <div class="tf-cart-total">Subtotal</div>
                                            <div class="tf-totals-total-value fw-6" id="minicart_subtotal">$49.99 USD</div>
                                        </div>
                                        <div class="tf-mini-cart-line"></div>
                                        <div class="tf-mini-cart-view-checkout"><a href="view-cart.html" class="tf-btn btn-outline radius-3 link w-100 justify-content-center" wp-call-function="$page_id = wc_get_page_id('cart');

if ($page_id) {
    $page_url = get_permalink($page_id);
    echo esc_url($page_url);
} else {
    echo 'page is not set.';
}" wp-call-function-set="attr" wp-call-function-set-attr="href">View cart</a><a href="checkout.html" class="tf-btn btn-fill animate-hover-btn radius-3 w-100 justify-content-center" wp-call-function="$page_id = wc_get_page_id('checkout');

if ($page_id) {
    $page_url = get_permalink($page_id);
    echo esc_url($page_url);
} else {
    echo 'page is not set.';
}" wp-call-function-set="attr" wp-call-function-set-attr="href"><span>Check out</span></a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade modalDemo" id="quick_add">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" id="quick-add-to-cart-content">
                        <div class="header" cms-no-export>
                            <span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                        </div>
                        <div class="font-haas_mediu wrap" cms-no-export>
                            <div class="tf-product-info-item">
                                <div class="image">
                                    <img src="images/products/orange-1.jpg" alt="">
                                </div>
                                <div class="content">
                                    <a href="product-detail.html">[name]Ribbed Tank Top</a>
                                    <div class="tf-product-info-price">
                                        <!-- <div class="price-on-sale">$8.00</div>
                                <div class="compare-at-price">$10.00</div>
                                <div class="badges-on-sale"><span>20</span>% OFF</div> -->
                                        <div class="price">[price/regular_price/sale_price]$18.00</div>
                                    </div>
                                </div>
                            </div>
                            <div class="tf-product-info-variant-picker mb_15">
                                <div class="variant-picker-item">
                                    <form class="form-variant" id="variantform" action="./variant/variant-process.php" method="post">
                                        <div class="d-flex flex-column gap-15 mb-3">
                                            <fieldset class="mb-3">
                                                <label for="color" class="form-label">Color *</label>
                                                <select class="form-control shadow-none border-dark " name="color" id="color" required>
                                                    <option value="" disabled selected>Select Color</option>
                                                    <option value="orange">Orange</option>
                                                    <option value="black">Black</option>
                                                    <option value="white">White</option>
                                                    <option value="red">Red</option>
                                                </select>
                                            </fieldset>
                                            <fieldset class="w-50">
                                                <label for="size">Size *</label>
                                                <select name="size" id="size" required>
                                                    <option value="" disabled selected>Select Size</option>
                                                    <option value="s">S</option>
                                                    <option value="m">M</option>
                                                    <option value="l">L</option>
                                                    <option value="xl">XL</option>
                                                </select>
                                            </fieldset>
                                            <fieldset class="w-50">
                                                <label for="size">Size *</label>
                                                <select name="size" id="sizes" required>
                                                    <option value="" disabled selected>Select Size</option>
                                                    <option value="s">S</option>
                                                    <option value="m">M</option>
                                                    <option value="l">L</option>
                                                    <option value="xl">XL</option>
                                                </select>
                                            </fieldset>
                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="tf-product-info-quantity mb_15">
                                <div class="quantity-title fw-6">Quantity</div>
                                <div class="wg-quantity">
                                    <span class="btn-quantity minus-btn">-</span>
                                    <input type="text" name="number" value="1">
                                    <span class="btn-quantity plus-btn">+</span>
                                </div>
                            </div>
                            <div class="tf-product-info-buy-button">
                                <form><a href="javascript:void(0);" class="animate-hover-btn btn-add-to-cart btn-fill flex-grow-1 fs-16 fw-6 justify-content-center tf-btn"><span>Add
                                        to cart -&nbsp;</span><span class="tf-qty-price">$18.00</span></a>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade modalDemo" id="quick_view">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content" id="quick-view-content">
                        <div class="header" cms-no-export>
                            <span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                        </div>
                        <div class="wrap" cms-no-export>
                            <div class="tf-product-media-wrap">
                                <div class="swiper tf-single-slide">
                                    <div class="swiper-wrapper">
                                        <div class="swiper-slide">
                                            <div class="item">
                                                <img src="images/products/orange-1.jpg" alt="">
                                            </div>
                                        </div>
                                        <div class="swiper-slide">
                                            <div class="item">
                                                <img src="images/products/pink-1.jpg" alt="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="swiper-button-next button-style-arrow single-slide-prev"></div>
                                    <div class="swiper-button-prev button-style-arrow single-slide-next"></div>
                                </div>
                            </div>
                            <div class="position-relative tf-product-info-wrap">
                                <div class="font-haas_mediu tf-product-info-list">
                                    <div class="tf-product-info-title">
                                        <h5><a class="font-druk link" href="product-detail.html">Ribbed Tank Top</a></h5>
                                    </div>
                                    <div class="tf-product-info-badges font-haas_mediu">
                                        <div class="badges text-uppercase">Best seller</div>
                                        <div class="product-status-content">
                                            <i class="icon-lightning"></i>
                                            <p class="fw-6">Selling fast! 48 people have this in their carts.</p>
                                        </div>
                                    </div>
                                    <div class="tf-product-info-price">
                                        <div class="price font-haas_mediu">$18.00</div>
                                    </div>
                                    <div class="tf-product-description">
                                        <p>Nunc arcu faucibus a et lorem eu a mauris adipiscing conubia ac aptent ligula
                                        facilisis a auctor habitant parturient a a.Interdum fermentum.</p>
                                    </div>
                                    <div class="tf-product-info-variant-picker">
                                        <div class="variant-picker-item">
                                            <div class="variant-picker-label font-haas_mediu">
                                                Color: <span class="fw-6 variant-picker-label-value">Orange</span>
                                            </div>
                                            <div class="variant-picker-values">
                                                <input id="values-orange-1" type="radio" name="color-1" checked>
                                                <label class="hover-tooltip radius-60" for="values-orange-1" data-value="Orange"><span class="btn-checkbox bg-color-orange"></span><span class="tooltip">Orange</span>
                                                </label>
                                                <input id="values-black-1" type="radio" name="color-1">
                                                <label class=" hover-tooltip radius-60" for="values-black-1" data-value="Black"><span class="btn-checkbox bg-color-black"></span><span class="tooltip">Black</span>
                                                </label>
                                                <input id="values-white-1" type="radio" name="color-1">
                                                <label class="hover-tooltip radius-60" for="values-white-1" data-value="White"><span class="btn-checkbox bg-color-white"></span><span class="tooltip">White</span>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="variant-picker-item">
                                            <div class="d-flex justify-content-between align-items-center font-haas_mediu">
                                                <div class="variant-picker-label">
                                                    Size: <span class="fw-6 variant-picker-label-value">S</span>
                                                </div>
                                                <div class="find-size btn-choose-size fw-6 ">Find your size</div>
                                            </div>
                                            <div class="variant-picker-values font-haas_mediu">
                                                <input type="radio" name="size-1" id="values-s-1" checked>
                                                <label class="style-text" for="values-s-1" data-value="S">
                                                    <p>S</p>
                                                </label>
                                                <input type="radio" name="size-1" id="values-m-1">
                                                <label class="style-text" for="values-m-1" data-value="M">
                                                    <p>M</p>
                                                </label>
                                                <input type="radio" name="size-1" id="values-l-1">
                                                <label class="style-text" for="values-l-1" data-value="L">
                                                    <p>L</p>
                                                </label>
                                                <input type="radio" name="size-1" id="values-xl-1">
                                                <label class="style-text" for="values-xl-1" data-value="XL">
                                                    <p>XL</p>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tf-product-info-quantity">
                                        <div class="quantity-title fw-6 font-haas_mediu">Quantity</div>
                                        <div class="wg-quantity"><span class="btn-quantity minus-btn">-</span>
                                            <input type="text" name="number" value="1"><span class="btn-quantity plus-btn">+</span>
                                        </div>
                                    </div>
                                    <div class="tf-product-info-buy-button font-haas_mediu">
                                        <form><a href="javascript:void(0);" class="tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart"><span>Add
                                                to cart -&nbsp;</span><span class="tf-qty-price">$8.00</span></a><a href="javascript:void(0);" class="tf-product-btn-wishlist hover-tooltip box-icon bg_white wishlist btn-icon-action"> <span class="icon icon-heart"></span> <span class="tooltip">Add to
                                                Wishlist</span> <span class="icon icon-delete"></span> </a><a href="#compare" data-bs-toggle="offcanvas" aria-controls="offcanvasLeft" class="tf-product-btn-wishlist hover-tooltip box-icon bg_white compare btn-icon-action"> <span class="icon icon-compare"></span> <span class="tooltip">Add to
                                                Compare</span> <span class="icon icon-check"></span> </a>
                                            <div class="w-100">
                                                <a href="#" class="btns-full">Buy with <img src="images/payments/paypal.png" alt=""></a>
                                                <a href="#" class="payment-more-option">More payment options</a>
                                            </div>
                                        </form>
                                    </div>
                                    <div>
                                        <a href="product-detail.html" class="tf-btn fw-6 btn-line font-haas_mediu">View
                                        full details<i class="icon icon-arrow1-top-left"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </wpfooter>
        <!-- Javascript -->
        <script type="text/javascript" src="js/bootstrap.min.js"></script>
        <script type="text/javascript" src="js/jquery.min.js"></script>
        <script type="text/javascript" src="js/swiper-bundle.min.js"></script>
        <script type="text/javascript" src="js/carousel.js"></script>
        <script type="text/javascript" src="js/count-down.js"></script>
        <script type="text/javascript" src="js/bootstrap-select.min.js"></script>
        <script type="text/javascript" src="js/lazysize.min.js"></script>
        <script type="text/javascript" src="js/count-down.js"></script>
        <script type="text/javascript" src="js/drift.min.js"></script>
        <script type="text/javascript" src="js/wow.min.js"></script>
        <script type="text/javascript" src="js/multiple-modal.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
    </body>
</html>