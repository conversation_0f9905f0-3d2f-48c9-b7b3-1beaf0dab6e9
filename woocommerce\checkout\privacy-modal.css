/* Privacy Modal Styles */
.privacy-modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.privacy-modal-content {
    background-color: #fff;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.privacy-modal-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.privacy-modal-header h3 {
    margin: 0;
    font-size: 1.4em;
    color: #333;
    font-weight: 600;
}

.privacy-modal-body {
    padding: 20px 25px;
    line-height: 1.6;
    color: #555;
}

.privacy-policy-content {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    margin: 15px 0;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
}

.privacy-policy-content::-webkit-scrollbar {
    width: 6px;
}

.privacy-policy-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.privacy-policy-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.privacy-policy-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.privacy-policy-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.privacy-policy-content li {
    margin-bottom: 5px;
}

.privacy-policy-content a {
    color: #0073aa;
    text-decoration: none;
    font-weight: 500;
}

.privacy-policy-content a:hover {
    text-decoration: underline;
}

.privacy-modal-footer {
    padding: 15px 25px 25px;
    border-top: 1px solid #eee;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.privacy-checkbox-container {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
}

.privacy-checkbox-container input[type="checkbox"] {
    margin-right: 10px;
    margin-top: 2px;
    transform: scale(1.2);
    cursor: pointer;
}

.privacy-modal-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.privacy-modal-buttons .button {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
}

.privacy-modal-buttons .button-secondary {
    background-color: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

.privacy-modal-buttons .button-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.privacy-modal-buttons .button-primary {
    background-color: #28a745;
    color: white;
    border: 1px solid #28a745;
}

.privacy-modal-buttons .button-primary:hover:not(:disabled) {
    background-color: #218838;
    border-color: #1e7e34;
}

.privacy-modal-buttons .button-primary:disabled {
    background-color: #ccc;
    border-color: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

/* Loading state */
.privacy-modal.loading .privacy-modal-content {
    opacity: 0.7;
    pointer-events: none;
}

/* Focus styles for accessibility */
.privacy-modal-buttons .button:focus,
.privacy-checkbox-container input:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .privacy-modal {
        padding: 10px;
    }
    
    .privacy-modal-content {
        width: 100%;
        max-height: 90vh;
        margin: 0;
    }
    
    .privacy-modal-header,
    .privacy-modal-body,
    .privacy-modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .privacy-modal-header h3 {
        font-size: 1.2em;
    }
    
    .privacy-modal-buttons {
        flex-direction: column;
        gap: 8px;
    }
    
    .privacy-modal-buttons .button {
        width: 100%;
        padding: 14px 20px;
    }
    
    .privacy-checkbox-container {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .privacy-modal-header,
    .privacy-modal-body,
    .privacy-modal-footer {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .privacy-modal-header {
        padding-top: 15px;
        padding-bottom: 10px;
    }
    
    .privacy-modal-body {
        padding-top: 15px;
        padding-bottom: 15px;
    }
    
    .privacy-modal-footer {
        padding-top: 10px;
        padding-bottom: 20px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .privacy-modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .privacy-modal-header,
    .privacy-modal-footer {
        background-color: #1a202c;
        border-color: #4a5568;
    }
    
    .privacy-modal-header h3 {
        color: #e2e8f0;
    }
    
    .privacy-policy-content {
        background-color: #1a202c;
        border-color: #4a5568;
        color: #cbd5e0;
    }
    
    .privacy-policy-content a {
        color: #63b3ed;
    }
}
