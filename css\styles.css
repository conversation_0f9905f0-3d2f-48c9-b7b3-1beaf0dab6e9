/* --------- Abstracts variable ---------- */
@import url(bootstrap-select.min.css);

:root {
    --main: #000000;
    --white: #ffffff;
    --primary: #db1215;
    --red_1: #db1215;
    --backdrop: rgba(0, 0, 0, 0.5);
    --main-rgba-1: rgba(0, 0, 0, 0.16);
    --main-rgba-2: rgba(0, 0, 0, 0.15);
    --text: #545454;
    --text-2: #909090;
    --text-3: #868686;
    --line: #ebebeb;
    --line-2: #ececec;
    --bg-1: #fcfbf9;
    --bg-2: #f6f6f6;
    --bg-3: #fcffb2;
    --bg-4: #c9f4aa;
    --bg-5: #ff7b54;
    --bg-6: #8054ff;
    --bg-7: #f1f0ed;
    --bg-8: #f0edf1;
    --bg-9: #edeef1;
    --bg-10: #fcfbf9;
    --bg-11: #f2f2f2;
    --success: #31a56d;
    --bg-load: #d9d9d9;
    --bg-scrollbar-track: #f1f1f1;
    --bg-scrollbar-thumb: #c1c1c1;
}

.color-primary-2 {
    --primary: #814037;
}

.color-primary-3 {
    --primary: #93f859;
}

.color-primary-4 {
    --primary: #000;
}

.color-primary-5 {
    --primary: #67c08f;
}

.color-primary-6 {
    --primary: #a45f3b;
}

.color-primary-7 {
    --primary: #a6e57c;
}

.color-primary-8 {
    --primary: #61b482;
}

.color-primary-9 {
    --primary: #ff0b0b;
}

.color-primary-10 {
    --primary: #fd8484;
}

.color-main-text-2 {
    --main: #253d4e;
}

/* ---------- Reset css styles ----------- */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    font-family: inherit;
    font-size: 100%;
    font-style: inherit;
    font-weight: inherit;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block;
}

/* Elements
-------------------------------------------------------------- */
html {
    margin-right: 0 !important;
    scroll-behavior: smooth;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Albert Sans", sans-serif;
    font-size: 14px;
    line-height: 22px;
    font-weight: 400;
    color: var(--main);
    background-color: var(--white);
}

img {
    max-width: 100%;
    height: auto;
    transform: scale(1);
    vertical-align: middle;
    -ms-interpolation-mode: bicubic;
}

.row {
    margin-right: -15px;
    margin-left: -15px;
}

.row > * {
    padding-left: 15px;
    padding-right: 15px;
}

ul,
li {
    list-style-type: none;
    margin-bottom: 0;
    padding-left: 0;
    list-style: none;
}

.container {
    max-width: 1540px;
}

.container {
    width: 100%;
    margin: auto;
}

.container {
    padding-left: 15px;
    padding-right: 15px;
}

.container-full {
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    padding: 0px 15px;
}

.slider-layout-right {
    width: calc(100vw - (100vw - 1440px) / 2 + 30px);

    /* margin-right: unset */
    max-width: 100%;
    margin-left: auto;
}

textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"] {
    font-family: "Albert Sans", sans-serif;
    border: 1px solid var(--line);
    outline: 0;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
    font-size: 14px;
    line-height: 24px;
    border-radius: 0px;
    padding: 12px 18px;
    width: 100%;
    background: var(--white);
    color: var(--text-2);
    font-weight: 400;
}

textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus {
    border-color: var(--line);
}

textarea::placeholder,
input[type="text"]::placeholder,
input[type="password"]::placeholder,
input[type="datetime"]::placeholder,
input[type="datetime-local"]::placeholder,
input[type="date"]::placeholder,
input[type="month"]::placeholder,
input[type="time"]::placeholder,
input[type="week"]::placeholder,
input[type="number"]::placeholder,
input[type="email"]::placeholder,
input[type="url"]::placeholder,
input[type="search"]::placeholder,
input[type="tel"]::placeholder,
input[type="color"]::placeholder {
    color: var(--text-2);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

textarea {
    height: 112px;
    resize: none;
}

/* Placeholder color */
::-webkit-input-placeholder {
    color: var(--text-2);
}

:-moz-placeholder {
    color: var(--text-2);
}

::-moz-placeholder {
    color: var(--text-2);
    opacity: 1;
}

/* Since FF19 lowers the opacity of the placeholder by default */
:-ms-input-placeholder {
    color: var(--text-2);
}

/* Typography
-------------------------------------------------------------- */
.font-young-serif {
    font-family: "Young Serif", serif;
}

.font-gloock {
    font-family: "Gloock", serif;
}

.font-caprasimo {
    font-family: "Caprasimo", serif;
}

.font-poppins {
    font-family: "Poppins", serif;
}

.font-compacta {
    font-family: "compacta", serif;
}

.font-druk {
    font-family: "druk", serif !important;
}

.font-haas_black {
    font-family: "haas_black", serif !important;
}

.font-haas_mediu {
    font-family: "haas_mediu", serif !important;
}

.font-haas_thin {
    font-family: "haas_thin", serif !important;
}

.font-haas_light {
    font-family: "haas_light", serif !important;
    font-size: 16px !important;
    line-height: 24px !important;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Albert Sans", sans-serif;
    text-rendering: optimizeLegibility;
    color: var(--main);
    font-weight: 400;
}

h1 {
    font-size: 80px;
    line-height: 96px;
}

h2 {
    font-size: 68px;
    line-height: 81.6px;
}

h3 {
    font-size: 56px;
    line-height: 67.2px;
}

h4 {
    font-size: 42px;
    line-height: 50.4px;
}

h5 {
    font-size: 28px;
    line-height: 33.6px;
}

h6 {
    font-size: 20px;
    line-height: 30px;
}

b,
strong {
    font-weight: bolder;
}

video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.fs-10 {
    font-size: 10px !important;
}

.fs-12 {
    font-size: 12px !important;
}

.text-14 {
    font-size: 14px !important;
}

.fs-14 {
    font-size: 14px !important;
    line-height: 22.4px !important;
}

.fs-15 {
    font-size: 15px !important;
}

.fs-16 {
    font-size: 16px !important;
}

.fs-18 {
    font-size: 18px !important;
}

.fs-20 {
    font-size: 20px !important;
}

.fs-28 {
    font-size: 28px !important;
}

.fs-22,
.text-22 {
    font-size: 22px !important;
}

.fw-4 {
    font-weight: 400 !important;
}

.fw-5 {
    font-weight: 500 !important;
}

.fw-6 {
    font-weight: 600 !important;
}

.fw-7 {
    font-weight: 700 !important;
}

.fw-8 {
    font-weight: 800 !important;
}

.bg_white {
    background-color: var(--white) !important;
}

.bg_dark {
    background-color: var(--main) !important;
}

.bg_line {
    background-color: var(--line);
}

.bg_primary {
    background-color: var(--primary);
}

.bg_orange {
    background-color: #f47249;
}

.bg_orange-2 {
    background-color: #c38361;
}

.bg_orange-3 {
    background-color: #ffa500;
}

.bg_orange-4 {
    background-color: #ff7b54;
}

.bg_violet {
    background-color: #7900f2;
}

.bg_violet-1 {
    background-color: #47276f;
}

.bg_blue {
    background-color: #2c3bc5;
}

.bg_blue-2 {
    background-color: #a8bcd4;
}

.bg_blue-3 {
    background-color: #8054ff;
}

.bg_blue-4 {
    background-color: #f5f7fc;
}

.bg_blue-5 {
    background-color: #f1f4f9 !important;
}

.bg_blue-6 {
    background-color: #1c355e !important;
}

.bg_dark-blue {
    background-color: #00008b;
}

.bg_light-blue {
    background-color: #add8e6;
}

.bg_light-blue-1 {
    background-color: #c6ccd1 !important;
}

.bg_beige {
    background-color: #c8ad7f;
}

.bg_beige-2 {
    background-color: #857460;
}

.bg_beige-3 {
    background-color: #f8f6f2;
}

.bg_dark-beige {
    background-color: #9f8c76;
}

.bg_brown {
    background-color: #977945;
}

.bg_brown-1 {
    background-color: #383240 !important;
}

.bg_brown-2 {
    background-color: #433b37;
}

.bg_brown-3 {
    background-color: #85715e;
}

.bg_brown-4 {
    background-color: #f4f0eb;
}

.bg_light-brown {
    background-color: #c55252;
}

.bg_light-brown1 {
    background-color: #6c2c28;
}

.bg_purple {
    background-color: #d966d9;
}

.bg_light-purple {
    background-color: #d1c6c6 !important;
}

.bg_light-green {
    background-color: #caffd6;
}

.bg_light-green-2 {
    background-color: #c9f4aa;
}

.bg_light-green-3 {
    background-color: #e9f5e0;
}

.bg_dark-green {
    background-color: #006400;
}

.bg-dark-green-2 {
    background-color: #6a7369 !important;
}

.bg_pink {
    background-color: #fcc6de;
}

.bg_light-pink {
    background-color: #ffb6c1;
}

.bg_light-grey {
    background-color: #d3d3d3;
}

.bg_light-grey-2 {
    background-color: #c4b3b3 !important;
}

.bg_light-grey-3 {
    background-color: #d1cec6 !important;
}

.bg_grey {
    background-color: #ccc;
}

.bg_grey-2 {
    background-color: #fbf7f0;
}

.bg_grey-3 {
    background-color: #faf8f2;
}

.bg_grey-4 {
    background-color: #fcf8f3;
}

.bg_grey-5 {
    background-color: #f6f6f6;
}

.bg_grey-6 {
    background-color: #f9f6ef;
}

.bg_grey-7 {
    background-color: #f5f5f5;
}

.bg_grey-8 {
    background-color: #f2f2f2;
}

.bg_grey-9 {
    background-color: #f1f3f1;
}

.bg_grey-10 {
    background-color: #faf7f2 !important;
}

.bg_grey-11 {
    background-color: #f8f8f8;
}

.bg_grey-12 {
    background-color: #f1f4f7 !important;
}

.bg_grey-13 {
    background-color: #efefef !important;
}

.bg_grey-14 {
    background-color: #f0f4f6 !important;
}

.bg_dark-grey {
    background-color: #808080;
}

.bg_yellow {
    background-color: #dcdb79;
}

.bg_yellow-2 {
    background-color: #fcffb2;
}

.bg_yellow-3 {
    background-color: #e6fe5e !important;
}

.bg_yellow-4 {
    background-color: #ffdf00;
}

.bg_yellow-5 {
    background-color: #fff7df !important;
}

.bg_yellow-6 {
    background-color: #ece5b6 !important;
}

.bg_yellow-7 {
    background-color: #f4eddb !important;
}

.bg-yellow-8 {
    background-color: #fcf151;
}

.bg-yellow-9 {
    background-color: #fbf24a !important;
}

.bg-yellow-10 {
    background-color: #fff6c6;
}

.bg_light-yellow {
    background-color: #ffffe0;
}

.bg_golden-yellow {
    background-color: #ffdf00;
}

.bg_taupe {
    background-color: #483c32;
}

.bg_green {
    background-color: #e1eb78;
}

.bg_green-1 {
    background-color: #dcf9e2;
}

.bg_green-2 {
    background-color: #2c3728 !important;
}

.bg_green-3 {
    background-color: #1b3622 !important;
}

.bg_green-4 {
    background-color: #161d28 !important;
}

.bg_green-5 {
    background-color: #84e4e0 !important;
}

.bg_green-6 {
    background-color: #a6e57c;
}

.bg_cream {
    background-color: #fffdd0;
}

.bg_cream-2 {
    background-color: #fbf6ee;
}

.bg-gradient-1 {
    background: linear-gradient(
    102deg,
    rgb(159, 111, 189) 8%,
    rgb(168, 125, 196) 47%,
    rgb(180, 140, 203) 83%
  );
}

.bg-gradient-2 {
    background: linear-gradient(
    104deg,
    rgb(61, 53, 75) 0.54%,
    rgba(61, 53, 75, 0.56) 100%
  );
}

.bg-gradient-3 {
    background: linear-gradient(
    270deg,
    rgba(31, 31, 40, 0.8) 10%,
    rgb(30, 32, 40) 100%,
    rgb(30, 32, 40) 100%
  );
}

.bg_red {
    background-color: #f63400;
}

.bg_navy {
    background-color: #001f3f;
}

.bg_copper {
    background-color: #b87333;
}

.bg_gold {
    background-color: #f4dfb6;
}

.bg_f1f6e6 {
    background-color: #f1f6e6;
}

.bg_e7e4df {
    background-color: #e7e4df !important;
}

.bg_f5f6f0 {
    background-color: #f5f6f0;
}

.bg_pastel-brown {
    background-color: #b1907f;
}

.text_black {
    color: var(--main) !important;
}

.text_black-2 {
    color: var(--text) !important;
}

.text_black-3 {
    color: #545454 !important;
}

.text_primary {
    color: var(--primary) !important;
}

.text_success {
    color: var(--success);
}

.text-paragraph {
    color: #868686;
}

.text-sale {
    text-decoration: line-through;
    color: rgba(0, 0, 0, 0.5490196078);
}

.text_white {
    color: var(--white) !important;
}

.text-1 {
    font-size: 18px !important;
    line-height: 28.8px !important;
}

.text-2 {
    font-size: 16px;
    line-height: 24px;
}

.text-3 {
    font-size: 16px;
    line-height: 19.2px;
}

.letter-2 {
    letter-spacing: 2px;
}

.text_red-1 {
    color: #db1215 !important;
}

.text_blue-1 {
    color: #1c355e !important;
}

.text-stroke-white {
    -webkit-text-stroke: 2px var(--white);
    color: transparent;
}

a {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
    display: inline-block;
    color: var(--main);
}

a:focus,
a:hover {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    text-decoration: none;
    outline: 0;
}

label {
    font-weight: 600;
}

.link {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.link:hover {
    color: var(--primary) !important;
}

.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
}

.grid-6 {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
}

.gap-4 {
    gap: 4px !important;
}

.gap-5 {
    gap: 5px !important;
}

.gap-6 {
    gap: 6px !important;
}

.gap-8 {
    gap: 8px !important;
}

.gap-10 {
    gap: 10px !important;
}

.gap-12 {
    gap: 12px !important;
}

.gap-14 {
    gap: 14px !important;
}

.gap-15 {
    gap: 15px !important;
}

.gap-16 {
    gap: 16px;
}

.gap-20 {
    gap: 20px;
}

.gap-30 {
    gap: 30px;
}

.border-line {
    border: 1px solid var(--line) !important;
}

.border-white {
    border: 1px solid var(--white);
}

.border-line-black {
    border: 1px solid var(--main);
}

.border-line-blue-1 {
    border: 1px solid #1c355e !important;
}

.line {
    border-bottom: 1px solid var(--line);
}

.line-black {
    border-bottom: 1px solid var(--main);
}

.no-line {
    border: 0 !important;
}

.place-self-center {
    place-self: center !important;
}

.radius-3 {
    border-radius: 3px !important;
}

.radius-5 {
    border-radius: 5px !important;
}

.radius-10 {
    border-radius: 10px !important;
}

.radius-20 {
    border-radius: 20px !important;
}

.radius-60 {
    border-radius: 60px !important;
}

.rounded-full {
    border-radius: 999px !important;
}

.o-hidden {
    overflow: hidden;
}

.h-40 {
    height: 40px;
}

.h-46 {
    height: 46px;
}

.h-52 {
    height: 52px;
}

.px_15 {
    padding-left: 15px;
    padding-right: 15px;
}

.tf-md-hidden {
    display: none;
}

.box-center {
    position: absolute;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
}

.pt_0 {
    padding-top: 0px !important;
}

.pb_0 {
    padding-bottom: 0px !important;
}

.pr_0 {
    padding-right: 0px !important;
}

.py_15 {
    padding: 15px 0;
}

.py_20 {
    padding: 20px 0;
}

.pb_8 {
    padding-bottom: 8px;
}

.pb_15 {
    padding-bottom: 15px;
}

.pb_20 {
    padding-bottom: 20px;
}

.my_20 {
    margin: 20px 0px;
}

.mt_5 {
    margin-top: 5px;
}

.mt_3 {
    margin-top: 3px;
}

.mt_4 {
    margin-top: 4px;
}

.mt_8 {
    margin-top: 8px;
}

.mt_20 {
    margin-top: 20px;
}

.mt_37 {
    margin-top: 37px;
}

.mt_140 {
    margin-top: 140px;
}

.mb_10 {
    margin-bottom: 10px;
}

.mb_12 {
    margin-bottom: 12px;
}

.mb_15 {
    margin-bottom: 15px;
}

.mb_18 {
    margin-bottom: 18px;
}

.mb_20 {
    margin-bottom: 20px;
}

.mb_24 {
    margin-bottom: 24px !important;
}

.mb_30 {
    margin-bottom: 30px;
}

.mb_40 {
    margin-bottom: 40px;
}

.mb_36 {
    margin-bottom: 36px;
}

.mb_60 {
    margin-bottom: 60px;
}

.mb_200 {
    margin-bottom: 200px;
}

.flat-spacing-1 {
    padding: 70px 0;
}

.flat-spacing-2 {
    padding: 45px 0 70px;
}

.flat-spacing-3 {
    padding: 50px 0;
}

.flat-spacing-4 {
    padding-top: 55px;
    padding-bottom: 100px;
}

.flat-spacing-5 {
    padding: 76px 0px;
}

.flat-spacing-6 {
    padding: 45px 0px 100px;
}

.flat-spacing-7 {
    padding-bottom: 60px;
}

.flat-spacing-8 {
    padding-top: 35px;
    padding-bottom: 70px;
}

.flat-spacing-9 {
    padding: 65px 0px;
}

.flat-spacing-10 {
    padding: 55px 0px;
}

.flat-spacing-11 {
    padding-top: 80px;
    padding-bottom: 70px;
}

.flat-spacing-12 {
    padding-top: 100px;
    padding-bottom: 100px;
}

.flat-spacing-13 {
    padding-top: 34px;
    padding-bottom: 40px;
}

.flat-spacing-14 {
    padding-top: 84px;
    padding-bottom: 60px;
}

.flat-spacing-15 {
    padding-top: 78px;
    padding-bottom: 100px;
}

.flat-spacing-16 {
    padding-top: 130px;
    padding-bottom: 65px;
}

.flat-spacing-17 {
    padding-top: 100px;
    padding-bottom: 80px;
}

.flat-spacing-18 {
    padding-top: 80px;
    padding-bottom: 100px;
}

.flat-spacing-19 {
    padding-top: 79px;
    padding-bottom: 91px;
}

.flat-spacing-20 {
    padding-top: 30px;
    padding-bottom: 33px;
}

.flat-spacing-21 {
    padding-top: 60px;
    padding-bottom: 100px;
}

.flat-spacing-22 {
    padding: 30px 0;
}

.flat-spacing-23 {
    padding-top: 100px;
    padding-bottom: 78px;
}

.flat-spacing-24 {
    padding-top: 109px;
    padding-bottom: 132px;
}

.flat-spacing-25 {
    padding-top: 36px;
    padding-bottom: 36px;
}

.flat-spacing-26 {
    padding-top: 65px;
    padding-bottom: 75px;
}

.flat-spacing-27 {
    padding-top: 90px;
    padding-bottom: 90px;
}

.flat-spacing-28 {
    padding-top: 38px;
    padding-bottom: 90px;
}

.flat-spacing-29 {
    padding-top: 26px;
    padding-bottom: 90px;
}

.flat-spacing-30 {
    padding-top: 65px;
    padding-bottom: 42px;
}

[data-grid="grid-1"] {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr;
}

[data-grid="grid-2"] {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr 1fr;
}

[data-grid="grid-3"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(3, 1fr);
}

[data-grid="grid-4"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);
}

[data-grid="grid-5"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(5, 1fr);
}

[data-grid="grid-6"] {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(6, 1fr);
}

.grid-template-columns-2 {
    grid-template-columns: 1fr 1fr;
}

.tf-row-flex {
    display: flex;
    flex-direction: row;
    column-gap: 30px;
    row-gap: 30px;
}

.tf-grid-layout {
    display: grid;
    gap: 15px;
}

.tf-grid-layout.tf-col-2 {
    grid-template-columns: 1fr 1fr;
}

.tf-grid-layout.gap30 {
    gap: 30px;
}

.tf-grid-layout.row-gap-10 {
    row-gap: 10px !important;
}

.tf-grid-layout-v2 {
    display: grid;
    gap: 15px;
}

.overflow-unset {
    overflow: unset !important;
}

.sticky-top {
    z-index: 50;
}

.cursor-not-allowed {
    cursor: not-allowed;
}

.cursor-auto {
    cursor: auto;
}

.tag-list {
    list-style: disc;
    padding-left: 20px;
}

.tag-list li {
    list-style: inherit;
}

.has-line-bottom {
    position: relative;
}

.has-line-bottom::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 50%;
    background-color: var(--line);
    height: 1px;
    width: 100%;
    max-width: 1440px;
    transform: translateX(-50%);
}

.letter-spacing-1 {
    letter-spacing: 1px;
}

.letter-spacing-2 {
    letter-spacing: 2px;
}

.letter-spacing-3 {
    letter-spacing: 0.6px;
}

.line-under {
    color: rgba(0, 0, 0, 0.85);
    text-underline-offset: 3px;
    text-decoration-thickness: 1px;
    text-decoration-line: underline;
    transition: text-decoration-thickness 1s ease;
}

.line-under:hover {
    color: var(--main);
    text-decoration-thickness: 2px;
    text-decoration-line: underline;
}

.transition-linear {
    transition-timing-function: linear !important;
}

.z-5 {
    z-index: 5;
}

.text-highlight {
    -webkit-text-stroke: 1px #000;
    color: transparent !important;
    flex-direction: row-reverse;
}

.text-line-clamp-1 {
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

.grid-2-lg {
    display: grid;
    gap: 15px;
}

.cus-container2 {
    position: static;
    max-width: 1200px;
    padding: 0px 15px;
    margin: 0 auto;
    width: 100%;
}

/* ------------ Components ---------------- */
/* ------------ header ---------------- */
.tf-top-bar_wrap {
    padding: 10px 0px;
}

.top-bar-text .tf-btn {
    padding-bottom: 0;
    color: var(--primary);
}

.top-bar-text .tf-btn::after {
    background-color: var(--primary);
}

.top-bar-text .tf-btn:hover {
    opacity: 0.8;
}

.top-bar-text .tf-btn:hover::after {
    opacity: 0.8;
}

.top-bar-text .tf-btn.text_white {
    color: var(--white);
}

.top-bar-text .tf-btn.text_white::after {
    background-color: var(--white);
}

.announcement-bar {
    padding-right: 65px;
    position: relative;
    transition: all 0.3s linear;
}

.announcement-bar .close-announcement-bar {
    cursor: pointer;
    position: absolute;
    font-size: 12px;
    right: 25px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--white);
}

.announcement-bar.not-hover .box-sw-announcement-bar:hover {
    animation-play-state: running !important;
}

.box-sw-announcement-bar {
    display: flex;
    -webkit-animation: slide-har 14s linear infinite;
    animation: slide-har 14s linear infinite;
    transition: animation-duration 300ms;
}

.box-sw-announcement-bar:hover {
    animation-play-state: paused;
}

.speed-1 {
    -webkit-animation: slide-har 40s linear infinite;
    animation: slide-har 40s linear infinite !important;
}

.speed-1:hover {
    animation-play-state: paused !important;
}

.wrap-announcement-bar {
    overflow: hidden;
}

.noti-bar-text {
    padding: 10px 0px;
}

.wrap-announcement-bar-2 {
    position: relative;
}

.wrap-announcement-bar-2 .tf-sw-top_bar {
    margin: 0px 20px;
}

.wrap-announcement-bar-2 .navigation-topbar {
    position: absolute;
    z-index: 1;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wrap-announcement-bar-2 .navigation-topbar .icon {
    color: var(--white);
    font-size: 12px;
}

.wrap-announcement-bar-2 .navigation-topbar.nav-next-topbar {
    left: 0;
}

.wrap-announcement-bar-2 .navigation-topbar.nav-prev-topbar {
    right: 0;
}

@keyframes slide-har {
    0% {
        -webkit-transform: translateX(0%);
        transform: translateX(0%);
    }

    100% {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%);
    }
}

.announcement-bar-item {
    display: inline-block;
    padding-right: 46px;
    position: relative;
}

.announcement-bar-item p {
    font-size: 12px;
    padding: 10px 0px;
    font-weight: 600;
    color: var(--white);
    white-space: nowrap;
    padding-left: 23px;
}

.announcement-bar-item::after {
    height: 1px;
    width: 22px;
    background-color: var(--white);
    content: "";
    position: absolute;
    z-index: 1;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.top-bar-language .dropdown-menu {
    inset: 0px !important;
}

.top-bar-language .bootstrap-select.image-select.style-default > button {
    padding-right: 24px !important;
}

.top-bar-language
  .dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu {
    margin-top: 12px !important;
}

.top-bar-language .filter-option-inner .filter-option-inner-inner {
    font-weight: 500 !important;
}

.header-default .wrapper-header {
    min-height: 64px;
}

.header-default .wrapper-header .nav-icon li:last-child .nav-icon-item {
    margin-right: 10px;
}

.header-default .box-nav-ul {
    gap: 22px;
}

.header-style-2 .wrapper-header {
    min-height: 60px;
}

#header .box-nav-ul .item-link {
    font-size: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 7px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    position: relative;
}

#header .box-nav-ul .item-link .icon {
    font-size: 7px;
}

#header .box-nav-ul .item-link::before {
    content: "";
    width: 0;
    height: 1px;
    top: 99%;
    position: absolute;
    left: auto;
    right: 0;
    z-index: 1;
    -webkit-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    -o-transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
    background: var(--main);
}

#header .box-nav-ul li:hover .item-link::before {
    width: 100%;
    left: 0;
    right: auto;
}

#header .nav-icon .icon {
    font-size: 18px;
    font-weight: 500;
}

#header .nav-icon li {
    display: inline-flex;
}

#header .nav-icon .nav-icon-item {
    position: relative;
    display: inline-flex;
}

#header .nav-icon .nav-icon-item:hover {
    color: var(--primary);
}

#header .nav-icon .nav-icon-item .text {
    font-size: 16px;
    line-height: 25.6px;
    font-weight: 500;
    display: inline-block;
}

#header .nav-icon .count-box {
    position: absolute;
    line-height: 18px;
    height: 18px;
    min-width: 18px;
    text-align: center;
    padding: 0 3px;
    font-size: 10px;
    font-weight: 500;
    border-radius: 9999px;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block;
    overflow: hidden;
    z-index: 2;
    top: -8px;
    right: -10px;
    color: var(--white);
    background-color: var(--primary);
}

#header .tf-product-header .card-product .box-icon {
    width: 32px;
    height: 32px;
}

#header .tf-product-header .card-product .tooltip {
    margin-top: -4px;
}

#header .sub-menu .hover-sw-nav .nav-sw {
    width: 36px;
    height: 36px;
    top: 38%;
}

#header .sub-menu .list-color-item .tooltip {
    display: none;
}

.header-absolute {
    margin-bottom: -64px;
    background-color: transparent;
    z-index: 999;
}

.logo-header img {
    width: 100%;
    max-height: 40px;
}

.box-nav-ul .menu-items {
    padding: 36px 0px;
}

.box-nav-ul .menu-items:hover > .sub-menu {
    pointer-events: all;
    opacity: 1;
    visibility: visible;
    transform: translateX(0px) translateY(0px);
}

.box-nav-ul .menu-items-2 {
    position: relative;
    cursor: pointer;
}

.box-nav-ul .menu-items-2::after {
    position: absolute;
    content: "\e906";
    font-family: "icomoon";
    font-size: 11px;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.box-nav-ul .menu-items-2 .sub-menu {
    top: -22px;
    left: 110%;
    min-width: 200px;
}

.box-nav-ul .menu-items-2 .sub-menu::after {
    position: absolute;
    display: block;
    content: "";
    width: 60px;
    height: 45px;
    background-color: transparent;
    left: -18%;
    top: 3px;
}

.box-nav-ul .menu-items-2:hover > .sub-menu {
    pointer-events: all;
    opacity: 1;
    visibility: visible;
    transform: translateX(0px) translateY(0px);
}

.box-nav-ul .sub-menu {
    pointer-events: none;
    position: absolute;
    background-color: var(--white);
    min-width: 268px;
    z-index: 999;
    visibility: hidden;
    text-align: left;
    padding-top: 24px;
    padding-bottom: 24px;
    padding-inline-end: 20px;
    padding-inline-start: 19px;
    top: 100%;
    opacity: 0;
    visibility: hidden;
    transform: translateX(0px) translateY(15px);
    box-shadow: 0 4px 8px rgb(235, 235, 235);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.box-nav-ul .mega-menu {
    padding-top: 37px;
    padding-bottom: 42px;
    border: none;
    max-height: calc(100vh - 74px);
    overflow: auto;
    left: 0;
    right: 0;
}

.box-nav-ul .mega-menu .card-product .card-product-wrapper {
    max-height: 290px;
}

.box-nav-ul .mega-menu .wrap-sw-over {
    padding-bottom: 40px;
    margin-bottom: -40px;
}

.box-nav-ul .menu-heading {
    font-size: 12px;
    line-height: 22px;
    font-weight: 700;
    color: var(--main);
    text-transform: uppercase;
    margin-bottom: 17px;
}

.box-nav-ul .menu-list li:not(:last-child) {
    margin-bottom: 14px;
}

.box-nav-ul .menu-list .demo-label {
    top: -14px;
    right: -35px;
}

.box-nav-ul .submenu-default {
    left: -110px;
}

.mega-menu .row-demo {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    column-gap: 20px;
    row-gap: 25px;
}

.mega-menu .demo-item {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
    border-radius: 5px;
    padding: 6px 7px 0;
    border: solid 1px transparent;
    transition: border 0.4s;
    margin-bottom: 1px;
}

.mega-menu .demo-item .demo-name {
    line-height: 45px;
    display: block;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    font-weight: 600;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.mega-menu .demo-item .demo-name:hover {
    color: var(--primary);
}

.mega-menu .demo-item:hover {
    border-color: var(--main);
}

.mega-menu .view-all-demo {
    margin-top: 48px;
}

.mega-menu .collection-item .collection-title {
    background-color: #f2f2f2;
    border-radius: 3px;
}

.mega-menu .collection-item .collection-content {
    bottom: 40px;
}

.mega-menu .collection-item .tf-btn .icon {
    margin: 0px;
}

.mega-page {
    max-width: 900px;
    margin: auto;
}

.demo-label {
    position: absolute;
    top: 9px;
    right: 7px;
    gap: 5px;
    display: flex;
}

.demo-label span {
    font-size: 10px;
    line-height: 19px;
    padding: 0 8px;
    background-color: rgb(131, 183, 53);
    color: var(--white);
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.demo-label .demo-new {
    background-color: rgb(72, 212, 187);
}

.demo-label .demo-hot {
    background-color: rgb(252, 87, 50);
}

.canvas-mb {
    width: 100% !important;
    max-width: min(90%, 320px);
    border-right: 0 !important;
}

.canvas-mb .mb-canvas-content {
    padding-top: 60px;
    padding-bottom: 0;
    padding-inline-end: 0;
    padding-inline-start: 20px;
    min-width: 320px;
    max-width: 100%;
    max-width: min(90%, 320px);
    grid-auto-rows: minmax(0, 1fr) auto;
    isolation: isolate;
    height: 100%;
    width: 100%;
    display: grid;
    align-content: start;
}

.canvas-mb .mb-body {
    padding-top: 20px;
    padding-right: 20px;
    overscroll-behavior-y: contain;
    overflow-y: auto;
}

.canvas-mb .icon-close-popup {
    position: absolute;
    font-size: 16px;
    z-index: 3;
    top: 20px;
    left: 15px;
    background-color: transparent;
    border: none;
    height: 30px;
    width: 30px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--main);
}

.canvas-mb .mb-bottom {
    padding-top: 15px;
}

.canvas-mb .mb-bottom .site-nav-icon {
    margin-bottom: 18px;
}

.canvas-mb .mb-bottom .bottom-bar-language {
    min-height: 63px;
    border-top: solid 1px rgb(236, 236, 236);
    gap: 28px;
    max-width: calc(100% - 20px);
    display: flex;
    align-items: center;
}

.canvas-mb
  .mb-bottom
  .bottom-bar-language
  .image-select.type-currencies
  > .dropdown-menu {
    width: 240px !important;
    margin-left: 0 !important;
}

.canvas-mb
  .mb-bottom
  .bottom-bar-language
  .image-select
  > .dropdown-menu::before {
    display: none;
}

.canvas-mb .site-nav-icon {
    padding: 0 18px;
    line-height: 40px;
    border: solid 1px rgb(242, 242, 242);
    gap: 9px;
    background-color: rgb(242, 242, 242);
    color: var(--main);
    border-radius: 3px;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.canvas-mb .site-nav-icon .icon {
    font-size: 14px;
}

.canvas-mb .site-nav-icon:hover {
    color: rgb(242, 242, 242);
    background-color: var(--main);
    border-color: var(--main);
}

.canvas-mb .mb-other-content .group-icon {
    gap: 9px;
    margin-bottom: 28px;
}

.canvas-mb .mb-other-content .text-need {
    font-weight: 500;
    border-bottom: 1px solid var(--main);
    margin-bottom: 11px;
}

.canvas-mb .mb-other-content .mb-info li {
    color: rgb(134, 134, 134);
    font-size: 14px;
    line-height: 24px;
}

.nav-ul-mb .nav-mb-item {
    padding: 2px 0px;
}

.nav-ul-mb .nav-mb-item:not(:last-child) {
    border-bottom: 1px solid rgb(238, 238, 238);
}

.nav-ul-mb .nav-mb-item .mb-menu-link {
    min-height: 40px;
    font-weight: 500;
    font-size: 14px;
    color: var(--main);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-ul-mb .nav-mb-item .mb-menu-link:not(.collapsed) .btn-open-sub::before {
    transform: rotate(90deg);
}

.nav-ul-mb .btn-open-sub {
    position: relative;
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.nav-ul-mb .btn-open-sub:after,
.nav-ul-mb .btn-open-sub::before {
    content: "";
    position: absolute;
    z-index: 1;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--main);
    transition: 0.4s ease 0.1s;
    margin: auto;
}

.nav-ul-mb .btn-open-sub::before {
    width: 2px;
    height: 12px;
}

.nav-ul-mb .btn-open-sub::after {
    width: 12px;
    height: 2px;
}

.nav-ul-mb .sub-nav-menu {
    margin-left: 10px;
    padding-left: 19px;
    margin-bottom: 26px;
    border-left: 1px solid rgb(236, 236, 236);
}

.nav-ul-mb .sub-menu-level-2 {
    margin-bottom: 5px;
}

.nav-ul-mb .sub-nav-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 32px;
}

.nav-ul-mb .sub-nav-link:not(.collapsed) .btn-open-sub::before {
    transform: rotate(90deg);
}

.nav-ul-mb .sub-nav-link.line-clamp {
    position: relative;
    display: inline-flex;
}

.nav-ul-mb .sub-nav-link.line-clamp .demo-label {
    top: -5px;
    right: -38px;
}

.header-list-categories {
    display: flex;
    align-items: center;
    gap: 4px;
}

.header-list-categories .categories-item {
    min-height: 68px;
    position: relative;
    display: flex;
    align-items: center;
}

.header-list-categories .categories-item a {
    font-size: 12px;
    font-weight: 800;
    padding: 11px 10px;
    transition: background-color 0.15s ease;
}

.header-list-categories .categories-item a::after {
    content: "";
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    height: 2px;
    background: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s;
}

.header-list-categories .categories-item.active a {
    background-color: rgb(242, 242, 242);
}

.header-list-categories .categories-item.active a::after {
    transform: scaleX(1);
}

.header-list-categories .categories-item:hover a::after {
    transform: scaleX(1);
}

.header-style-2 .header-bottom .wrapper-header {
    min-height: 60px;
}

.header-style-2
  .dropdown.bootstrap-select.image-select.type-currencies
  > .dropdown-menu {
    margin-left: calc(50% - 60px) !important;
}

.header-style-2
  .dropdown.bootstrap-select.image-select.type-currencies
  > .dropdown-menu::after {
    left: 12%;
}

.header-bottom .box-nav-ul .menu-items {
    padding: 20px 0px;
}

.header-bottom .header-list-categories .categories-item {
    min-height: auto;
}

.header-bottom .header-list-categories .categories-item a::after {
    content: none;
}

.header-style-3 .wrapper-header {
    min-height: 64px;
}

.header-style-3 .box-nav-ul .menu-items {
    padding: 32px 0px;
}

.wrap-header-left {
    display: flex;
    align-items: center;
}

.wrap-header-left .box-navigation {
    padding-left: 40px;
}

.wrap-header-left .mega-page {
    max-width: 810px;
    margin-left: 70px;
}

.wrap-header-left-2 .mega-page {
    margin-left: 0px;
}

.header-white .box-nav-ul .item-link,
.header-white .nav-icon .nav-icon-item {
    color: var(--white);
}

.header-white .box-nav-ul .item-link::before {
    background-color: var(--white) !important;
}

.header-white .btn-mobile svg path {
    fill: var(--white);
}

header {
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    left: 0;
    right: 0;
    -webkit-transition: 0.15s ease-out;
    -o-transition: 0.15s ease-out;
    transition: 0.15s ease-out;
    z-index: 888;
    background-color: var(--white);
}

header.header-bg {
    background-color: var(--white);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-white.header-bg {
    background-color: #c4b3b3;
}

.header-scroll-white .dark-logo {
    display: none;
}

.header-scroll-white.header-bg {
    background-color: var(--white);
}

.header-scroll-white.header-bg .dark-logo {
    display: block;
}

.header-scroll-white.header-bg .white-logo {
    display: none;
}

.header-scroll-white.header-bg .box-nav-ul .item-link,
.header-scroll-white.header-bg .nav-icon .nav-icon-item {
    color: var(--main);
}

.header-scroll-white.header-bg .box-nav-ul .item-link::before {
    background-color: var(--main) !important;
}

.header-scroll-white.header-bg .btn-mobile svg path {
    fill: var(--main);
}

#header.header-uppercase .box-nav-ul .item-link {
    text-transform: uppercase;
    font-size: 12px;
    line-height: 14.4px;
    font-weight: 700;
}

#header .header-bottom .box-right {
    display: flex;
    align-items: center;
    gap: 18px;
}

#header .header-bottom .box-right .phone {
    font-size: 28px;
    line-height: 28px;
    font-weight: 700;
    color: var(--primary);
}

#header .header-bottom .tf-list-categories {
    position: relative;
}

#header .header-bottom .tf-list-categories .categories-title {
    min-width: 226px;
    height: 40px;
    font-size: 16px;
    line-height: 40px;
    padding: 0 14px;
    margin-inline-end: 35px;
    background-color: var(--primary);
    color: var(--white);
    border-radius: 5px;
    font-weight: 700;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

#header .header-bottom .tf-list-categories .list-categories-inner {
    left: 0;
    border-radius: 10px;
    position: absolute;
    background-color: var(--white);
    min-width: 367px;
    z-index: 3;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    transition: 0.4s ease 0.1s;
    text-align: left;
    box-shadow: rgba(37, 61, 78, 0.08) 0px 4px 8px 0px;
    border: solid 1px var(--line);
    top: 100%;
    margin-top: 10px;
    color: var(--main);
    pointer-events: none;
}

#header .header-bottom .tf-list-categories .list-categories-inner::before {
    height: 20px;
    position: absolute;
    top: -15px;
    left: 0;
    right: 0;
    content: "";
}

#header
  .header-bottom
  .tf-list-categories
  .list-categories-inner
  .categories-bottom {
    min-height: 50px;
    padding: 15px 26px;
    background-color: rgba(37, 61, 78, 0.05);
}

#header .header-bottom .tf-list-categories:hover .list-categories-inner {
    opacity: 1;
    visibility: visible;
    transform: none;
    pointer-events: all;
}

#header .header-bottom .tf-list-categories .nav-ul-mb {
    padding-top: 24px;
    padding-inline-start: 21px;
    padding-inline-end: 20px;
    padding-bottom: 11px;
}

#header
  .header-bottom
  .tf-list-categories
  .nav-mb-item:last-child
  .tf-category-link::after {
    content: none;
}

#header
  .header-bottom
  .tf-list-categories
  .nav-mb-item:last-child
  .list-cate.show::after {
    content: none;
}

#header .header-bottom .box-left {
    display: flex;
    align-items: center;
}

#header.header-style-4 .nav-icon .nav-search {
    display: none;
}

#header.header-style-4 .header-bottom .wrapper-header {
    min-height: 78px;
}

#header.header-style-4 .header-bottom .menu-items {
    padding: 28px 0px;
}

/* ------------ footer ---------------- */
.footer .footer-wrap .footer-body {
    padding-top: 40px;
    padding-bottom: 40px;
    position: relative;
}

.footer .footer-wrap .footer-body::before {
    position: absolute;
    content: "";
    top: 0;
    left: 50%;
    background-color: var(--line);
    height: 1px;
    width: 100%;
    max-width: 1440px;
    transform: translateX(-50%);
}

.footer .footer-wrap .footer-bottom .footer-bottom-wrap {
    padding-top: 21px;
    border-top: 1px solid var(--line);
    padding-bottom: 20px;
}

.footer .footer-wrap .footer-bottom .footer-bottom-wrap .footer-menu_item {
    color: rgb(84, 84, 84);
}

.footer.has-border-full .footer-body::before {
    left: 0;
    max-width: 100%;
    transform: unset;
}

.footer.has-border-full .footer-newsletter .footer-menu_item {
    line-height: 22.4px;
}

.footer .footer-logo {
    margin-bottom: 30px;
}

.footer .footer-heading {
    margin-bottom: 25px;
}

.footer .footer-heading h6 {
    font-size: 18px;
    font-weight: 500;
    line-height: 18px;
}

.footer .footer-heading-moblie {
    display: none;
}

.footer .footer-menu_item {
    line-height: 30px;
}

.footer .footer-infor ul {
    margin-bottom: 15px;
}

.footer .footer-infor ul li p {
    line-height: 30px;
}

.footer .footer-infor ul li p a {
    color: rgb(84, 84, 84);
    font-weight: 700;
}

.footer .footer-infor > a {
    margin-bottom: 28px;
}

.footer ul.footer-menu-list li a:hover {
    color: var(--primary);
}

.footer .footer-newsletter form {
    margin-top: 27px;
    margin-bottom: 40px;
    position: relative;
}

.footer .footer-newsletter form input {
    height: 55px;
    padding-right: 143px;
}

.footer .footer-newsletter form .button-submit {
    position: absolute;
    top: 7px;
    right: 8px;
}

.footer .footer-newsletter form .button-submit button {
    height: 41px;
    padding-top: 0;
    padding-bottom: 0;
}

.footer.background-black {
    background-color: var(--main);
}

.footer.background-black .footer-wrap .footer-body::before {
    display: none;
}

.footer.background-black .footer-wrap .footer-bottom .footer-bottom-wrap {
    border-top: 1px solid rgb(48, 48, 48);
}

.footer.background-black .footer-infor ul li p {
    color: rgb(161, 161, 161);
}

.footer.background-black .footer-infor ul li p a {
    color: rgb(161, 161, 161);
}

.footer.background-black .footer-infor > a {
    margin-bottom: 28px;
    color: #a1a1a1;
}

.footer.background-black .footer-infor > a::after {
    background-color: #a1a1a1;
}

.footer.background-black .footer-infor > a:hover {
    color: var(--primary);
}

.footer.background-black .footer-infor > a:hover::after {
    background-color: var(--primary);
}

.footer.background-black .footer-heading h6 {
    color: var(--white);
}

.footer.background-black .footer-menu_item {
    color: rgb(161, 161, 161);
}

.footer.background-black .footer-newsletter form input {
    background-color: rgb(44, 44, 44);
    border: 0;
    color: var(--white);
}

.footer.background-black .footer-newsletter form input::placeholder {
    color: var(--white);
}

.footer.background-black .footer-newsletter form .tf-btn {
    border: 0;
}

.footer.bg_green-4 .footer-infor .tf-btn,
.footer.bg_green-4 .footer-infor ul li p {
    color: #606a7a;
}

.footer.bg_green-4 .footer-infor .tf-btn a,
.footer.bg_green-4 .footer-infor ul li p a {
    color: #606a7a;
}

.footer.bg_green-4 .footer-menu_item {
    color: #606a7a !important;
}

.footer.bg_green-4 .footer-newsletter form input {
    background-color: rgb(35, 42, 55);
}

.footer.background-green {
    background-color: #214332;
}

.footer.background-green * {
    border-color: rgba(235, 235, 235, 0.15);
}

.footer.background-green .footer-wrap .footer-body::before {
    display: none;
}

.footer.background-green .footer-infor ul li p {
    color: var(--white) !important;
}

.footer.background-green .footer-infor ul li p a {
    color: var(--white) !important;
}

.footer.background-green .footer-infor > a {
    margin-bottom: 28px;
}

.footer.background-green .footer-heading h6 {
    color: var(--white);
}

.footer.background-green .footer-menu_item {
    color: var(--white) !important;
}

.footer.background-green .form-newsletter input {
    border-radius: 60px;
}

.footer.background-green .form-newsletter button {
    border-radius: 60px;
    background-color: rgb(23, 46, 34);
}

.footer.background-gray {
    background-color: #f6f6f6;
}

.footer.background-gray .footer-body::before {
    content: none;
}

.footer.has-border .footer-wrap .footer-body {
    padding-top: 0px;
    padding-bottom: 0px;
    border-bottom: 1px solid var(--line);
}

.footer.has-border .footer-wrap .footer-bottom .footer-bottom-wrap {
    border-top: 0;
}

.footer.has-border .footer-newsletter {
    border-right: 1px solid var(--line);
    padding-top: 40px;
    padding-bottom: 40px;
    padding-right: 123px;
    width: 40%;
}

.footer.has-border .footer-col {
    padding-top: 40px;
    padding-bottom: 40px;
}

.footer.has-border .footer-col.footer-col-1 {
    width: 25%;
    padding-top: 40px;
    padding-right: 30px;
    padding-bottom: 60px;
    padding-left: 90px;
}

.footer.has-border .footer-col.footer-col-2 {
    width: 15%;
    padding-top: 40px;
    padding-right: 30px;
    padding-bottom: 30px;
    padding-left: 0px;
}

.footer.has-border .footer-col.footer-col-3 {
    width: 20%;
    padding-top: 40px;
    padding-right: 30px;
    padding-bottom: 30px;
    padding-left: 0px;
}

.footer.has-all-border .footer-wrap .footer-body {
    padding-top: 0px;
    padding-bottom: 0px;
    border-bottom: 1px solid var(--line);
}

.footer.has-all-border .footer-wrap .footer-bottom .footer-bottom-wrap {
    border-top: 0;
}

.footer.has-all-border .footer-newsletter {
    height: 100%;
    padding-top: 40px;
    padding-bottom: 60px;
}

.footer.has-all-border .footer-col {
    height: 100%;
    padding-top: 40px;
    padding-bottom: 60px;
    border-right: 1px solid var(--line);
}

.footer.has-all-border .tf-social-icon {
    margin-top: 20px;
}

.footer.border-container .footer-body {
    border-bottom: 0 !important;
}

.footer.border-container .footer-body::before {
    width: 1440px;
    left: 50%;
    transform: translateX(-50%);
}

.footer.border-container .footer-body::after {
    position: absolute;
    content: "";
    bottom: 0;
    left: 50%;
    background-color: var(--line);
    height: 1px;
    width: 100%;
    max-width: 1440px;
    transform: translateX(-50%);
}

.tf-cur {
    display: flex;
    gap: 28px;
}

.tf-payment {
    display: flex;
    gap: 10px;
}

.tf-toolbar-bottom {
    display: none;
    padding: 0 5px;
    overflow-x: auto;
    overflow-y: hidden;
    position: fixed;
    z-index: 200;
    bottom: 0;
    right: 0;
    left: 0;
    background-color: var(--white);
    box-shadow: 0 -4px 10px 0 rgba(0, 0, 0, 0.08);
}

.tf-toolbar-bottom .toolbar-item {
    flex: 1 0 20%;
    position: relative;
}

.tf-toolbar-bottom .toolbar-item a {
    width: 100%;
    padding-right: 10px;
    padding-left: 10px;
    height: 67px;
    gap: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.tf-toolbar-bottom .toolbar-item a .toolbar-icon {
    width: 18px;
    height: 18px;
    position: relative;
}

.tf-toolbar-bottom .toolbar-item a .toolbar-icon i {
    font-size: 18px;
    color: var(--main);
}

.tf-toolbar-bottom .toolbar-item a .toolbar-icon .toolbar-count {
    position: absolute;
    top: -6px;
    right: -12px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary);
    border-radius: 50%;
    font-size: 10px;
    font-weight: 500;
    line-height: 18px;
    color: var(--white);
}

.tf-toolbar-bottom .toolbar-item a .toolbar-label {
    font-size: 12px;
    font-weight: 600;
    line-height: 12px;
}

.tf-toolbar-bottom .toolbar-item.active::before {
    position: absolute;
    content: "";
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary);
}

.tf-toolbar-bottom .toolbar-item.active .toolbar-icon i {
    color: var(--primary);
}

/* ------------ tabs ---------------- */
.widget-tabs .widget-menu-tab .item-title:not(.default) {
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    cursor: pointer;
    position: relative;
}

.widget-tabs .widget-menu-tab .item-title:not(.default)::after {
    position: absolute;
    content: "";
    background-color: var(--main);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.widget-tabs .widget-content-tab {
    position: relative;
    overflow: hidden;
}

.widget-tabs .widget-content-tab .widget-content-inner {
    padding: 35px;
    display: block;
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    -webkit-transform: translateY(30px);
    -ms-transform: translateY(30px);
    transform: translateY(30px);
    transition-timing-function: ease-in;
    transition-duration: 0.2s;
}

.widget-tabs .widget-content-tab .widget-content-inner.active {
    pointer-events: auto;
    opacity: 1;
    visibility: visible;
    position: relative;
    z-index: 2;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    transition-timing-function: ease-out;
    transition-duration: 0.3s;
    transition-delay: 0.3s;
}

.widget-tabs.style-has-border {
    border: 1px solid var(--line);
}

.widget-tabs.style-has-border .widget-menu-tab {
    display: flex;
    gap: 10px 50px;
    margin: 0 38px;
    border-bottom: 1px solid var(--line);
    overflow-x: auto;
}

.widget-tabs.style-has-border .widget-menu-tab .item-title {
    padding: 15px 0;
    min-width: max-content;
}

.widget-tabs.style-has-border .widget-menu-tab .item-title::after {
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
}

.widget-tabs.style-has-border .widget-menu-tab .item-title.active::after {
    width: 100%;
}

.widget-tabs.style-two-col {
    display: grid;
    grid-template-columns: 3fr 9fr;
    gap: 30px;
}

.widget-tabs.style-two-col .widget-menu-tab {
    display: flex;
    flex-direction: column;
    gap: 10px 50px;
    border-left: 2px solid var(--line);
    height: max-content;
}

.widget-tabs.style-two-col .widget-menu-tab .item-title {
    padding: 15px 38px;
}

.widget-tabs.style-two-col .widget-menu-tab .item-title::after {
    top: 0;
    left: -2px;
    width: 2px;
    height: 0;
}

.widget-tabs.style-two-col .widget-menu-tab .item-title.active::after {
    height: 100%;
}

.widget-tabs.style-two-col .widget-content-tab {
    border: 1px solid var(--line);
    height: fit-content;
}

.widget-tab-2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
    gap: 15px;
}

.widget-tab-2 .nav-tab-item {
    width: 100%;
}

.widget-tab-2 .nav-tab-item a {
    display: flex;
    justify-content: center;
    width: 100%;
    color: var(--main);
    font-size: 20px;
    font-weight: 400;
    white-space: nowrap;
    padding-bottom: 10px;
    padding-inline-start: 15px;
    padding-inline-end: 15px;
    border-bottom: 2px solid rgb(238, 238, 238);
    transition: border-bottom-color 0.3s;
}

.widget-tab-2 .nav-tab-item a.active {
    border-bottom-color: var(--main);
}

.widget-tab-3 {
    margin-bottom: 30px;
    gap: 70px;
    margin-bottom: 70px;
    overflow-x: auto;
}

.widget-tab-3 .nav-tab-item a {
    display: flex;
    justify-content: center;
    width: 100%;
    font-size: 32px;
    line-height: 38.4px;
    font-weight: 400;
    white-space: nowrap;
    padding-bottom: 12px;
    border-bottom: 2px solid transparent;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    color: var(--text);
}

.widget-tab-3 .nav-tab-item a.active {
    font-weight: 500;
    border-bottom-color: var(--main);
}

.widget-tab-3 .nav-tab-item a:hover,
.widget-tab-3 .nav-tab-item a.active {
    color: var(--main);
}

.widget-tab-3::-webkit-scrollbar {
    width: 0px;
    height: 0px;
}

.widget-tab-3.style-3 {
    gap: 30px;
}

.widget-tab-3.style-3 .nav-tab-item a {
    font-weight: 500;
}

.widget-tab-3.style-3 .nav-tab-item a.active {
    font-weight: 600;
}

.widget-tab-4 {
    border-radius: 10px;
    border: 1px solid var(--main);
    gap: 20px;
    padding: 20px 15px;
    display: flex;
}

.widget-tab-4 .nav-tab-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 5px 0px;
    min-height: 30px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer;
}

.widget-tab-4 .nav-tab-link .icon {
    font-size: 10px;
    font-weight: 500;
    display: none;
}

.widget-tab-4 .nav-tab-link span {
    font-size: 15px;
    line-height: 18px;
    font-weight: 500;
    display: flex;
    gap: 5px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
}

.widget-tab-4 .nav-tab-link .count {
    font-weight: 400;
    margin-top: -2px;
}

.widget-tab-4 .nav-tab-link.active .icon {
    display: block;
}

.widget-tab-5 {
    display: flex;
    align-items: center;
    gap: 30px;
    overflow-x: auto;
}

.widget-tab-5 .nav-tab-item {
    width: 100%;
}

.widget-tab-5 .nav-tab-item a {
    display: flex;
    justify-content: center;
    width: 100%;
    color: var(--text);
    font-size: 14px;
    line-height: 16.8px;
    font-weight: 500;
    white-space: nowrap;
    padding: 6px 0px;
    border: 2px solid transparent;
}

.widget-tab-5 .nav-tab-item a.active {
    padding: 6px 20px;
    border-radius: 60px;
    border-color: var(--primary);
    color: var(--primary);
}

.widget-tab-5 .nav-tab-item a:hover {
    color: var(--primary);
}

.flat-animate-tab {
    overflow: hidden;
}

.flat-animate-tab .tab-content {
    position: relative;
}

.flat-animate-tab .tab-pane {
    display: block;
    pointer-events: none;
    opacity: 0;
    visibility: hidden;
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    -webkit-transform: translateY(30px);
    -ms-transform: translateY(30px);
    transform: translateY(30px);
    transition-timing-function: ease-in;
    transition-duration: 0.2s;
}

.flat-animate-tab .tab-pane.active {
    pointer-events: auto;
    opacity: 1;
    visibility: visible;
    position: relative;
    z-index: 2;
    -webkit-transform: none;
    -ms-transform: none;
    transform: none;
    transition-timing-function: ease-out;
    transition-duration: 0.3s;
    transition-delay: 0.3s;
}

/* ------------ slider banner ---------------- */
.wrap-slider {
    position: relative;
}

.wrap-slider img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wrap-slider .box-content {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.wrap-slider .box-content .heading,
.wrap-slider .box-content h1 {
    margin-bottom: 18px;
}

.wrap-slider .box-content .heading.mb_38,
.wrap-slider .box-content h1.mb_38 {
    margin-bottom: 38px;
}

.wrap-slider .box-content p {
    margin-bottom: 48px;
    font-size: 20px;
    line-height: 24px;
}

.wrap-slider .box-content p.mb_15 {
    margin-bottom: 15px;
}

.wrap-slider .box-content p.sale_off {
    background-color: #ff5c39;
    padding: 5px 20px;
    width: max-content;
    margin-left: auto;
    margin-right: auto;
    font-size: 14px;
}

.tf-slideshow {
    overflow: hidden;
}

.tf-slideshow .wrap-pagination {
    position: absolute;
    z-index: 10;
    bottom: 57px;
    left: 0;
    right: 0;
}

.tf-slideshow .sw-absolute-3 {
    right: 3%;
    left: unset;
    width: auto;
}

.tf-slideshow .sw-absolute-3 .sw-dots {
    flex-direction: column;
}

.tf-slideshow .card-box {
    padding: 64px 48px 70px;
    border-radius: 10px;
    max-width: 550px;
}

.tf-slideshow .card-box .heading {
    margin-bottom: 38px;
}

.tf-slideshow .subheading {
    margin-bottom: 14px;
    font-size: 14px;
    line-height: 22.4px;
}

.slider-skincare .sw-absolute-3 {
    top: 50%;
    transform: translateY(-50%);
    bottom: unset;
}

.slider-effect-fade .swiper-slide .fade-item {
    transform: translateY(100px);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.slider-effect-fade .swiper-slide .fade-item.fade-box {
    transition-delay: 0.4s;
}

.slider-effect-fade .swiper-slide .fade-item.fade-item-1 {
    transition-delay: 0.5s;
}

.slider-effect-fade .swiper-slide .fade-item.fade-item-2 {
    transition-delay: 0.6s;
}

.slider-effect-fade .swiper-slide .fade-item.fade-item-3 {
    transition-delay: 0.7s;
}

.slider-effect-fade .swiper-slide .fade-item.fade-item-4 {
    transition-delay: 0.8s;
}

.slider-effect-fade .swiper-slide.swiper-slide-active .fade-item {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.slider-giftcard .wrap-slider {
    height: 350px;
}

.slider-giftcard .wrap-slider .heading {
    margin-bottom: 18px;
    font-weight: 500;
}

.hover-sw-nav .sw-dots {
    margin-top: 15px;
    position: relative;
    z-index: 10;
    display: none;
    transform: unset !important;
    bottom: unset;
}

.slider-effect {
    position: relative;
}

.slider-effect.wrap-slider {
    height: auto !important;
}

.slider-effect .content-left {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    background-color: rgb(238, 241, 224);
}

.slider-effect .content-left .box-content {
    position: unset;
    transform: unset;
}

.slider-effect .content-left img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slider-effect .img-slider {
    width: 50%;
    margin-left: auto;
}

.swiper-slide .box-content {
    opacity: 0;
}

.swiper-slide.swiper-slide-active .box-content {
    opacity: 1;
}

.slideshow-effect-zoom {
    padding-top: 74px;
    padding-bottom: 100px;
}

.slideshow-effect-zoom .content-left {
    padding: 20px;
    padding-left: 0;
}

.slideshow-effect-zoom .content-left .desc {
    margin-top: 18px;
    font-size: 20px;
    line-height: 32px;
    color: rgb(84, 84, 84);
}

.slideshow-effect-zoom .content-left .tf-btn {
    margin-top: 48px;
}

.slideshow-effect-zoom .wrap-content {
    display: flex;
    align-items: center;
}

.slideshow-effect-zoom .wrap-content .content-left,
.slideshow-effect-zoom .wrap-content .content-right {
    width: 50%;
}

.grid-img-group {
    position: relative;
}

.grid-img-group .item-1 {
    position: absolute;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    z-index: 1;
    width: 51%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.grid-img-group .item-1:hover {
    z-index: 2;
}

.grid-img-group .item-2 {
    margin-left: auto;
    width: 84%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.grid-img-group .item-2:hover {
    z-index: 2;
    position: relative;
}

.grid-img-group .img-style {
    border-radius: 10px;
}

.grid-img-group.style-ter-1 {
    padding-bottom: 66px;
}

.grid-img-group.style-ter-1 .item-1 {
    width: 53%;
    align-items: end;
}

.grid-img-group.style-ter-1 .item-2 {
    width: 79%;
}

.tf-slideshow .box-content .subheading {
    margin-bottom: 14px;
}

.tf-slideshow .banner-wrapper {
    position: relative;
}

.tf-slideshow .banner-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-slideshow .banner-wrapper .box-content {
    position: absolute;
    left: 15px;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

.tf-slideshow .banner-wrapper .description {
    margin-top: 20px;
    font-size: 16px;
    line-height: 19.2px;
}

.tf-slideshow .banner-wrapper .tf-btn {
    margin-top: 30px;
}

.tf-slideshow.about-us-page .text {
    font-size: 68px;
    line-height: 81.6px;
}

.tf-slideshow.about-us-page img {
    max-height: 860px;
}

.tf-slideshow .text-in-right .box-content {
    left: unset;
}

.slider-video .tf-btn {
    margin-top: 28px;
}

.slider-radius {
    padding: 0 40px;
}

.slider-radius .tf-sw-slideshow {
    border-radius: 60px;
}

.slider-radius .tf-sw-slideshow .heading {
    margin-bottom: 38px;
}

.slider-radius .tf-sw-slideshow .subheading {
    font-size: 14px;
    line-height: 22.4px;
}

.slider-radius .row-end .box-content {
    left: unset;
}

.autoplay-linear .swiper-wrapper {
    transition-timing-function: linear;
}

.slideshow-men .tf-sw-slideshow {
    padding-top: 60px;
    margin-top: -60px;
}

.slideshow-men .wrap-slider {
    position: relative;
}

.slideshow-men .lookbook-1 .lookbook-item {
    position: absolute;
}

.slideshow-men .lookbook-1 .item-1 {
    top: 8%;
    left: 60%;
}

.slideshow-men .lookbook-1 .item-2 {
    top: 35%;
    left: 50%;
}

.slideshow-men .lookbook-2 .lookbook-item {
    position: absolute;
}

.slideshow-men .lookbook-2 .item-1 {
    top: 36%;
    left: 50%;
}

.slideshow-men .wrap-pagination {
    bottom: 45px;
}

.slideshow-lookbook .wrap-slider {
    position: relative;
}

.slideshow-lookbook .lookbook-1 .lookbook-item {
    position: absolute;
}

.slideshow-lookbook .lookbook-1 .item-1 {
    top: 39%;
    left: 35%;
}

.slideshow-lookbook .lookbook-1 .item-2 {
    top: 59%;
    left: 60%;
}

.slideshow-lookbook .lookbook-1 .item-3 {
    top: 26%;
    left: 74%;
}

.slideshow-lookbook .wrap-pagination {
    bottom: 45px;
}

.banner-collection-men-wrap {
    position: relative;
}

.banner-collection-men-wrap .box-content {
    position: absolute;
    left: 15px;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
}

.banner-collection-men-wrap .card-box {
    background-color: var(--white);
    border-radius: 10px;
    padding: 20px 15px;
}

.banner-collection-men-wrap .card-box .subheading {
    font-size: 14px;
    line-height: 16.8px;
    font-weight: 700;
}

.banner-collection-men-wrap .card-box .heading {
    font-weight: 400;
    font-size: 24px;
    line-height: 28.8px;
    margin-top: 16px;
}

.banner-collection-men-wrap .card-box .text {
    margin-top: 20px;
    font-size: 16px;
    line-height: 25.6px;
    color: var(--text);
}

.banner-collection-men-wrap .card-box .tf-btn {
    margin-top: 20px;
}

.banner-collection-men-wrap .img-wrap {
    height: 450px;
    width: 100%;
}

.banner-collection-men-wrap .img-wrap img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-parallax {
    height: 350px;
    background-attachment: fixed;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

.slider-skateboard .wrap-slider .box-content {
    top: unset;
    transform: unset;
    bottom: 10%;
}

.tf-slideshow .card-box-2 {
    max-width: 400px;
}

.tf-slideshow .card-box-2 .title {
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 16px;
    line-height: 19.2px;
}

.tf-slideshow .card-box-2 .price {
    font-size: 20px;
    line-height: 20px;
    font-weight: 500;
    margin-bottom: 20px;
    display: inline-block;
}

.flat-testimonial-bg {
    padding: 40px 0px 30px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

.flat-testimonial-bg-1 {
    background-image: url(../images/slider/image_bg_testimonials.jpg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.height-auto {
    height: auto;
}

.height-auto > .h-100 {
    height: 100% !important;
}

.slideshow-tee .wrap-slider .box-content .heading {
    font-size: 34px;
    line-height: 40.8px;
}

.slideshow-tee .wrap-slider .box-content p {
    font-size: 34px;
    line-height: 40.8px;
}

.slideshow-tee.tf-slideshow .wrap-slider {
    height: 400px;
}

.flat-testimonial-bg-v2 {
    padding: 50px 0px 40px;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: cover;
}

.flat-testimonial-bg-v2 .wrap-content-left .rating {
    margin-bottom: 10px;
}

.flat-testimonial-bg-v2 .wrap-content-right {
    margin-top: 15px;
}

.flat-testimonial-bg-v2 .wrap-content-right .box-sw-navigation {
    margin-top: 30px;
}

.slider-sock .img-mb {
    display: none;
}

.slider-accessories {
    position: relative;
}

.slider-accessories .wrap-slider .box-content {
    top: 10%;
    transform: unset;
}

.slider-accessories .wrap-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    z-index: 50;
}

.slider-accessories .nav-sw {
    position: absolute;
}

.slider-accessories .nav-next-slider {
    left: 6%;
}

.slider-accessories .nav-prev-slider {
    right: 6%;
}

/* ------------ button ---------------- */
.tf-btn {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    will-change: background-color, color, border;
    pointer-events: auto;
    overflow: hidden;
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    box-sizing: border-box;
    padding: 14px 24px;
    display: inline-flex;
    border-radius: 3px;
    align-items: center;
    border: 1px solid transparent;
    background-color: transparent;
    cursor: pointer;
}

.tf-btn.hover-icon .icon {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    transform: scale(0);
    transform-origin: right;
    width: 0;
    display: inline-block;
    font-size: 9px;
}

.tf-btn.hover-icon:hover .icon {
    transform: scale(1);
    width: 10px;
    min-width: 10px;
    margin-inline-start: 8px;
}

.tf-btn span {
    z-index: 1;
    color: inherit;
}

.tf-btn.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.tf-btn.btn-primary-main {
    background-color: var(--primary);
    color: var(--main);
}

.tf-btn.style-2 {
    padding: 0 24px;
    line-height: 42px;
}

.tf-btn.style-3 {
    padding: 0 24px;
    line-height: 40px;
}

.tf-btn.btn-color-1 {
    background-color: #fcf151;
    border-color: var(--main);
}

.tf-btn.btn-color-2 {
    background-color: #c7fcff;
    border-color: var(--main);
}

.tf-btn.btn-color-3 {
    background-color: #ffb1d2;
    border-color: var(--main);
}

.tf-btn.btn-color-4 {
    background-color: #c1fb9b;
    border-color: var(--main);
}

.tf-btn.btn-color-5 {
    background-color: #e09314;
    color: var(--white);
}

.tf-btn.btn-color-6 {
    background-color: #b46161;
    color: var(--white);
}

.btn-icon .icon {
    margin-inline-start: 8px;
    display: inline-block;
    font-size: 9px;
}

.btn-xl {
    padding: 0px 30px;
    font-size: 18px;
    line-height: 50px;
}

.btn-xl .icon {
    font-weight: 600;
    margin-inline-start: 12px;
    font-size: 12px;
}

.btn-md {
    padding: 0 30px;
    min-height: 46.8px;
}

.btn-sm {
    padding-left: 18px;
    padding-right: 18px;
}

.btn-fill {
    background-color: var(--main);
    border: 1px solid var(--main);
    color: var(--white);
}

.btn-outline {
    background-color: var(--white);
    border: 1px solid var(--main);
}

.btn-outline-2 {
    background-color: var(--white);
    border: 1px solid #ebebeb;
}

.btn-outline-dark {
    background-color: transparent;
    border: 1px solid var(--main);
    color: var(--main);
    font-weight: 400;
    gap: 12px;
}

.btn-outline-dark i {
    font-size: 10px;
}

.btn-outline-dark:hover {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-light {
    background-color: transparent;
    border: 1px solid var(--white);
    color: var(--white);
}

.btn-outline-light:hover {
    color: var(--primary);
    border-color: var(--primary);
}

.fill-outline-light {
    background-color: transparent;
    border: 1px solid var(--white);
    color: var(--white);
}

.fill-outline-light:hover {
    color: var(--main);
    border-color: var(--white);
    background-color: var(--white);
}

.btn-line {
    padding: 0;
    padding-bottom: 7px;
    color: var(--main);
    position: relative;
}

.btn-line .icon {
    margin-inline-start: 8px;
    display: inline-block;
    font-size: 8px;
}

.btn-line::after {
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    background-color: var(--main);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.btn-line:hover {
    color: var(--primary);
}

.btn-line:hover::after {
    background-color: var(--primary);
}

.btn-line.collection-other-link {
    padding-bottom: 4px;
}

.btn-line-light {
    color: var(--white);
}

.btn-line-light::after {
    background-color: var(--white);
}

.btn-light {
    background-color: var(--white);
}

.btn-light .icon {
    color: var(--white);
}

.btn-light:hover {
    background-color: var(--main);
    color: var(--white);
}

.btn-light-icon {
    background-color: var(--white);
}

.btn-fill-line {
    border: 1px solid var(--main) !important;
}

.btn-fill-line:hover {
    border-color: transparent !important;
}

.animate-hover-btn {
    position: relative;
    overflow: hidden;
}

.animate-hover-btn:after {
    background-image: linear-gradient(
    90deg,
    transparent,
    rgba(0, 0, 0, 0.25),
    transparent
  );
    content: "";
    left: 150%;
    position: absolute;
    top: 0;
    bottom: 0;
    transform: skew(-20deg);
    width: 200%;
}

.animate-hover-btn.btn-color-6:after,
.animate-hover-btn.btn-color-5:after,
.animate-hover-btn.btn-primary:after,
.animate-hover-btn.btn-fill::after {
    background-image: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.25),
    transparent
  );
}

@keyframes shine {
    100% {
        left: -200%;
    }
}

.tf-loading-default,
.tf-btn-loading {
    position: relative;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-loading-default.loading,
.tf-btn-loading.loading {
    background-color: var(--main);
}

.tf-loading-default.loading .icon,
.tf-loading-default.loading i,
.tf-btn-loading.loading .icon,
.tf-btn-loading.loading i {
    display: none;
}

.tf-loading-default.loading::before,
.tf-btn-loading.loading::before {
    border: solid 2px var(--white);
    opacity: 1;
    animation-play-state: running;
}

.tf-loading-default::before,
.tf-btn-loading::before {
    width: 18px;
    height: 18px;
    border: solid 2px transparent;
    border-top-color: transparent !important;
    content: "";
    position: absolute;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    border-radius: 50%;
    animation: tf_rotator 0.6s linear infinite paused;
    opacity: 0;
}

.tf-loading-default {
    width: auto;
    height: 42px;
    min-width: 118px;
    border: 1px solid var(--line);
    background-color: var(--white);
    border-radius: 3px;
}

.tf-loading-default.loading {
    background-color: var(--white);
}

.tf-loading-default.loading::before {
    border-color: var(--main);
}

.tf-loading-default.loading .text {
    display: none;
}

.tf-loading-default .text {
    font-size: 14px;
    color: var(--main);
    font-weight: 400;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-loading-default.style-2 {
    height: 38px;
    border-color: var(--main);
}

.tf-loading-default.style-2 .text {
    font-weight: 600;
}

.tf-loading-default.style-2.loading {
    border-color: var(--primary);
}

.tf-loading-default.style-2.loading::before {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-color: var(--primary);
}

.tf-loading-default.style-2:hover {
    border-color: var(--primary);
}

.tf-loading-default.style-2:hover .text {
    color: var(--primary);
}

@keyframes tf_rotator {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.tf-btn-filter {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 10px;
    color: var(--main);
    border: solid 1px var(--line-2);
    border-radius: 3px;
    text-transform: uppercase;
    line-height: 22px;
    padding: 6px 8px;
    font-size: 12px;
    font-weight: 600;
    max-width: 100%;
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ------------ range slider ---------------- */
.noUi-target,
.noUi-target * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -ms-touch-action: none;
    touch-action: none;
    -ms-user-select: none;
    -moz-user-select: none;
    user-select: none;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    cursor: pointer;
}

.noUi-target {
    position: relative;
    direction: ltr;
}

.noUi-base {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    background: rgb(235, 235, 235);
}

.noUi-origin {
    position: absolute;
    right: 0;
    top: 0;
    left: 0;
    bottom: 0;
}

.noUi-handle {
    position: relative;
    z-index: 1;
}

.noUi-stacking .noUi-handle {
    z-index: 10;
}

.noUi-state-tap .noUi-origin {
    -webkit-transition: left 0.3s, top 0.3s;
    transition: left 0.3s, top 0.3s;
}

.noUi-state-drag * {
    cursor: inherit !important;
}

.noUi-base,
.noUi-handle {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
}

.noUi-horizontal {
    height: 3px;
}

.noUi-horizontal .noUi-handle {
    position: relative;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    left: 0px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary);
    border: 2px solid transparent;
    cursor: pointer;
}

.caption {
    margin-bottom: 10px;
}

/* Styling; */
.noUi-background {
    background: rgb(235, 235, 235);
}

.noUi-connect {
    background: var(--primary);
    -webkit-transition: background 450ms;
    transition: background 450ms;
}

.noUi-origin {
    border-radius: 0px;
}

.noUi-target {
    width: 100%;
    padding-right: 15px;
}

/* Handles and cursors; */
.noUi-draggable {
    cursor: w-resize;
}

.noUi-vertical .noUi-draggable {
    cursor: n-resize;
}

.noUi-handle {
    cursor: default;
    -webkit-box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
    box-sizing: border-box !important;
}

/* Disabled state; */
[disabled].noUi-connect,
[disabled] .noUi-connect {
    background: #b8b8b8;
}

[disabled].noUi-origin,
[disabled] .noUi-handle {
    cursor: not-allowed;
}

.slider-labels .caption {
    font-weight: 500;
    font-size: 16px;
}

/* ------------ form ---------------- */
.form-newlleter h4 {
    margin-bottom: 15px;
}

.form-newlleter p {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 45px;
}

.form-newlleter form > div:first-child {
    display: flex;
    gap: 9px;
    max-width: 562px;
    margin-left: auto;
    margin-right: auto;
}

.wrap-form-newlleter {
    height: 565px;
    padding-top: 112px;
    padding-left: 92px;
    background-image: url(../images/slider/fashion-slideshow-08.jpg);
}

.wrap-form-newlleter .form-newlleter {
    background-color: var(--white);
    max-width: 613px;
    padding: 48px 54px;
}

.wrap-form-newlleter .form-newlleter form > div:first-child {
    margin-left: unset;
    margin-right: unset;
}

.wrap-form-newlleter.style-half {
    background-image: unset;
    height: unset;
    padding: 0;
    display: flex;
}

.wrap-form-newlleter.style-half .form-newlleter {
    background-color: #f3f3f3;
    width: 100%;
    max-width: unset;
    padding-top: 107px;
    padding-bottom: 107px;
}

.wrap-form-newlleter.style-half .image {
    width: 100%;
}

.wrap-form-newlleter.style-half .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-select {
    display: inline-block;
    padding: 6px 30px 6px 15px;
    color: var(--text-2);
    border-radius: 3px;
    border: 1px solid var(--line);
    height: 42px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    outline: none;
}

.tf-select:focus-visible {
    border: 1px solid var(--main);
}

.tf-product-bundle-variant {
    position: relative;
}

.tf-product-bundle-variant .tf-select {
    width: 100%;
    appearance: none;
    background-color: transparent;
}

.tf-product-bundle-variant::after {
    font-family: "icomoon";
    position: absolute;
    content: "\e904";
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 6px;
    z-index: -1;
}

.tf-field {
    position: relative;
}

.tf-field .tf-input {
    padding: 13px 10px;
}

.tf-field .tf-input:focus {
    border-color: rgb(134, 134, 134);
}

.tf-field .tf-input:not(:placeholder-shown) ~ .tf-field-label,
.tf-field .tf-input:focus ~ .tf-field-label {
    border-radius: 3px;
    background-color: rgb(242, 242, 242);
    font-size: 12px;
    top: 0;
    letter-spacing: 0.4px;
    padding: 0 8px;
}

.tf-field .tf-input::placeholder {
    color: transparent;
}

.tf-field .tf-field-label {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    transition: all 0.1s ease;
    cursor: text;
}

.tf-field.style-1 .tf-input {
    padding: 25px 18px 6px;
    height: 50px;
    border: 1px solid var(--line) !important;
}

.tf-field.style-1 .tf-input:not(:placeholder-shown) ~ .tf-field-label,
.tf-field.style-1 .tf-input:focus ~ .tf-field-label {
    top: 4px;
    left: 14px;
    transform: scale(0.8);
    background-color: transparent;
    padding: 0;
    font-size: 14px;
}

.tf-field.style-1 .tf-field-label {
    font-weight: 400;
    color: var(--text-2);
    left: 18px;
}

.tf-check {
    position: relative;
    background: transparent;
    cursor: pointer;
    outline: 0;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    min-width: 16px;
    border: 1px solid var(--line);
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

.tf-check:checked {
    border-color: var(--primary);
    background-color: var(--primary);
}

.tf-check:checked::before {
    opacity: 1;
    transform: scale(1);
}

.tf-check::before {
    font-family: "icomoon";
    content: "\e911";
    position: absolute;
    color: var(--white);
    opacity: 0;
    font-size: 8px;
    transform: scale(0);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-check-color {
    position: relative;
    cursor: pointer;
    outline: 0;
    -webkit-appearance: none;
    width: 26px;
    height: 26px;
    min-width: 16px;
    border: 1px solid transparent;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    transition: box-shadow 0.25s cubic-bezier(0, 0, 0.44, 1.18);
}

.tf-check-color:checked::before {
    opacity: 1;
    transform: scale(1);
}

.tf-check-color::before {
    font-family: "icomoon";
    content: "\e911";
    position: absolute;
    color: var(--white);
    opacity: 0;
    font-size: 8px;
    transform: scale(0);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-check-color.bg_white {
    border-color: var(--line);
}

.tf-check-color.bg_white::before {
    color: var(--main);
}

.tf-dropdown-sort {
    border-radius: 3px;
    padding: 6px 8px;
    min-width: 100px;
    border: 1px solid var(--line-2);
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-dropdown-sort .btn-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
}

.tf-dropdown-sort .btn-select .icon {
    font-size: 7px;
}

.tf-dropdown-sort .text-sort-value {
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 14px;
    line-height: 22px;
}

.tf-dropdown-sort .dropdown-menu {
    margin: 6px !important;
    margin-top: 10px !important;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1019607843);
    min-width: 180px;
    border: 0;
    padding-top: 15px;
    padding-bottom: 15px;
    border-radius: 0;
    max-height: 68vh;
    isolation: isolate;
    overscroll-behavior-y: contain;
    overflow-y: auto;
}

.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar {
    width: 5px;
}

.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar-track {
    background-color: var(--bg-scrollbar-track);
}

.tf-dropdown-sort .dropdown-menu::-webkit-scrollbar-thumb {
    background: var(--bg-scrollbar-thumb);
    border-radius: 4px;
}

.tf-dropdown-sort .select-item {
    position: relative;
    font-size: 14px;
    font-weight: 500;
    color: var(--text);
    padding: 0 20px;
    line-height: 30px;
    width: 100%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-dropdown-sort .select-item::after {
    height: 100%;
    content: "";
    width: 6px;
    position: absolute;
    z-index: 2;
    left: 3px;
    top: 0;
    background-color: var(--white);
}

.tf-dropdown-sort .select-item::before {
    left: 0;
    width: 3px;
    height: 100%;
    content: "";
    position: absolute;
    z-index: 1;
    top: 0;
    background-color: var(--white);
    border-radius: 0 3px 3px 0;
}

.tf-dropdown-sort .select-item.active {
    background-color: var(--bg-11);
    color: var(--main);
}

.tf-dropdown-sort .select-item.active::before {
    background-color: var(--main);
}

.tf-dropdown-sort .select-item:hover {
    background-color: var(--bg-11);
    color: var(--main);
}

.tf-dropdown-sort:hover {
    border-color: var(--main);
}

.tf-dropdown-sort.full .dropdown-menu {
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.tf-dropdown-sort.full .select-item {
    line-height: 40px;
}

.tf-dropdown-sort.has-color .select-item {
    display: flex;
    gap: 6px;
    align-items: center;
}

.tf-dropdown-sort.has-color .box-color {
    width: 15px;
    height: 15px;
}

.tf-product-form {
    margin-top: 24px;
    border-radius: 5px;
    background-color: var(--bg-11);
    padding: 25px 28px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.tf-product-form p {
    font-size: 12px;
    line-height: 19px;
}

.tf-product-form .tf-field textarea {
    height: 163px;
    padding-left: 20px;
}

.tf-product-form .tf-field textarea + label {
    top: 20px;
}

.tf-product-form .tf-field input {
    padding-left: 20px;
}

.tf-product-form .tf-field input[type="date"] {
    box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 16px 0px;
    width: 213px;
    height: 68px;
    padding-top: 28px;
    color: var(--main);
}

.tf-product-form .tf-field input[type="date"] + label {
    top: 20px;
}

.tf-product-form .tf-field label {
    left: 20px;
    color: var(--text-3);
    font-size: 12px;
}

#subscribe-form textarea {
    height: 235px;
}

#subscribe-form.mw-705 button {
    min-width: 215px;
}

#recover:target ~ #login,
#recover {
    display: none;
}

#recover:target {
    display: block;
}

.select-custom {
    position: relative;
}

.select-custom .tf-select {
    appearance: none;
    background-color: transparent;
}

.select-custom::after {
    font-family: "icomoon";
    position: absolute;
    content: "\e904";
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 6px;
    z-index: -1;
}

.flat-subscrite-wrap .form-newsletter {
    max-width: 520px;
    margin: auto;
}

.flat-subscrite-wrap .subscribe-content {
    display: flex;
    align-items: center;
    gap: 9px;
}

.flat-subscrite-wrap .subscribe-content .email {
    flex-grow: 1;
}

.flat-subscrite-wrap .subscribe-content input {
    padding-top: 10px;
    padding-bottom: 10px;
    border-radius: 0;
}

.flat-subscrite-wrap .subscribe-content input:focus {
    border: 1px solid var(--main);
}

.tf-form-search {
    position: relative;
}

.tf-form-search .search-box {
    position: relative;
}

.tf-form-search .search-box input {
    font-size: 16px;
    padding-right: 40px;
    color: var(--main);
    border-radius: 60px;
    padding-top: 11px;
    padding-bottom: 11px;
}

.tf-form-search .search-box .tf-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px;
    background-color: var(--main);
    border-radius: 60px;
    width: 81px;
    height: 36px;
    justify-content: center;
}

.tf-form-search .search-box .tf-btn .icon {
    font-size: 18px;
    color: var(--white);
}

.tf-form-search .search-box .tf-btn:hover {
    background-color: var(--primary);
}

.tf-form-search .search-suggests-results {
    border-radius: 10px;
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    left: 0;
    z-index: 1000;

    /* width: 1600px; */
    height: auto;
    background-color: var(--white);
    opacity: 0;
    visibility: hidden;
    transition: all 0.1s ease-in-out;
    max-width: 100%;
    box-shadow: 0 1px 5px 2px #ebebeb;
    pointer-events: none;
}

.tf-form-search .search-suggests-results .search-suggests-results-inner {
    overflow: auto;
    overflow-x: hidden;
    padding: 20px;
    max-height: calc(95vh - 183px);
}

.tf-form-search
  .search-suggests-results
  .search-suggests-results-inner::-webkit-scrollbar {
    width: 5px;
    border-radius: 5px;
}

.tf-form-search
  .search-suggests-results
  .search-suggests-results-inner::-webkit-scrollbar-track {
    background-color: var(--line);
}

.tf-form-search
  .search-suggests-results
  .search-suggests-results-inner::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.16);
    border-radius: 10px;
}

.tf-form-search:hover .search-suggests-results {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
}

.tf-form-search .search-suggests-results-inner .search-result-item {
    display: flex;
    gap: 15px;
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .box-content {
    flex-grow: 1;
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .box-content
  .title {
    color: #000000;
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .box-content
  .price {
    color: #000000;
    font-weight: 600;
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .box-content
  .old-price {
    text-decoration: line-through;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.55);
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .box-content
  .new-price {
    color: rgb(219, 18, 21);
    font-weight: 600;
}

.tf-form-search .search-suggests-results-inner .search-result-item .img-box {
    width: 60px;
    height: 84px;
}

.tf-form-search
  .search-suggests-results-inner
  .search-result-item
  .img-box
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-form-search
  .search-suggests-results-inner
  li:not(:last-child)
  .search-result-item {
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: solid 1px var(--line);
}

.wd-form-address {
    margin: 20px 0px 40px;
    border-radius: 10px;
    padding: 15px;
    border: 1px solid var(--line);
}

.wd-form-address .title {
    font-size: 28px;
    line-height: 33.6px;
    margin: 20px 0px;
}

.wd-form-address .box-field {
    margin-bottom: 15px;
}

.wd-form-address .tf-select {
    height: 50px;
}

.wd-form-order {
    padding: 15px;
    border-radius: 10px;
    border: 1px solid var(--line);
}

.wd-form-order .order-head {
    display: flex;
    align-items: center;
    border-bottom: 1px dashed var(--line);
    padding-bottom: 20px;
    margin-bottom: 30px;
    gap: 12px;
}

.wd-form-order .order-head .img-product {
    width: 80px;
    height: 80px;
    border: 1px solid var(--line);
    border-radius: 3px;
}

.wd-form-order .order-head .img-product img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.badge {
    padding: 5px 10px;
    font-weight: 500;
    background-color: var(--primary);
    color: white;
    border-radius: 4px;
    font-size: 12px;
    min-width: 22px;
    min-height: 22px;
    text-transform: uppercase;
    text-align: center;
}

/* ------------ nice select ---------------- */
.nice-select {
    -webkit-tap-highlight-color: transparent;
    background-color: #fff;
    border: 0;
    padding: 0;
    padding-right: 16px;
    box-sizing: border-box;
    clear: both;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
    outline: none;
    position: relative;
    transition: all linear 0.2s;
    user-select: none;
    white-space: nowrap;
    width: max-content;
    border-radius: 0;
    color: var(--main);
}

.nice-select:active,
.nice-select.open,
.nice-select:focus {
    border-color: var(--line);
}

.nice-select:after {
    border-bottom: 1.7px solid var(--main);
    border-right: 1.7px solid var(--main);
    content: "";
    height: 8px;
    width: 8px;
    margin-top: -6px;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform-origin: 66% 66%;
    -ms-transform-origin: 66% 66%;
    transform-origin: 66% 66%;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition: all 0.15s ease-in-out;
    transition: all 0.15s ease-in-out;
}

.nice-select.open:after {
    -webkit-transform: rotate(-135deg);
    -ms-transform: rotate(-135deg);
    transform: rotate(-135deg);
    -moz-transform: rotate(-135deg);
    -o-transform: rotate(-135deg);
}

.nice-select.open .list {
    opacity: 1;
    z-index: 10;
    pointer-events: auto;
    -webkit-transform: scale(1) translateY(0);
    -ms-transform: scale(1) translateY(0);
    transform: scale(1) translateY(0);
    width: 100%;
    -moz-transform: scale(1) translateY(0);
    -o-transform: scale(1) translateY(0);
}

.nice-select.disabled {
    border-color: #ededed;
    color: #999;
    pointer-events: none;
}

.nice-select.disabled:after {
    border-color: #cccccc;
}

.nice-select.wide {
    width: 100%;
}

.nice-select.wide .list {
    left: 0 !important;
    right: 0 !important;
}

.nice-select.right {
    float: right;
}

.nice-select.right .list {
    left: auto;
    right: 0;
}

.nice-select.small {
    font-size: 12px;
    height: 36px;
    line-height: 34px;
}

.nice-select.small:after {
    height: 4px;
    width: 4px;
}

.nice-select.small .option {
    line-height: 34px;
    min-height: 34px;
}

.nice-select .list {
    background-color: var(--white);
    border-radius: 5px;
    box-shadow: 0 0 0 1px rgba(68, 68, 68, 0.11);
    box-sizing: border-box;
    margin-top: 4px;
    opacity: 0;
    overflow: hidden;
    padding: 0;
    pointer-events: none;
    position: absolute;
    top: 100%;
    left: 0;
    -webkit-transform-origin: 50% 0;
    -ms-transform-origin: 50% 0;
    transform-origin: 50% 0;
    -webkit-transform: scale(0.75) translateY(-21px);
    -ms-transform: scale(0.75) translateY(-21px);
    transform: scale(0.75) translateY(-21px);
    -webkit-transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25),
    opacity 0.15s ease-out;
    transition: all 0.2s cubic-bezier(0.5, 0, 0, 1.25), opacity 0.15s ease-out;
    z-index: 9;
    width: 100%;
    font-size: 14px;
    max-height: 155px;
    overflow: auto;
}

.nice-select .list.style {
    max-height: unset;
}

.nice-select .list::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f5f5f5;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar-thumb {
    background-color: #a7a7a7;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
}

.nice-select .list::-webkit-scrollbar {
    width: 6px;
    height: 4px;
    background-color: #f5f5f5;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
}

.nice-select .option {
    cursor: pointer;
    font-weight: 500;
    line-height: 40px;
    list-style: none;
    min-height: 40px;
    outline: none;
    padding-left: 18px;
    padding-right: 29px;
    font-size: 16px;
    text-align: left;
    -webkit-transition: all 0.2s;
    transition: all 0.2s;
    color: var(--main);
}

.nice-select .option:hover,
.nice-select .option.focus,
.nice-select .option.selected.focus {
    background-color: var(--white);
    color: var(--primary);
}

.nice-select .option.selected {
    font-weight: 600;
}

.nice-select .option.disabled {
    color: var(--main);
    cursor: default;
}

.no-csspointerevents .nice-select .list {
    display: none;
}

.no-csspointerevents .nice-select.open .list {
    display: block;
}

.dropdown.bootstrap-select.image-select.style-default {
    width: unset;
}

.dropdown.bootstrap-select.image-select.style-default > button {
    padding: 0;
    padding-right: 17px;
    background-color: transparent;
    border: 0;
    outline: none !important;
    color: var(--main);
}

.dropdown.bootstrap-select.image-select.style-default > button::after {
    border: 0;
    position: absolute;
    right: 0;
    content: "\e904";
    font-family: "icomoon";
    font-size: 6px;
    color: var(--main);
    margin: -3px 0 0 0;
}

.dropdown.bootstrap-select.image-select.style-default > button:hover {
    color: rgba(0, 0, 0, 0.8);
}

.dropdown.bootstrap-select.image-select.style-default
  > button
  .filter-option-inner-inner {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 5px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
}

.dropdown.bootstrap-select.image-select.style-default
  > button
  .filter-option-inner-inner
  img {
    width: 16px;
    height: 12px;
}

.dropdown.bootstrap-select.image-select.style-default > .dropdown-menu {
    overflow: unset !important;
    margin-top: 17px !important;
    margin-bottom: 17px !important;
    padding: 15px 20px;
    border-radius: 0;
    border: 0;
    background-color: var(--white);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 18px 0px;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu
  ul.dropdown-menu
  > li
  > a {
    padding: 5px 0;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu
  ul.dropdown-menu
  > li
  > a
  .text {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 5px;
    font-weight: 400;
    font-size: 14px;
    line-height: 16px;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu
  ul.dropdown-menu
  > li
  > a
  .text
  img {
    width: 16px;
    height: 12px;
}

.dropdown.bootstrap-select.image-select.style-default  > .dropdown-menu  ul.dropdown-menu  > li  > a:active,
.dropdown.bootstrap-select.image-select.style-default  > .dropdown-menu  ul.dropdown-menu  > li  > a.active {
    color: var(--primary) !important;
    background-color: unset !important;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu
  ul.dropdown-menu
  > li
  > a:hover {
    color: rgba(0, 0, 0, 0.8);
    background-color: unset;
}

.dropdown.bootstrap-select.image-select.style-default > .dropdown-menu::after {
    position: absolute;
    content: "";
    width: 16px;
    height: 16px;
    transform: translate(-50%, -50%) rotate(45deg);
    background-color: var(--white);
    top: 0;
    left: 50%;
    z-index: 2;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu[data-popper-placement="top-start"]::after {
    display: none;
}

.dropdown.bootstrap-select.image-select.style-default
  > .dropdown-menu[data-popper-placement="top-start"]::before {
    position: absolute;
    content: "";
    width: 16px;
    height: 16px;
    transform: translate(-50%, 50%) rotate(45deg);
    background-color: var(--white);
    bottom: 0%;
    left: 50%;
    z-index: 2;
}

.dropdown.bootstrap-select.image-select.type-currencies
  > button
  .filter-option
  .filter-option-inner {
    width: 50px;
}

.dropdown.bootstrap-select.image-select.type-currencies > .dropdown-menu {
    width: 300px;
    margin-left: calc(50% - 150px) !important;
}

.dropdown.bootstrap-select.image-select.type-languages > .dropdown-menu {
    width: 96px;
    margin-left: calc(50% - 48px) !important;
}

.dropdown.bootstrap-select.image-select.color-white > button::after {
    color: var(--white);
}

.dropdown.bootstrap-select.image-select.color-white
  > button
  .filter-option
  .filter-option-inner {
    color: var(--white);
}

.dropdown.image-select > .dropdown-menu {
    width: 300px;
    margin-left: calc(50% - 150px) !important;
    min-height: max-content !important;
}

.dropdown.image-select > .dropdown-menu > .inner {
    min-height: max-content !important;
}

.dropdown .dropdown-item {
    color: var(--main);
}

/* ------------ carousel ---------------- */
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets  .swiper-pagination-bullet {
    margin: 0px;
}

.sw-auto .swiper-slide {
    width: auto;
}

.sw-dots {
    display: flex;
    gap: 8px;
}

.sw-dots.swiper-pagination-bullets .swiper-pagination-bullet {
    display: flex;
    align-items: center;
    justify-content: center;
}

.sw-dots .swiper-pagination-bullet {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    opacity: 1;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.sw-dots .swiper-pagination-bullet:before {
    content: "";
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 999px;
    background-color: var(--main);
    transition: 0.3s;
}

.sw-dots .swiper-pagination-bullet.swiper-pagination-bullet-active {
    background-color: rgba(0, 0, 0, 0.16);
}

.sw-dots.style-2 span::before {
    width: 8px;
    height: 8px;
    background-color: rgba(0, 0, 0, 0.16);
}

.sw-dots.style-2 span.swiper-pagination-bullet-active {
    background-color: transparent;
    border: 2px solid var(--main);
}

.sw-dots.style-2 span.swiper-pagination-bullet-active::before {
    background-color: var(--main);
}

.sw-dots.dots-white span::before {
    background-color: rgba(255, 255, 255, 0.2);
}

.sw-dots.dots-white span.swiper-pagination-bullet-active {
    background-color: transparent;
    border: 2px solid var(--white);
}

.sw-dots.dots-white span.swiper-pagination-bullet-active::before {
    background-color: var(--white);
}

.sw-dots.line-pagination span {
    width: 6px;
    height: 6px;
    background-color: rgb(0, 0, 0);
    opacity: 0.4;
}

.sw-dots.line-pagination span::before {
    content: none;
}

.sw-dots.line-pagination span.swiper-pagination-bullet-active {
    background-color: rgb(0, 0, 0);
    opacity: 1;
    width: 22px;
    border-radius: 9999px;
}

.sw-dots.rectangle-pagination span {
    width: 30px;
    height: 2px;
    background-color: rgb(0, 0, 0);
    opacity: 0.4;
    border-radius: 0;
    border: 0;
}

.sw-dots.rectangle-pagination span::before {
    content: none;
}

.sw-dots.rectangle-pagination span.swiper-pagination-bullet-active {
    background-color: rgb(0, 0, 0);
    opacity: 1;
}

.sw-dots.line-white-pagination span {
    background-color: rgb(255, 255, 255);
    opacity: 0.4;
    width: 6px;
    height: 6px;
}

.sw-dots.line-white-pagination span::before {
    content: none;
}

.sw-dots.line-white-pagination span.swiper-pagination-bullet-active {
    background-color: rgb(255, 255, 255);
    opacity: 1;
    width: 22px;
    border-radius: 9999px;
}

.sw-dots.dots-fill-white .swiper-pagination-bullet::before {
    background-color: var(--white);
}

.sw-dots.dots-fill-white .swiper-pagination-bullet-active {
    background-color: rgba(255, 255, 255, 0.1607843137);
}

.sw-dots:not(.swiper-pagination-lock) {
    margin-top: 15px;
}

.sw-dots.medium span {
    width: 20px;
    height: 20px;
    border-width: 1px !important;
}

.sw-dots.medium span::before {
    width: 6px;
    height: 6px;
}

.sw-dots.medium span.swiper-pagination-bullet-active::before {
    width: 4px;
    height: 4px;
}

.sw-dots.small span {
    width: 16px;
    height: 16px;
    border-width: 1px !important;
}

.sw-dots.small span::before {
    width: 6px;
    height: 6px;
}

.sw-dots.small span.swiper-pagination-bullet-active::before {
    width: 4px;
    height: 4px;
}

.sw-dots.absolute-dots {
    position: absolute;
    left: 15px;
    right: 15px;
    bottom: 10px;
    z-index: 10;
}

.sw-dots.large .swiper-pagination-bullet::before {
    width: 8px;
    height: 8px;
}

.box-sw-navigation {
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-sw {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    color: var(--main);
    border: 1px solid var(--main);
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    font-size: 12px;
    cursor: pointer;
}

.nav-sw.swiper-button-disabled {
    border-color: rgba(0, 0, 0, 0.14);
    color: rgba(0, 0, 0, 0.4);
}

.nav-sw:hover:not(.swiper-button-disabled) {
    background-color: var(--main);
    color: var(--white);
}

.nav-sw.lg {
    width: 46px;
    height: 46px;
    font-size: 12px;
}

.nav-sw.square {
    width: 30px;
    height: 30px;
    border-radius: 3px;
}

.nav-sw.round {
    border-radius: 999px;
}

.nav-sw.style-not-line {
    border-color: transparent;
}

.nav-sw.style-not-line.swiper-button-disabled {
    background-color: rgba(255, 255, 255, 0.4);
    color: rgb(0, 0, 0);
}

.nav-sw.line-white {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

.nav-sw.line-white:hover:not(.swiper-button-disabled) {
    color: var(--main);
    background-color: var(--white);
    border-color: var(--white);
}

.nav-sw.line-white.swiper-button-disabled {
    border-color: rgba(255, 255, 255, 0.4);
    color: rgba(255, 255, 255, 0.4);
}

.nav-sw.disable-line.swiper-button-disabled {
    border-color: transparent;
    background-color: rgba(255, 255, 255, 0.4);
    color: rgb(0, 0, 0);
}

.nav-sw.style-blue {
    border-color: #1c355e;
    background-color: #1c355e;
}

.nav-sw.style-blue .icon {
    color: var(--white);
}

.nav-sw.style-blue:hover:not(.swiper-button-disabled) {
    border-color: var(--white);
    background-color: var(--white);
}

.nav-sw.style-blue:hover:not(.swiper-button-disabled) .icon {
    color: #1c355e;
}

.nav-sw.style-blue.swiper-button-disabled {
    border-color: rgba(255, 255, 255, 0.4);
    background-color: rgba(255, 255, 255, 0.4);
}

.nav-sw.style-blue.swiper-button-disabled .icon {
    color: #1c355e;
}

.nav-sw.style-white {
    background-color: var(--white);
    border: 0;
}

.nav-sw.style-white-line {
    background-color: var(--white);
}

.nav-sw-arrow {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
}

.nav-sw-arrow:hover {
    color: var(--primary);
}

.nav-sw-arrow.swiper-button-disabled {
    color: rgba(0, 0, 0, 0.4);
}

.nav-sw-arrow.type-white {
    color: var(--white);
}

.nav-sw-arrow.type-white:hover {
    color: var(--primary);
}

.hover-sw-nav {
    position: relative;
}

.hover-sw-nav .nav-sw {
    z-index: 10;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
    cursor: pointer;
    visibility: hidden;
    background-color: var(--white);
    border-color: transparent;
}

.hover-sw-nav .nav-sw.nav-next-slider {
    margin-left: 20px;
    left: 13px;
}

.hover-sw-nav .nav-sw.nav-prev-slider {
    right: 13px;
    margin-right: 20px;
}

.hover-sw-nav .nav-sw.w_46 .icon {
    font-size: 12px;
}

.hover-sw-nav .nav-sw.swiper-button-disabled {
    background-color: rgba(0, 0, 0, 0.2);
}

.hover-sw-nav .nav-sw:hover:not(.swiper-button-disabled) {
    background-color: var(--main);
    color: var(--white);
}

.hover-sw-nav .nav-sw.style-2.swiper-button-disabled {
    opacity: 0.4;
    background-color: var(--white);
}

.hover-sw-nav.view-default .nav-sw,
.hover-sw-nav:hover .nav-sw {
    margin: 0;
    visibility: visible;
}

.hover-sw-nav.hover-sw-2 .nav-sw {
    top: 37%;
}

.hover-sw-nav.hover-sw-3 .nav-sw {
    top: 40%;
}

.hover-sw-nav.hover-sw-4 .nav-sw {
    top: 35%;
}

.hover-sw-nav.hover-sw-5 .nav-sw {
    top: 32%;
}

.button-style-arrow {
    width: 46px;
    height: 46px;
    background-color: var(--white);
    border-radius: 50%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.button-style-arrow::after {
    font-size: 11px;
    color: var(--main);
    font-weight: 700;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.button-style-arrow:hover {
    background-color: var(--primary);
}

.button-style-arrow:hover::after {
    color: var(--white);
}

.wrap-carousel {
    position: relative;
}

.wrap-carousel .nav-sw {
    z-index: 20;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.wrap-carousel .nav-next-slider {
    left: -84px;
}

.wrap-carousel .nav-prev-slider {
    right: -84px;
}

.wrap-carousel .sw-dots {
    display: none;
}

.wrap-carousel.wrap-sw-2 .nav-sw {
    top: 37%;
}

.wrap-carousel.wrap-sw-3 .nav-sw {
    top: 46%;
}

.sw-wrapper-right {
    margin-right: -15px;
}

.navigation-sw-dot {
    width: 27px;
    height: 27px;
    border-radius: 50%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    background-color: var(--white);
}

.navigation-sw-dot span {
    width: 9px;
    height: 9px;
    background-color: var(--main);
    border-radius: 50%;
}

.navigation-sw-dot::after,
.navigation-sw-dot::before {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 9999px;
    animation: ripple 3s infinite;
}

.navigation-sw-dot::after {
    animation-delay: 0.5s;
}

.navigation-sw-dot::before {
    animation-delay: 0.9s;
}

.navigation-sw-dot.swiper-button-disabled {
    width: 36px;
    height: 36px;
}

.navigation-sw-dot.swiper-button-disabled span {
    width: 12px;
    height: 12px;
}

.navigation-sw-dot.type-black::after,
.navigation-sw-dot.type-black::before {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    border-radius: 9999px;
    animation: ripple_black 3s infinite;
}

@keyframes ripple {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.5);
    }

    50% {
        box-shadow: 0 0 0 20px rgba(255, 255, 255, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

@keyframes ripple_black {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.5);
    }

    50% {
        box-shadow: 0 0 0 20px rgba(0, 0, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
    }
}

.swiper-button-disabled {
    cursor: auto !important;
}

.box-nav-pagination {
    display: inline-flex;
    gap: 6px;
    align-items: center;
    min-width: 70px;
    background-color: var(--white);
    padding: 8px;
    line-height: 0;
    border-radius: 999px;
}

.box-nav-pagination .dots-default {
    display: flex;
    justify-content: center;
    gap: 4px;
}

.nav-arr-default.swiper-button-disabled {
    opacity: 0.35;
}

.dots-default .swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    background-color: var(--main);
}

.tf-sw-brand .sw-dots:not(.swiper-pagination-lock) {
    margin-top: 35px;
}

/* ------------ avatar ---------------- */
.avatar.round {
    border-radius: 50%;
    overflow: hidden;
}

.avatar img {
    width: 100%;
    min-width: 100%;
    height: 100%;
    object-fit: cover;
}

.avt-40 {
    width: 40px;
    min-width: 40px;
    height: 40px;
}

.avt-56 {
    width: 56px;
    min-width: 56px;
    height: 56px;
}

.avt-60 {
    width: 60px;
    min-width: 60px;
    height: 60px;
}

.avt-100 {
    width: 100px;
    min-width: 100px;
    height: 100px;
}

/* ------------ pop up ---------------- */
.offcanvas {
    z-index: 3000;
    border: none !important;
}

.offcanvas-backdrop {
    background-color: var(--backdrop);
    cursor: url("../images/item/cursor-close.svg"), auto;
}

.offcanvas-backdrop.show {
    opacity: 1;
}

.overflow-x-auto::-webkit-scrollbar,
.overflow-y-auto::-webkit-scrollbar {
    width: 0px;
}

.modal-backdrop {
    background-color: var(--backdrop);
}

.modal-backdrop.show {
    opacity: 1;
}

.modal {
    cursor: url("../images/item/cursor-close.svg"), auto;
}

.modal.show .modal-dialog {
    transform: none;
    transition: transform 0.3s ease-out !important;
}

.modal.fullRight .modal-dialog {
    transform: translate(100%, 0);
    min-width: 100%;
    height: 100%;
    margin: 0;
    transition: transform 1s ease-out;
}

.modal.fullRight .modal-dialog .modal-content {
    border-radius: 0;
    border: 0;
    margin: auto;
    overflow: hidden;
    position: absolute;
    right: 0;
    bottom: 0;
    top: 0;
    padding: 0;
}

.modal.fullRight .modal-dialog .modal-content .modal-body {
    overflow: auto;
    padding: 0;
    padding-bottom: 30px;
}

.modal.fullRight.show .modal-dialog {
    transform: translate(0, 0);
}

.modal.fullLeft .modal-dialog {
    transform: translate(-100%, 0) !important;
    min-width: 100%;
    height: 100%;
    margin: 0;
    transition: all 0.3s !important;
}

.modal.fullLeft .modal-dialog .modal-content {
    border-radius: 0;
    border: 0;
    margin: auto;
    overflow: hidden;
    position: absolute;
    left: 0;
    bottom: 0;
    top: 0;
    padding: 0;
}

.modal.fullLeft .modal-dialog .modal-content .modal-body {
    overflow: auto;
    padding: 0;
    padding-bottom: 30px;
}

.modal.fullLeft.show .modal-dialog {
    transform: translate(0, 0) !important;
}

.modal.modalLeft .modal-dialog {
    transform: translate(-100px, 0);
}

.modal.modalRight .modal-dialog {
    transform: translate(100px, 0);
}

.modal.modalBottom .modal-dialog {
    transform: translate(0, 100px);
}

.modal.modalCentered .modal-dialog {
    transform: translate(0, 0) !important;
}

.modal .modal-content {
    cursor: default !important;
}

.modal.fade:not(.show) {
    opacity: 0;
}

.modalDemo .demo-title {
    margin-top: 50px;
    margin-bottom: 44px;
    font-weight: 500;
    text-align: center;
}

.modalDemo .modal-dialog {
    max-width: 1540px;
    margin-top: 8px;
    margin-bottom: 8px;
    height: calc(100vh - 16px);
}

.modalDemo .modal-content {
    padding: 0 50px 40px;
    background-color: var(--white);
    width: 100%;
    border-radius: 10px;
    margin: 0 30px;
    max-height: calc(100vh - 60px);
    border: 0;
    cursor: default;
    overflow: hidden;
}

.modalDemo .mega-menu {
    padding: 0 40px;
    overscroll-behavior-y: contain;
    overflow-y: auto;
}

.modalDemo .mega-menu::-webkit-scrollbar {
    width: 5px;
}

.modalDemo .mega-menu::-webkit-scrollbar-thumb {
    background: var(--line);
}

.modalDemo .header {
    position: relative;
}

.modalDemo .header .icon-close-popup {
    position: absolute;
    top: 18px;
    right: 0;
    width: 30px;
    height: 30px;
    display: inline-flex;
    justify-content: flex-end;
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: rgb(134, 134, 134);
}

.tf-product-modal .modal-dialog {
    max-width: min(625px, 90vw);
}

.tf-product-modal .modal-dialog .modal-content {
    padding: 38px 36px 40px;
    margin-left: 0;
    margin-right: 0;
}

.tf-product-modal .modal-dialog .modal-content .header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tf-product-modal .modal-dialog .modal-content .header .demo-title {
    margin: 0;
    text-align: start;
    font-size: 26px;
    font-weight: 400;
    line-height: 31px;
}

.tf-product-modal .modal-dialog .modal-content .header span {
    position: unset;
    color: var(--main);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-product-modal .modal-dialog .modal-content .header span:hover {
    color: var(--primary);
}

.tf-product-modal .modal-dialog .modal-content h6 {
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 25px;
}

.tf-product-modal .modal-dialog .modal-content .title {
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
    margin-top: 15px;
}

.tf-product-modal .modal-dialog .modal-content p {
    margin-top: 15px;
    margin-bottom: 20px;
}

.tf-product-modal .tf-social-icon .box-icon {
    width: 40px;
    height: 40px;
    border-radius: 999px;
    font-size: 16px;
    color: var(--white);
}

.tf-product-modal .tf-social-icon .box-icon.social-twiter {
    font-size: 12px;
    background: var(--twitter-cl);
}

.tf-product-modal .tf-social-icon .box-icon.social-facebook {
    background: var(--facebook-cl);
}

.tf-product-modal .tf-social-icon .box-icon.social-instagram {
    background: var(--instagram-cl);
}

.tf-product-modal .tf-social-icon .box-icon.social-tiktok {
    background: var(--tiktok-cl);
}

.tf-product-modal .tf-social-icon .box-icon.social-pinterest {
    background: var(--pinterest-cl);
}

.tf-product-modal .form-share {
    margin-top: 20px;
    position: relative;
}

.tf-product-modal .form-share .button-submit {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 5px;
}

.tf-product-modal .form-share .button-submit .tf-btn {
    padding: 10px 18px;
}

.tf-product-modal .form-share input {
    padding-right: 80px;
}

#find_size .modal-dialog {
    max-width: min(900px, 90vw);
}

#find_size .tf-rte {
    overflow-y: auto;
}

#find_size .tf-rte::-webkit-scrollbar {
    width: 2px;
}

#find_size .tf-sizeguide-table {
    border: 1px solid var(--line);
    border-radius: 5px;
    width: 100%;
}

#find_size .tf-sizeguide-table th {
    border: 1px solid var(--line);
    padding: 10px;
    font-weight: 600;
    line-height: 20px;
}

#find_size .tf-sizeguide-table td {
    border: 1px solid var(--line);
    border-width: 0 1px 1px 0;
    padding: 10px;
    line-height: 20px;
}

#find_size .tf-page-size-chart-content {
    margin-top: 15px;
    display: grid;
    gap: 10px;
    grid-template-columns: 8fr 4fr;
}

#find_size p {
    color: var(--text-3);
}

#compare_color .modal-dialog {
    max-width: min(1400px, 90vw);
    min-width: min(1400px, 90vw);
    margin-top: 8px;
    margin-bottom: 8px;
    height: calc(100vh - 16px);
    max-height: calc(100vh - 16px);
}

#compare_color .modal-dialog .modal-content {
    padding: 30px 28px 33px;
}

#compare_color .modal-dialog .header {
    justify-content: center;
    margin-bottom: 32px;
}

#compare_color .modal-dialog .header .icon-close-popup {
    position: absolute;
    top: 0;
}

#compare_color .tf-compare-color-wrapp {
    overflow: auto;
    position: relative;
}

#compare_color .tf-compare-color-wrapp::-webkit-scrollbar {
    height: 3px;
}

#compare_color .tf-compare-color-grid {
    display: inline-flex;
    text-align: center;
    border: 1px solid var(--line);
}

#compare_color .tf-compare-color-grid > input {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
}

#compare_color .tf-compare-color-grid > input:checked + .tf-compare-color-item {
    display: none !important;
}

.tf-compare-color-item {
    display: flex;
    flex-direction: column;
    padding: 14px 22px 28px;
    min-width: 330px;
    max-width: 330px;
    flex: 1;
}

.tf-compare-color-item:not(:last-child) {
    border-right: 1px solid var(--line);
}

.tf-compare-color-item .tf-compare-color-top label {
    text-decoration: underline;
    color: var(--main);
    font-size: 12px;
    font-weight: 400;
    line-height: 19px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tf-compare-color-item .tf-compare-color-top img {
    border-radius: 10px;
    margin-top: 14px;
}

.tf-compare-color-item .tf-compare-color-bottom {
    margin-top: 1.5px;
}

.tf-compare-color-item .tf-compare-color-bottom .tf-compare-color-color {
    display: inline-flex;
    align-items: center;
    gap: 7px;
    min-width: 87px;
    margin-top: 14px;
    padding: 3px 10px 3px 4px;
    border: 1px solid var(--line);
    border-radius: 24px;
    font-weight: 600;
}

.tf-compare-color-item
  .tf-compare-color-bottom
  .tf-compare-color-color
  .tf-color-list-color {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid rgba(134, 134, 134, 0.12);
}

.tf-compare-color-item .tf-compare-color-bottom form select {
    margin-top: 14px;
    width: 100%;
}

.tf-compare-color-item .tf-compare-color-bottom form a {
    width: 100%;
    margin-top: 15px;
    border-radius: 3px;
}

#ask_question fieldset {
    margin-bottom: 15px;
}

#ask_question fieldset label {
    margin-bottom: 5px;
    font-weight: 400;
    color: var(--text);
}

#ask_question fieldset input {
    height: 50px;
}

#ask_question textarea {
    height: 176px;
}

#delivery_return .tf-product-popup-delivery .title {
    font-size: 20px;
    font-weight: 400;
    line-height: 36px;
    margin-bottom: 25px;
    margin-top: 0;
}

#delivery_return .tf-product-popup-delivery p {
    color: rgb(134, 134, 134);
    line-height: 22.4px;
    margin-bottom: 10px;
}

#delivery_return .tf-product-popup-delivery p a {
    color: rgb(134, 134, 134);
    text-decoration: underline;
    text-underline-offset: 3px;
}

#delivery_return .tf-product-popup-delivery p a:hover {
    color: var(--main);
    text-decoration-thickness: 2px;
}

#delivery_return .tf-product-popup-delivery:not(:last-child) {
    margin-bottom: 20px;
}

#quick_add .modal-dialog {
    max-width: min(466px, 90vw);
}

#quick_add .modal-content {
    margin: 8px;
    padding: 30px 0px 30px;
}

#quick_add .modal-content > .wrap {
    overflow-y: auto;
    padding: 0px 20px;
}

#quick_add .modal-content > .wrap::-webkit-scrollbar {
    width: 2px;
}

#quick_add .modal-content .icon-close-popup {
    top: 0px;
    right: 20px;
}

#quick_add .tf-product-info-item {
    margin-bottom: 15px;
    display: flex;
    gap: 18px;
    align-items: center;
}

#quick_add .tf-product-info-item .image img {
    width: 70px;

    /* height: 98px; */
}

#quick_add .tf-product-info-item .content {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

#quick_add .tf-product-info-item .content a {
    font-size: 20px;
    line-height: 24px;
}

#quick_add .tf-product-info-item .content .price {
    font-size: 20px;
    line-height: 20px;
}

#quick_add .payment-more-option {
    text-decoration: none;
}

#quick_add .payment-more-option:hover {
    text-decoration: underline;
}

#quick_view .modal-dialog {
    max-width: min(1146px, 90vw);
}

#quick_view .modal-dialog .modal-content {
    margin: 8px;
    padding: 0;
}

#quick_view .modal-dialog .modal-content .icon-close-popup {
    top: 20px;
    right: 20px;
    font-size: 16px;
    z-index: 5;
}

#quick_view .modal-dialog .modal-content > .wrap {
    display: flex;
}

#quick_view .tf-product-media-wrap {
    flex: 0 0 auto;
    max-width: 100%;
    width: min(50%, 68vh) !important;
}

#quick_view .tf-product-info-wrap {
    flex: 0 0 auto;
    max-width: 100%;
    flex-grow: 1;
}

#quick_view .tf-product-info-wrap .tf-product-info-list {
    position: absolute;
    inset: 0;
    padding: 50px 40px;
    overflow-y: auto;
}

#quick_view .tf-product-info-wrap .tf-product-info-list::-webkit-scrollbar {
    width: 0px;
}

#quick_view .tf-product-info-wrap .tf-product-info-list .tf-product-info-title {
    margin-bottom: 20px;
}

#quick_view
  .tf-product-info-wrap
  .tf-product-info-list
  .tf-product-info-badges {
    margin-bottom: 18px;
}

#quick_view
  .tf-product-info-wrap
  .tf-product-info-list
  .tf-product-description {
    color: var(--text-3);
}

.bg-color-beige {
    background: conic-gradient(#c8ad7f 0deg 360deg);
}

.bg-color-black {
    background: conic-gradient(#000000 0deg 360deg);
}

.bg-color-blue {
    background: conic-gradient(#a8bcd4 0deg 360deg);
}

.bg-color-white {
    background: conic-gradient(#ffffff 0deg 360deg);
}

.bg-color-pink {
    background: conic-gradient(#fcc6de 0deg 360deg);
}

.bg-color-brown {
    background: conic-gradient(#977945 0deg 360deg);
}

.bg-color-light-purple {
    background: conic-gradient(#d966d9 0deg 360deg);
}

.bg-color-light-green {
    background: conic-gradient(#caffd6 0deg 360deg);
}

.bg-color-orange {
    background: conic-gradient(#ffa500 0deg 360deg);
}

.bg-color-light-blue {
    background: conic-gradient(#add8e6 0deg 360deg);
}

.canvas-wrapper {
    padding: 0;
    isolation: isolate;
    height: 100%;
    width: 100%;
    max-height: none;
    display: grid;
    grid-auto-rows: auto minmax(0, 1fr) auto;
    align-content: start;
}

.canvas-body {
    background-color: var(--white);
    padding: 15px 20px;
    overscroll-behavior-y: contain;
    overflow-y: auto;
}

.canvas-body::-webkit-scrollbar {
    width: 5px;
}

.canvas-body::-webkit-scrollbar-track {
    background-color: var(--bg-scrollbar-track);
}

.canvas-body::-webkit-scrollbar-thumb {
    background: var(--bg-scrollbar-thumb);
    border-radius: 4px;
}

.canvas-filter {
    max-width: 300px;
}

.canvas-filter .filter-icon {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    gap: 10px;
    color: var(--main);
    text-transform: uppercase;
    line-height: 22px;
    font-size: 12px;
    font-weight: 600;
}

.canvas-filter .canvas-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 20px;
    background-color: var(--bg-11);
}

.canvas-filter .canvas-header .icon-close-popup {
    font-size: 16px;
}

.canvas-filter .widget-facet:last-child {
    margin-bottom: 0;
}

.canvas-sidebar .canvas-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 15px;
    background-color: var(--bg-11);
    min-height: 40px;
}

.canvas-sidebar .canvas-header .icon-close-popup {
    font-size: 16px;
}

.canvas-sidebar .canvas-header .title {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
}

.canvas-sidebar .canvas-body {
    padding: 15px;
}

.canvas-compare {
    height: max-content !important;
    z-index: 5000;
}

.canvas-compare .close-popup {
    position: absolute;
    top: 30px;
    right: 30px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
}

.canvas-compare .canvas-body {
    padding: 28px 0;
}

.canvas-compare .canvas-wrapper {
    height: unset;
}

.offcanvas-backdrop {
    z-index: 2000;
}

.tf-compare-list {
    display: flex;
    align-items: center;
}

.tf-compare-list .tf-compare-head {
    flex: 0 0 22%;
    display: flex;
    align-items: center;
}

.tf-compare-list .tf-compare-head .title {
    font-size: 28px;
    line-height: 34px;
}

.tf-compare-list .tf-compare-offcanvas {
    display: flex;
    align-items: center;
    flex: 1 1 63%;
    overflow-x: auto;
}

.tf-compare-list .tf-compare-offcanvas .tf-compare-item {
    flex: 0 0 16.666%;
    padding: 0 10px;
    position: relative;
}

.tf-compare-list .tf-compare-offcanvas .tf-compare-item .icon {
    position: absolute;
    z-index: 5;
    top: 6px;
    right: 6px;
    width: 25px;
    height: 25px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.tf-compare-list .tf-compare-offcanvas .tf-compare-item img {
    width: 100%;
    object-fit: cover;
}

.tf-compare-list .tf-compare-buttons {
    display: flex;
    align-items: center;
    justify-content: end;
    flex: 0 0 15%;
}

.tf-compare-list .tf-compare-buttons a {
    height: 47px;
    width: 147px;
}

.tf-compare-list .tf-compare-buttons .tf-compapre-button-clear-all {
    margin-top: 24px;
    font-weight: 600;
    line-height: 19px;
    text-align: center;
    text-decoration: underline;
    text-underline-offset: 4px;
    cursor: pointer;
}

.modal-shopping-cart .modal-content {
    max-width: 477px !important;
    cursor: default !important;
}

.modal-shopping-cart .header {
    margin: 0 36px;
    padding: 5px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--line);
    height: 60px;
}

.modal-shopping-cart .header .title {
    font-size: 20px;
}

.modal-shopping-cart .header .icon-close-popup {
    font-size: 16px;
    padding: 0 6px;
}

.modal-shopping-cart .wrap {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.modal-shopping-cart .tf-mini-cart-threshold {
    margin: 0 36px;
    padding: 18px 0 16px;
    border-bottom: 1px solid var(--line);
}

.modal-shopping-cart .tf-mini-cart-threshold .tf-progress-bar {
    margin-top: 14px;
    margin-bottom: 10px;
    height: 4px;
}

.modal-shopping-cart .tf-mini-cart-threshold .tf-progress-msg {
    margin-top: 18px;
    font-size: 16px;
    line-height: 26px;
}

.modal-shopping-cart .tf-mini-cart-wrap {
    flex: 1 1 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main {
    flex: 1 1 auto;
    position: relative;
}

.modal-shopping-cart .tf-mini-cart-wrap .tf-mini-cart-main .tf-mini-cart-sroll {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    overflow: auto;
}

.modal-shopping-cart
  .tf-mini-cart-wrap
  .tf-mini-cart-main
  .tf-mini-cart-sroll::-webkit-scrollbar {
    width: 6px;
}

.modal-shopping-cart
  .tf-mini-cart-wrap
  .tf-mini-cart-main
  .tf-mini-cart-sroll::-webkit-scrollbar-thumb {
    background: var(--line);
    border-radius: 6px;
}

.modal-shopping-cart .tf-mini-cart-item {
    margin: 0 36px;
    padding: 20px 0;
    display: flex;
    gap: 24px;
}

.modal-shopping-cart .tf-mini-cart-item:not(:last-child) {
    border-bottom: 1px solid var(--line);
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-image a {
    width: 80px;
    height: 110px;
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-image a img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info a {
    font-size: 16px;
    line-height: 16px;
    display: block;
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info .meta-variant {
    font-size: 12px;
    line-height: 19px;
    margin-top: 6px;
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info .price {
    line-height: 14px;
    margin-top: 6px;
}

.modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info .tf-mini-cart-btns {
    margin-top: 10px;
    display: flex;
    gap: 12px;
    align-items: center;
}

.modal-shopping-cart
  .tf-mini-cart-item
  .tf-mini-cart-info
  .tf-mini-cart-btns
  .wg-quantity {
    width: 86px;
}

.modal-shopping-cart
  .tf-mini-cart-item
  .tf-mini-cart-info
  .tf-mini-cart-btns
  .tf-mini-cart-remove {
    font-size: 12px;
    line-height: 19px;
    color: var(--text);
    text-decoration: underline;
}

.modal-shopping-cart .tf-minicart-recommendations {
    margin: 15px 36px;
    padding: 18px;
    box-shadow: 0 0.4rem 1.6rem rgba(0, 0, 0, 0.0784313725);
}

.modal-shopping-cart
  .tf-minicart-recommendations
  .tf-minicart-recommendations-heading {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-shopping-cart
  .tf-minicart-recommendations
  .tf-minicart-recommendations-heading
  .tf-minicart-recommendations-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
}

.modal-shopping-cart
  .tf-minicart-recommendations
  .tf-minicart-recommendations-heading
  .sw-dots {
    width: unset;
}

.modal-shopping-cart .tf-minicart-recommendations-item {
    display: flex;
    align-items: center;
}

.modal-shopping-cart
  .tf-minicart-recommendations-item
  .tf-minicart-recommendations-item-image
  a {
    width: 80px;
    height: 110px;
    margin-right: 18px;
}

.modal-shopping-cart
  .tf-minicart-recommendations-item
  .tf-minicart-recommendations-item-infos
  a {
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 16px;
}

.modal-shopping-cart
  .tf-minicart-recommendations-item
  .tf-minicart-recommendations-item-infos
  .price {
    margin-top: 4px;
    line-height: 14px;
    font-weight: 600;
}

.modal-shopping-cart
  .tf-minicart-recommendations-item
  .tf-minicart-recommendations-item-quickview
  .quickview {
    margin-left: 15px;
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--main);
    font-size: 16px;
    color: var(--white);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.modal-shopping-cart
  .tf-minicart-recommendations-item
  .tf-minicart-recommendations-item-quickview
  .quickview:hover {
    background-color: var(--primary);
}

.modal-shopping-cart .tf-mini-cart-tool {
    background-color: rgb(242, 242, 242);
    display: flex;
    justify-content: center;
}

.modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn {
    width: 69px;
    height: 43px;
    line-height: 43px;
    margin: 18px 10px;
    border-radius: 3px;
    cursor: pointer;
    color: var(--main);
    background-color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn:hover {
    background-color: var(--primary);
    color: var(--white);
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap {
    padding: 20px 36px 30px;
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-totals-discounts {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-shopping-cart
  .tf-mini-cart-bottom-wrap
  .tf-cart-totals-discounts
  .tf-cart-total {
    font-size: 20px;
}

.modal-shopping-cart
  .tf-mini-cart-bottom-wrap
  .tf-cart-totals-discounts
  .tf-totals-total-value {
    font-size: 20px;
    line-height: 32px;
    margin-left: 15px;
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-tax {
    margin-top: 10px;
    color: var(--text);
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-tax a {
    text-decoration: underline;
    text-underline-offset: 3px;
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-line {
    border: 1px solid var(--line);
    margin-top: 14px;
    margin-bottom: 14px;
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-cart-checkbox {
    margin-top: 10px;
}

.modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout {
    margin-top: 20px;
    display: flex;
    gap: 14px;
}

.tf-cart-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.tf-cart-checkbox .tf-checkbox-wrapp {
    min-width: 1.6rem;
    place-items: center;
    position: relative;
    overflow: hidden;
    display: flex;
}

.tf-cart-checkbox .tf-checkbox-wrapp input {
    cursor: pointer;
    display: block;
    width: 16px;
    height: 16px;
    border-radius: 50% !important;
    transition: 0.2s ease-in-out;
    background-color: var(--white);
    opacity: 0;
}

.tf-cart-checkbox .tf-checkbox-wrapp input:checked + div {
    background-color: var(--primary);
}

.tf-cart-checkbox .tf-checkbox-wrapp input:checked + div i {
    transform: scale(1);
}

.tf-cart-checkbox .tf-checkbox-wrapp div {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    transition: 0.25s ease-in-out;
    z-index: 5;
    border: 1px solid var(--line);
    border-radius: 50%;
    background-color: var(--white);
    color: var(--white);
    pointer-events: none;
}

.tf-cart-checkbox .tf-checkbox-wrapp div i {
    font-size: 6px;
    transform: scale(0);
}

.tf-cart-checkbox label {
    font-weight: 400;
    cursor: pointer;
}

.tf-cart-checkbox label a {
    text-decoration: underline;
    text-underline-offset: 2px;
}

.tf-cart-checkbox .wrap-content {
    display: none;
}

.tf-cart-checkbox.check .wrap-content {
    display: block;
}

.tf-mini-cart-tool-openable {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    transition: transform 0.25s ease-in-out;
    z-index: 70;
}

.tf-mini-cart-tool-openable.open {
    transform: translateY(0);
}

.tf-mini-cart-tool-openable.open > .overplay {
    opacity: 1;
    visibility: visible;
}

.tf-mini-cart-tool-openable > .overplay {
    position: absolute;
    left: 0;
    bottom: 0;
    height: 200vh;
    right: 0;
    background-color: rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.tf-mini-cart-tool-openable .tf-mini-cart-tool-close {
    cursor: pointer;
}

.tf-mini-cart-tool-openable .tf-btn {
    height: 47px;
}

.tf-mini-cart-tool-openable .tf-mini-cart-tool-content {
    position: relative;
    z-index: 80;
    padding: 32px 36px;
    background-color: rgb(242, 242, 242);
}

.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-mini-cart-tool-text {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 28px;
}

.tf-mini-cart-tool-openable
  .tf-mini-cart-tool-content
  .tf-mini-cart-tool-text
  .icon {
    width: 33px;
    height: 33px;
    border: 1px solid var(--main);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: var(--main);
}

.tf-mini-cart-tool-openable .tf-mini-cart-tool-content .tf-cart-tool-btns {
    margin-top: 32px;
    display: flex;
    gap: 30px;
    align-items: center;
}

.tf-mini-cart-tool-openable
  .tf-mini-cart-tool-content
  .tf-cart-tool-btns
  .tf-mini-cart-tool-primary {
    text-decoration: underline;
    text-underline-offset: 8px;
}

.tf-mini-cart-tool-openable.add-gift .tf-mini-cart-tool-text {
    gap: 20px;
    align-items: start;
}

.tf-mini-cart-tool-openable.add-gift .tf-mini-cart-tool-text .icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
    background-color: var(--white);
    color: var(--main);
    border: 0;
}

.tf-mini-cart-tool-openable.add-gift .tf-gift-wrap-infos p {
    font-size: 20px;
    line-height: 32px;
}

.tf-mini-cart-tool-openable.estimate-shipping .tf-mini-cart-tool-text {
    margin-bottom: 20px;
}

.tf-mini-cart-tool-openable.estimate-shipping select {
    height: 49px;
}

.tf-mini-cart-tool-openable.estimate-shipping .field {
    margin-bottom: 15px;
}

.tf-mini-cart-tool-openable.estimate-shipping .field p {
    margin-bottom: 8px;
}

.canvas-search {
    width: 100% !important;
    max-width: 463px;
    padding-top: 55px;
    border: 0 !important;
}

.canvas-search .tf-search-head {
    padding: 0 22px;
    border-bottom: 1px solid var(--line);
    margin-bottom: 22px;
    box-shadow: unset;
}

.canvas-search .tf-search-head .title {
    font-size: 28px;
    line-height: 34px;
    margin-bottom: 19px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.canvas-search .tf-search-head .close {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.canvas-search .tf-search-head .tf-search-sticky {
    margin-bottom: 30px;
}

.canvas-search .tf-search-content {
    padding: 0 22px 16px 22px;
}

.canvas-search .tf-search-content-title {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 30px;
}

.canvas-search .tf-col-quicklink {
    margin-bottom: 32px;
}

.canvas-search .tf-col-quicklink .tf-search-content-title {
    margin-bottom: 14px;
}

.canvas-search .tf-col-quicklink .tf-quicklink-item a {
    padding: 4px 0;
    line-height: 22.4px;
}

.canvas-search .tf-search-hidden-inner {
    padding-top: 5px;
}

.canvas-search .tf-loop-item {
    display: flex;
    gap: 19px;
    align-items: flex-start;
}

.canvas-search .tf-loop-item:not(:last-child) {
    padding-bottom: 16px;
    border-bottom: 1px solid var(--line);
    margin-bottom: 16px;
}

.canvas-search .tf-loop-item .image {
    width: 68px;
    max-height: 95px;
}

.canvas-search .tf-loop-item .tf-product-info-price > div {
    font-size: 14px;
}

.tf-mini-search-frm {
    position: relative;
}

.tf-mini-search-frm input {
    height: 42px;
    padding: 12px 20px 12px 44px;
    font-size: 16px;
    line-height: 26px;
    color: var(--main);
}

.tf-mini-search-frm input::placeholder {
    font-size: 16px;
    line-height: 26px;
    color: var(--main);
}

.tf-mini-search-frm button {
    position: absolute;
    left: 14px;
    top: 0;
    font-size: 16px;
    margin: 13px 0;
    background-color: transparent;
    border: 0;
    outline: none;
}

.form-sign-in .modal-dialog {
    max-width: 640px;
}

.form-sign-in .modal-dialog .modal-content {
    border: 0;
    padding: 37px 35px;
    border-radius: 3px;
}

.form-sign-in .header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-sign-in .header .demo-title {
    font-size: 28px;
    line-height: 33.6px;
}

.form-sign-in .header .icon-close-popup {
    padding: 0 6px;
    font-size: 16px;
    cursor: pointer;
}

.form-sign-in .tf-login-form form > div {
    margin-top: 15px;
}

.form-sign-in .tf-login-form form .btn-link {
    margin: 10px 0;
    text-decoration: underline !important;
    text-underline-offset: 3px;
    color: var(--text);
}

.form-sign-in .tf-login-form form .btn-link .icon {
    font-size: 8px;
}

.form-sign-in .tf-login-form form .bottom {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
}

.form-sign-in .tf-login-form form .bottom .btn-link {
    color: var(--main);
}

.toolbar-shop-mobile {
    max-width: min(90%, 430px) !important;
}

.toolbar-shop-mobile .mb-canvas-content {
    max-width: 100%;
    padding-top: 70px;
    padding-left: 0;
}

.toolbar-shop-mobile .mb-body {
    padding: 0 20px 0 21px;
}

.toolbar-shop-mobile ul.nav-ul-mb > li {
    padding: 0 !important;
    border: 0 !important;
}

.toolbar-shop-mobile .tf-category-link {
    gap: 16px;
    min-height: 50px !important;
    padding: 4px 0 6px;
    position: relative;
}

.toolbar-shop-mobile .tf-category-link .image {
    width: 34px;
    height: 34px;
    position: relative;
}

.toolbar-shop-mobile .tf-category-link .image::before {
    position: absolute;
    z-index: 1;
    content: "";
    top: -3px;
    bottom: -3px;
    left: -3px;
    right: -3px;
    width: calc(100% + 6px);
    height: calc(100% + 6px);
    border: solid 1px var(--line);
    margin: auto;
    pointer-events: none;
    border-radius: 50%;
}

.toolbar-shop-mobile .tf-category-link .image img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.toolbar-shop-mobile .tf-category-link > span:nth-child(2) {
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    flex-grow: 1;
}

.toolbar-shop-mobile .tf-category-link::after {
    position: absolute;
    bottom: 0;
    content: "";
    height: 1px;
    width: calc(100% - 53px);
    right: 0;
    left: 53px;
    background-color: rgba(0, 0, 0, 0.12);
}

.toolbar-shop-mobile .tf-category-link .btn-open-sub {
    width: 40px;
    height: 40px;
    border-left: 1px solid var(--line);
}

.toolbar-shop-mobile .tf-category-link.current::after {
    display: none;
}

.toolbar-shop-mobile .tf-category-link.has-children:not(.collapsed)::after {
    display: none;
}

.toolbar-shop-mobile .sub-nav-menu {
    margin: 0px 0 17px 50px !important;
    padding: 0 !important;
}

.toolbar-shop-mobile .sub-nav-menu .tf-category-link {
    padding: 4px 0 4px 15px;
    margin-bottom: 1px;
    min-height: 30px !important;
}

.toolbar-shop-mobile .sub-nav-menu .tf-category-link::after {
    display: none;
}

.toolbar-shop-mobile .sub-menu-level-2 {
    margin-left: 65px !important;
}

.toolbar-shop-mobile .mb-bottom {
    min-height: 50px;
    clear: both;
    padding: 15px 26px;
    background-color: rgba(0, 0, 0, 0.05);
}

.toolbar-shop-mobile .mb-bottom a {
    line-height: 13px;
}

.toolbar-shop-mobile .list-cate {
    position: relative;
}

.toolbar-shop-mobile .list-cate.show::after {
    position: absolute;
    bottom: -17px;
    content: "";
    height: 1px;
    width: calc(100% - 53px);
    right: 0;
    left: 53px;
    background-color: rgba(0, 0, 0, 0.12);
}

#sidebarmobile {
    max-height: 100vh;
    overflow-y: auto;
}

.modal-newleter .modal-content .modal-top {
    position: relative;
    border-radius: 10px 10px 0px 0px;
    overflow: hidden;
}

.modal-newleter .modal-content .modal-top .icon {
    position: absolute;
    cursor: pointer;
    top: 13px;
    right: 13px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 999px;
    background-color: var(--white);
    color: var(--main);
    font-size: 12px;
}

.modal-newleter .modal-content .modal-bottom {
    border-radius: 0px 0px 10px 10px;
    background-color: var(--white);
    padding: 20px;
}

.modal-newleter .modal-content .modal-bottom h6 {
    margin-top: 17px;
}

.modal-newleter .modal-content .modal-bottom .tf-btn {
    margin-top: 25px;
}

.modal-newleter .modal-content .form-newsletter {
    margin-top: 26px;
}

.canvas-sidebar-blog .canvas-header {
    background-color: var(--white);
    padding: 14px 20px;
}

.canvas-sidebar-blog .canvas-header .title {
    font-size: 16px;
    line-height: 19.2px;
    font-weight: 400;
}

.canvas-sidebar-blog .canvas-body {
    padding: 20px;
}

/* ------------ box icon ---------------- */
.box-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
    cursor: pointer;
}

.box-icon.w_28 {
    width: 28px;
    height: 28px;
}

.box-icon.w_34 {
    width: 34px;
    height: 34px;
}

.box-icon.w_46 {
    width: 46px;
    height: 46px;
}

.box-icon.w_50 {
    width: 50px;
    height: 50px;
}

.box-icon.round {
    border-radius: 50% !important;
}

.tf-social-icon {
    --facebook-cl: rgb(59, 89, 152);
    --twitter-cl: rgb(85, 85, 85);
    --instagram-cl: linear-gradient(#8a3ab9, #e95950, #fccc63);
    --threads-cl: rgb(224, 53, 102);
    --youtube-cl: rgb(205, 32, 31);
    --tiktok-cl: linear-gradient(#25f4ee, #000, #fe2c55);
    --tiktok-cl2: rgb(254, 44, 85);
    --pinterest-cl: rgb(203, 32, 39);
    --tumblr-cl: rgb(55, 69, 92);
    --vimeo-cl: rgb(26, 183, 234);
    --snapchat-cl: rgb(255, 221, 0);
    --whatsapp-cl: rgb(0, 230, 118);
    --linked_in-cl: rgb(23, 106, 255);
    --wechat-cl: rgb(26, 173, 24);
    --reddit-cl: rgb(255, 69, 0);
    --line-cl: rgb(0, 195, 77);
    --spotify-cl: rgb(30, 125, 96);
}

.tf-social-icon .social-facebook:hover {
    background: var(--facebook-cl);
    color: var(--white);
}

.tf-social-icon .social-twiter:hover {
    background: var(--twitter-cl);
    color: var(--white);
}

.tf-social-icon .social-instagram:hover {
    background: var(--instagram-cl);
    color: var(--white);
}

.tf-social-icon .social-tiktok:hover {
    background: var(--tiktok-cl);
    color: var(--white);
}

.tf-social-icon .social-pinterest:hover {
    background: var(--pinterest-cl);
    color: var(--white);
}

.tf-social-icon.style-white li a {
    border-color: var(--white);
    color: var(--white);
}

.tf-social-icon .social-line {
    border: 1px solid var(--main);
}

.tf-social-icon .social-line.social-facebook:hover {
    background: transparent;
    color: var(--facebook-cl);
    border-color: var(--facebook-cl);
}

.tf-social-icon .social-line.social-twiter:hover {
    background: transparent;
    color: var(--twitter-cl);
    border-color: var(--twitter-cl);
}

.tf-social-icon .social-line.social-instagram:hover {
    background: transparent;
    color: var(--threads-cl);
    border-color: var(--threads-cl);
}

.tf-social-icon .social-line.social-tiktok:hover {
    background: transparent;
    color: var(--tiktok-cl2);
    border-color: var(--tiktok-cl2);
}

.tf-social-icon .social-line.social-pinterest:hover {
    background: transparent;
    color: var(--pinterest-cl);
    border-color: var(--pinterest-cl);
}

.tf-social-icon.style-default li {
    display: inline-flex;
}

.tf-social-icon.style-default li a {
    border: 0 !important;
}

.tf-social-icon.style-default li a:hover {
    background: unset !important;
}

.tf-social-icon.style-default li .social-facebook:hover {
    color: var(--facebook-cl);
}

.tf-social-icon.style-default li .social-twiter:hover {
    color: var(--twitter-cl);
}

.tf-social-icon.style-default li .social-instagram:hover {
    color: var(--threads-cl);
}

.tf-social-icon.style-default li .social-tiktok:hover {
    color: var(--tiktok-cl2);
}

.tf-social-icon.style-default li .social-pinterest:hover {
    color: var(--pinterest-cl);
}

.tf-icon-box .icon {
    margin-bottom: 34px;
}

.tf-icon-box .icon i {
    font-size: 30px;
    color: #c5c5c5;
}

.tf-icon-box .content .title {
    font-size: 18px;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 10px;
}

.tf-icon-box.style-border-line {
    border: 1px solid #ececec;
    padding: 26px 20px;
    border-radius: 10px;
}

.tf-icon-box.style-border-line .icon i {
    color: var(--main);
}

.tf-icon-box.style-row {
    display: flex;
    gap: 22px;
    align-items: center;
}

.tf-icon-box.style-row .icon {
    width: 60px;
    height: 60px;
    border: 1px solid #ececec;
    border-radius: 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
}

.tf-icon-box.style-row .icon i {
    color: var(--main);
}

.swiper-slide .tf-icon-box {
    width: 100%;
    height: 100%;
}

/* ------------ hover ---------------- */
.hover-img .img-style {
    overflow: hidden;
}

.hover-img .img-style img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    -webkit-transition: transform 2s cubic-bezier(0, 0, 0.44, 1.18);
    transition: transform 1.5s cubic-bezier(0, 0, 0.44, 1.18);
}

.hover-img:hover img {
    -webkit-transform: scale(1.06);
    transform: scale(1.06);
}

.hover-img .img-style2 {
    overflow: hidden;
    border-radius: 10px;
}

.hover-img .img-style2 .img-hv {
    width: 100%;
    object-fit: cover;
    -webkit-transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
    transition: all 1s cubic-bezier(0.3, 1, 0.35, 1) 0s;
    transition: transform 500ms ease;
}

.hover-img2 .img-style2 {
    overflow: hidden;
    border-radius: 8px;
}

.hover-img2 .img-style2 .img2 {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.hover-img2:hover .img2 {
    transform: scale(1.1) rotate(3deg);
}

.hover-img3 .img-style3 {
    border-radius: 8px;
    overflow: hidden;
}

.hover-img3 .img-style3 img {
    width: 100%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.hover-img3:hover img {
    transform: scale(1.075);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.pagi2 .swiper-pagination2:hover .box-img .icon-practice,
.swiper-button-next2:hover .box-img .icon-practice,
.swiper-button-prev2:hover .box-img .icon-practice,
.hv-one:hover .box-img .icon-practice {
    opacity: 1;
    z-index: 99;
    top: 50%;
    transition-delay: 0.5s;
}

.pagi2 .swiper-pagination2:hover .img-style::before,
.swiper-button-next2:hover .img-style::before,
.swiper-button-prev2:hover .img-style::before,
.hv-one:hover .img-style::before {
    opacity: 1;
}

.pagi2 .swiper-pagination2 .img-style,
.swiper-button-next2 .img-style,
.swiper-button-prev2 .img-style,
.hv-one .img-style {
    border-radius: 10px;
    overflow: hidden;
}

.pagi2 .swiper-pagination2 .img-style::before,
.swiper-button-next2 .img-style::before,
.swiper-button-prev2 .img-style::before,
.hv-one .img-style::before {
    content: "";
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    position: absolute;
    background: rgba(0, 0, 0, 0.5019607843);
    width: 100%;
    height: 100%;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -ms-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    z-index: 99;
    opacity: 0;
    border-radius: 10px;
}

.pagi2 .swiper-pagination2 .img-style.s-one::before,
.swiper-button-next2 .img-style.s-one::before,
.swiper-button-prev2 .img-style.s-one::before,
.hv-one .img-style.s-one::before {
    border-radius: 50%;
}

.hv-one2:hover .img-style2::before {
    opacity: 1;
    visibility: visible;
}

.hv-one2 .img-style2::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    -webkit-transition: all 0.4s ease-out 0s;
    -moz-transition: all 0.4s ease-out 0s;
    -ms-transition: all 0.4s ease-out 0s;
    -o-transition: all 0.4s ease-out 0s;
    transition: all 0.4s ease-out 0s;
    opacity: 0;
    visibility: hidden;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
    border-radius: 10px;
}

.hv-tool {
    position: relative;
    transition: all 0.3s ease;
}

/* ------------ collection ---------------- */
.collection-item .collection-inner {
    position: relative;
}

.collection-item .collection-image {
    width: 100%;
    display: block;
    border-radius: 10px;
    overflow: hidden;
}

.collection-item .collection-content {
    position: absolute;
    z-index: 3;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.collection-item .collection-title {
    line-height: 40px;
    height: 40px;
    font-size: 16px;
    padding: 0px 14px;
    background-color: var(--white);
    border-radius: 3px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.collection-item .collection-title .icon {
    color: var(--white);
    margin: 0;
}

.collection-item .collection-title:hover {
    background-color: var(--main);
    color: var(--white);
}

.collection-item.style-left .collection-content {
    left: 0;
    transform: unset;
}

.collection-item.style-left .collection-content .collection-title {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.collection-item-v2 .collection-inner {
    position: relative;
    width: 100%;
    height: 100%;
}

.collection-item-v2 .collection-image {
    width: 100%;
    height: 100%;
}

.collection-item-v2 .collection-content {
    position: absolute;
    inset: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.collection-item-v2 .collection-content .heading {
    margin-bottom: 7px;
    font-size: 18px;
    line-height: 21.6px;
}

.collection-item-v2 .collection-content .subheading {
    font-size: 16px;
    line-height: 19.2px;
    margin-bottom: 7px;
}

.collection-item-v2.type-small .collection-content .heading {
    font-size: 18px;
    margin-bottom: 0;
}

.collection-item-v2.type-small .collection-content p {
    font-size: 14px;
}

.collection-item-v3 .collection-image {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    width: 100%;

    /* border: 2px solid var(--main) */
}

.collection-item-v3 .collection-image .box-icon {
    position: absolute;
    border-radius: 50%;
    color: var(--main);
    font-size: 10px;
    bottom: 15px;
    right: 15px;
    background-color: var(--white);
    width: 35px;
    height: 35px;
}

.collection-item-v3 .collection-image .box-icon:hover {
    background-color: var(--main);
    color: var(--white);
}

.collection-item-v3 .collection-content {
    margin-top: 15px;
}

.collection-item-v3 .collection-content .title {
    font-size: 15px;
    line-height: 18px;
    margin-bottom: 5px;
}

.collection-item-v3 .collection-content .count {
    font-size: 14px;
    line-height: 22.4px;
}

.collection-item-v4 .collection-inner {
    position: relative;
}

.collection-item-v4 .collection-image {
    display: block;
}

.collection-item-v4 .collection-content {
    text-align: center;
    bottom: 7%;
    position: absolute;
    left: 7%;
    right: 7%;
}

.collection-item-v4 .collection-content .heading {
    font-size: 18px;
    line-height: 21.6px;
    text-transform: capitalize;
}

.collection-item-v4 .collection-content .subheading {
    font-size: 14px;
    line-height: 22.4px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 6px;
}

.collection-item-v4 .collection-content .tf-btn {
    margin-top: 14px;
}

.collection-item-v4.style-2 .collection-content {
    bottom: unset;
    top: 50%;
    transform: translateY(-50%);
}

.collection-item-v4.style-2 .collection-content .subtext {
    margin-top: 9px;
}

.collection-item-v4.style-2 .collection-content .heading {
    font-size: 22px;
    line-height: 26.4px;
}

.collection-item-v4.style-3 .collection-content {
    text-align: left;
}

.collection-item-v4.st-lg .collection-content .heading {
    font-size: 28px;
    line-height: 33.6px;
    margin-bottom: 10px;
}

.collection-item-v4.st-lg .collection-content .subtext {
    font-size: 16px;
    line-height: 19.2px;
}

.collection-item-v5 .collection-inner {
    position: relative;
    width: 100%;
    height: 100%;
}

.collection-item-v5 .collection-image {
    width: 100%;
    height: 100%;
    display: block;
}

.collection-item-v5 .collection-content {
    position: absolute;
    left: 15px;
    bottom: 15px;
}

.collection-item-v5 .collection-content .collection-title {
    padding: 10px 12px;
    font-size: 15px;
    line-height: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
    background-color: var(--white);
    border-radius: 3px;
    gap: 5px;
}

.collection-item-v5 .collection-content .collection-title .icon {
    font-size: 12px;
    font-weight: 500;
}

.collection-item-v5 .collection-content .collection-title:hover {
    background-color: var(--main);
    color: var(--white);
}

.collection-item-v6 .collection-inner {
    position: relative;
    width: 100%;
    height: 100%;
    display: block;
}

.collection-item-v6 .collection-image {
    width: 100%;
    height: 100%;
    display: block;
}

.collection-item-v6 .collection-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--primary);
    padding: 14px;
}

.collection-item-v6 .collection-content .heading {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 2px;
}

.collection-item-v6 .collection-content .subheading {
    font-size: 16px;
    line-height: 19.2px;
}

.collection-item-circle {
    position: relative;
}

.collection-item-circle .collection-image {
    border-radius: 50%;
    overflow: hidden;
    display: block;
}

.collection-item-circle .collection-content {
    margin-top: 15px;
}

.collection-item-circle .collection-content .title {
    margin-bottom: 5px;
    font-size: 15px;
    line-height: 18px;
    font-weight: 500;
}

.collection-item-circle .collection-content .count {
    color: var(--text);
}

.collection-item-circle .sale-off {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 10px;
    line-height: 19px;
    padding: 0 5px;
    background-color: var(--main);
    border-radius: 999px;
    color: var(--white);
}

.collection-item-circle.has-bg {
    padding: 15px 0;
    background-color: var(--bg-2);
    border-radius: 20px;
}

.collection-item-circle.has-bg .collection-image {
    max-width: 60%;
    margin-left: auto;
    margin-right: auto;
}

.collection-item-circle.has-bg .collection-content {
    margin-top: 19px;
}

.collection-item-circle.has-bg .collection-content .title {
    margin-bottom: 0;
}

.collection-item-circle.has-bg-2 {
    background-color: #f2f2f2;
    border-radius: 3px;
}

.collection-item-circle.circle-line .collection-image {
    padding: 15px;
    border: 1px solid var(--primary);
}

.collection-item-circle.circle-line .collection-content {
    margin-top: 20px;
}

.collection-item-circle.circle-line .collection-content .title {
    margin: 0;
}

.tf-shopall .tf-shopall-icon {
    width: 99px;
    height: 99px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--main);
    font-size: 10px;
    margin-left: auto;
    margin-right: auto;
}

.tf-categories-wrap {
    display: flex;
    overflow-x: auto;
}

.tf-categories-wrap .tf-categories-container {
    display: flex;
}

.tf-categories-wrap .collection-item-circle img {
    max-width: 104px;
}

.tf-categories-wrap .collection-item-circle {
    min-width: 155px;
}

.tf-categories-wrap .collection-item-circle .has-saleoff-wrap,
.tf-categories-wrap .collection-item-circle .collection-image {
    width: 99px;
    height: 99px;
    margin-left: auto;
    margin-right: auto;
}

.tf-categories-wrap .collection-content {
    margin-top: 17px !important;
}

.tf-categories-wrap .collection-content a {
    font-size: 14px !important;
    line-height: 17px !important;
}

.slider-collection {
    padding-bottom: 6px;
}

.slider-collection .collection-item .collection-title {
    padding: 0 16px;
    line-height: 44px;
    height: 44px;
    font-size: 16px;
    background-color: var(--white);
    color: var(--main);
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

.slider-collection .collection-item .collection-title .icon {
    display: none;
    font-size: 10px;
    margin-top: 2px;
}

.slider-collection .collection-item .collection-title:hover {
    background-color: var(--main);
    color: var(--white);
}

.collection-title-v2 {
    font-size: 16px;
    font-weight: 500;
    line-height: 48px;
    border-radius: 3px;
    padding: 0px 30px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    background-color: var(--white);
}

.collection-title-v2:hover {
    color: var(--white);
    background-color: var(--main);
}

.discovery-new-item {
    padding: 37px;
    height: 100%;
    border: 1px solid var(--main);
    display: flex;
    flex-direction: column;
    justify-content: end;
    border-radius: 10px;
    gap: 30px;
}

.discovery-new-item a {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--main);
    border-radius: 50%;
    font-size: 10px;
    color: var(--main);
}

.discovery-new-item a:hover {
    background-color: var(--main);
    color: var(--white);
}

.banner-gr-item {
    position: relative;
}

.banner-gr-item .img-style {
    margin-bottom: 30px;
}

.banner-gr-item .content .title {
    font-size: 28px;
    line-height: 34px;
}

.banner-gr-item .content > a {
    margin-top: 18px;
}

.banner-gr-item.style-content-absolute .content {
    position: absolute;
    left: 30px;
    right: 30px;
}

.banner-gr-item.style-content-absolute .content p {
    color: var(--main);
}

.banner-gr-item.style-content-absolute .absolute-bot {
    bottom: 50px;
    top: unset;
}

.banner-gr-item.style-content-absolute .absolute-top {
    top: 50px;
    bottom: unset;
}

.banner-gr-item.style-content-absolute .absolute-center {
    top: 50%;
    transform: translateY(-50%);
    bottom: unset;
}

.tf-sw-collection .swiper-slide {
    height: auto;
}

.tf-sw-collection .swiper-slide img {
    height: 100%;
}

.tf-banner-collection {
    position: relative;
}

.tf-banner-collection img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}

.tf-banner-collection .box-content {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}

.tf-banner-collection .box-content .sub {
    line-height: 17px;
}

.tf-banner-collection .box-content .heading {
    font-size: 42px;
    line-height: 50px;
    margin-top: 5px;
}

.tf-banner-collection .box-content p {
    font-size: 20px;
    line-height: 32px;
    margin-top: 15px;
    margin-bottom: 26px;
}

.collection-line-upper .collection-inner {
    position: relative;
    width: 100%;
    height: 100%;

    /* border: 2px solid var(--main) */
}

.collection-line-upper .collection-image {
    width: 100%;
    height: 100%;
}

.collection-line-upper .collection-content {
    position: absolute;
    bottom: 10%;
    left: 10%;
    right: 10%;
}

.collection-line-upper .collection-content .collection-title {
    font-size: 12px;
    line-height: 14.4px;
    text-transform: uppercase;
    padding: 10px 24px;
    font-weight: 700;
}

.collection-line-upper .collection-content .subheading {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 22.4px;
    text-transform: uppercase;
    font-weight: 700;
}

.collection-line-upper .collection-content .heading {
    margin-bottom: 11px;
    font-size: 18px;
    line-height: 21.6px;
    text-transform: uppercase;
    font-weight: 800;
}

.collection-item-centered {
    position: relative;
}

.collection-item-centered .img-style {
    width: 100%;
    height: 100%;
}

.collection-item-centered .content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.collection-item-centered .content .tf-btn {
    padding: 0px 16px;
    line-height: 44px;
    border: 0;
}

.collection-item-centered .content .tf-btn span {
    text-wrap: nowrap;
}

.flat-categories-bg {
    background-color: #f8f8f8;
    border-radius: 10px;
    overflow: hidden;
    padding: 17px 15px;
}

.flat-categories-bg .collection-item-circle .collection-content {
    margin-top: 20px;
}

.flat-categories-bg .collection-item-circle .collection-content .title {
    font-size: 16px;
    line-height: 19.2px;
    margin-bottom: 0;
}

.flat-categories-bg .nav-sw {
    border: 0;
    top: 44%;
}

.flat-categories-bg .nav-sw.nav-next-slider {
    left: 0;
}

.flat-categories-bg .nav-sw.nav-prev-slider {
    right: 0;
}

.flat-categories-bg .nav-sw:hover {
    color: var(--primary);
    background-color: transparent;
}

.swiper-slide .collection-item-v4,
.swiper-slide .collection-item-v4 .collection-inner,
.swiper-slide .collection-item-v4 .collection-image {
    height: 100%;
}

/* ------------ product ---------------- */
.thumbs-slider {
    display: flex;
    gap: 10px;
}

.tf-product-media-thumbs {
    width: calc(14% - 10px);
    max-height: 846px;
}

.tf-product-media-thumbs .swiper-slide {
    height: max-content;
}

.tf-product-media-thumbs .swiper-slide .item {
    position: relative;
    height: 100%;
}

.tf-product-media-thumbs .swiper-slide .item img {
    border-radius: 5px;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-product-media-thumbs .swiper-slide .item::after {
    position: absolute;
    content: "";
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    border: 2px solid transparent;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 0px;
}

.tf-product-media-thumbs .swiper-slide.swiper-slide-thumb-active .item::after {
    border: 2px solid var(--main);
}

.thumbs-bottom .thumbs-slider {
    flex-direction: column;
}

.thumbs-bottom .thumbs-slider .tf-product-media-thumbs {
    order: 1;
    width: 100%;
}

.thumbs-bottom .thumbs-slider .tf-product-media-thumbs .swiper-slide {
    width: 119px;
}

.thumbs-bottom .thumbs-slider .tf-product-media-main {
    width: 100%;
}

.tf-product-media-main {
    width: 86%;
}

.tf-product-media-main .item {
    width: 100%;
    height: 100%;
    max-height: 846px;
}

.tf-product-media-main .item img {
    border-radius: 10px;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-product-info-list {
    padding-left: 25px;
}

.tf-product-info-list > div:not(:last-child) {
    margin-bottom: 30px;
}

.tf-product-info-title {
    margin-bottom: 20px !important;
}

.tf-product-info-badges {
    margin-bottom: 15px !important;
    display: flex;
    gap: 14px;
    align-items: center;
    flex-wrap: wrap;
}

.tf-product-info-badges .badges {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 0 14px;
    border: 1px solid var(--main);
    border-radius: 0px;
    font-size: 10px;
    font-weight: 700;
    line-height: 29px;
}

.tf-product-info-badges .product-status-content {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.tf-product-info-badges .product-status-content svg {
    animation: tf-ani-flash 2s infinite;
}

.tf-product-info-badges .product-status-content i {
    font-size: 20px;
    color: var(--primary);
    animation: tf-ani-flash 2s infinite;
}

.tf-product-info-price {
    display: flex;
    gap: 10px;
    align-items: center;
}

.tf-product-info-price .price {
    color: var(--main);
    font-size: 14px;
    line-height: 28px;
}

.tf-product-info-price .price-on-sale {
    color: var(--main);
    font-size: 14px;
    line-height: normal;
}

.tf-product-info-price .compare-at-price {
    color: rgba(0, 0, 0, 0.55);
    font-size: 14px;
    line-height: normar;
    text-decoration: line-through;
}

.tf-product-info-price .badges-on-sale {
    background-color: rgb(252, 87, 50);
    border-radius: 0px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 700;
    line-height: 28px;
    color: var(--white);
}

.tf-product-info-liveview {
    display: flex;
    gap: 10px;
    align-items: center;
}

.tf-product-info-liveview .liveview-count {
    display: flex;
    justify-content: center;
    min-width: 34px;
    min-height: 24px;
    line-height: 24px;
    color: var(--white);
    background-color: var(--main);
    border-radius: 3px;
    font-weight: 600;
}

.tf-product-info-countdown .countdown-wrap {
    display: inline-block;
    padding: 16px 30px;
    border: 1px solid var(--primary);
    border-radius: 2.5px;
    text-align: center;
    min-width: 367px;
}

.tf-product-info-countdown .countdown-wrap .countdown-title {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.tf-product-info-countdown .countdown-wrap .countdown-title i {
    font-size: 14px;
    color: var(--main);
}

.tf-product-info-countdown .countdown-wrap .countdown-title p {
    font-size: 10px;
    font-weight: 700;
    line-height: 16px;
}

@-webkit-keyframes tf-ani-tada {
    from,
    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    10%,
    20% {
        -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }
}

@keyframes tf-ani-tada {
    from,
    to {
        -webkit-transform: scale3d(1, 1, 1);
        transform: scale3d(1, 1, 1);
    }

    10%,
    20% {
        -webkit-transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    }

    30%,
    50%,
    70%,
    90% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }

    40%,
    60%,
    80% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }
}

.tf-ani-tada {
    -webkit-animation: tf-ani-tada 2s infinite;
    animation: tf-ani-tada 2s infinite;
}

.tf-product-info-variant-picker {
    display: flex;
    gap: 20px;
    flex-direction: column;
}

.tf-product-item-property input {
    height: 42px;
}

.tf-product-item-property label {
    font-weight: 400;
    color: var(--text-3);
}

.tf-product-info-quantity .quantity-title {
    margin-bottom: 5px;
}

.tf-product-info-buy-button form {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.tf-product-info-buy-button form .btns-full {
    width: 100%;
    margin-top: 10px;
    display: flex;
    gap: 5px;
    align-items: center;
    justify-content: center;
    height: 44px;
    background-color: #ffc520;
    border-radius: 3px;
    font-weight: 500;
    color: rgb(37, 59, 128);
}

.tf-product-info-buy-button form .btns-full:hover {
    background-color: #f6b600;
}

.tf-product-info-buy-button form .payment-more-option {
    width: 100%;
    margin-top: 10px;
    text-decoration: underline;
    color: rgb(134, 134, 134);
    text-align: center;
}

.btns-sold-out {
    opacity: 0.3;
}

.btns-sold-out::after {
    display: none;
}

.tf-pickup-availability {
    display: flex;
    gap: 10px;
}

.tf-pickup-availability a {
    font-size: 12px;
    line-height: 20px;
    text-decoration: underline;
}

.tf-pickup-availability-list {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tf-pickup-availability-list .tf-pickup-availability-location {
    font-size: 20px;
    line-height: 24px;
    margin-bottom: 15px;
}

.tf-pickup-availability-list .tf-pickup-availability {
    margin-top: 15px;
    margin-bottom: 20px;
}

.tf-pickup-availability-list .tf-btn {
    margin-top: 14px;
}

.tf-product-info-extra-link {
    display: flex;
    align-items: center;
    gap: 10px 30px;
    flex-wrap: wrap;
}

.tf-product-info-extra-link .tf-product-extra-icon {
    display: flex;
    gap: 8px;
    align-items: center;
}

.tf-product-info-extra-link .tf-product-extra-icon i {
    font-size: 18px;
}

.tf-product-info-extra-link .tf-product-extra-icon:hover {
    color: var(--primary);
}

.tf-product-delivery {
    padding: 30px 28px;
    text-align: center;
    display: flex;
    gap: 16px;
    flex-direction: column;
    border-radius: 2.5px;
    border: 1px solid var(--line);
}

.tf-product-delivery i {
    font-size: 29px;
}

.tf-product-order {
    display: flex;
    gap: 14px;
    align-items: center;
}

.tf-product-order .icon i {
    font-size: 24px;
}

.tf-product-info-trust-seal {
    display: flex;
    gap: 24px;
    align-items: center;
    flex-wrap: wrap;
}

.tf-product-info-trust-seal .tf-product-trust-mess {
    display: flex;
    gap: 10px;
    align-items: center;
}

.tf-product-info-trust-seal .tf-product-trust-mess i {
    font-size: 21px;
    color: var(--main);
}

.find-size {
    text-decoration: underline !important;
    text-underline-offset: 5px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer;
}

.find-size:hover {
    color: var(--primary);
}

.variant-picker-item .variant-picker-label {
    margin-bottom: 15px;
}

.variant-picker-item .variant-picker-values {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.variant-picker-item .variant-picker-values input {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
}

.variant-picker-item .variant-picker-values input:checked + label {
    border-color: var(--main);
    box-shadow: 0 0.4rem 0.4rem rgba(0, 0, 0, 0.1019607843);
}

.variant-picker-item .variant-picker-values input:checked + label.style-text {
    background-color: var(--main);
}

.variant-picker-item .variant-picker-values input:checked + label.style-text p {
    color: var(--white);
}

.variant-picker-item .variant-picker-values input:checked + label.style-image {
    border-color: var(--main);
}

.variant-picker-item .variant-picker-values label {
    position: relative;
    width: 36px;
    height: 36px;
    text-align: center;
    padding: 5px;
    border: 1px solid transparent;
    cursor: pointer;
    font-weight: 400;
    line-height: 22.4px;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.variant-picker-item .variant-picker-values label:hover {
    border-color: var(--main);
}

.variant-picker-item .variant-picker-values label .btn-checkbox {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 50%;
    border: 1px solid rgba(134, 134, 134, 0.12);
}

.variant-picker-item .variant-picker-values label.style-text {
    min-width: 45px;
    height: 38px;
    width: max-content;
    border: 1px solid rgba(134, 134, 134, 0.12);
    border-radius: 3px;
    padding: 7px 15px;
}

.variant-picker-item .variant-picker-values label.style-text:hover {
    border-color: var(--main);
}

.variant-picker-item .variant-picker-values label.style-image {
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(134, 134, 134, 0.12);
    border-radius: 3px;
    padding: 5px;
    width: 90px;
    height: unset;
}

.variant-picker-item .variant-picker-values label.style-image .image img {
    height: 112.5px;
}

.variant-picker-item .variant-picker-values label.style-image p {
    font-size: 12px;
    font-weight: 600;
    line-height: 19px;
    color: var(--main);
    margin-top: 5px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
}

.variant-picker-item .variant-picker-values label.image-rounded {
    width: 40px;
    height: 40px;
}

.variant-picker-item .variant-picker-values label.image-rounded img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 999px;
}

.variant-picker-item .variant-picker-values label.sold-out {
    opacity: 0.5;
    border-color: rgba(0, 0, 0, 0.1) !important;
    color: rgba(0, 0, 0, 0.6);
    text-decoration: line-through;
}

.variant-picker-item .variant-picker-values label.sold-out-line::after {
    content: "";
    width: 90%;
    height: 1px;
    background: rgb(34, 34, 34);
    display: block;
    position: absolute;
    z-index: 22;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(135deg);
}

.variant-picker-item .variant-picker-values label .tooltip {
    left: 50%;
    transform: translateX(-50%);
}

.variant-picker-item .variant-picker-values label.rectangle-color {
    display: flex;
    align-items: center;
    gap: 4px;
}

.variant-picker-item .variant-picker-values label.rectangle-color > span {
    width: 15px;
    height: 15px;
}

.tf-product-bundle-wrap {
    padding: 34px;
    border: 1px solid var(--line);
    border-radius: 2.5px;
}

.tf-product-bundle-wrap > .title {
    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    color: var(--main);
    margin-bottom: 25px;
}

.tf-product-bundle-wrap .tf-btn {
    height: 49px;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
}

.tf-bundle-product-item {
    display: flex;
    gap: 20px;
}

.tf-bundle-product-item.type-lg {
    gap: 37px;
    margin-top: 32px;
    padding-bottom: 32px;
    border-bottom: 1px dashed var(--line);
}

.tf-bundle-product-item.type-lg img {
    width: 160px;
    min-width: 160px;
    max-width: unset;
    max-height: 223px;
}

.tf-bundle-product-item.type-lg .tf-product-bundle-title {
    margin-bottom: 10px;
}

.tf-bundle-product-item.type-lg .tf-product-bundle-price {
    margin-bottom: 17px;
}

.tf-product-bundle-image img {
    width: 83px;
    min-width: 83px;
    max-width: 83px;
    border-radius: 2.5px;
}

.tf-product-bundle-infos > a:hover {
    color: var(--primary);
}

.tf-product-bundle-infos .tf-product-bundle-title {
    margin-bottom: 5px;
}

.tf-product-bundle-infos .tf-product-bundle-variant {
    margin-bottom: 10px;
}

.tf-product-bundle-infos .tf-product-bundle-variant select {
    height: 40px;
}

.tf-product-bundle-infos .tf-product-bundle-price {
    display: flex;
    gap: 5px;
}

.tf-product-bundle-infos .tf-product-bundle-price .compare-at-price {
    color: var(--main);
    text-decoration: line-through;
}

.tf-product-bundle-infos .tf-product-bundle-price .price-on-sale {
    font-weight: 600;
    color: var(--primary);
}

.tf-product-bundle-infos .tf-product-bundle-price .price {
    font-weight: 600;
    color: var(--main);
}

.tf-product-bundle-total-submit {
    display: flex;
    align-items: center;
    margin-top: 16px;
    margin-bottom: 16px;
}

.tf-product-bundle-total-submit .text {
    margin-right: 5px;
    font-size: 20px;
    font-weight: 600;
    line-height: 32px;
    color: var(--main);
}

.tf-product-bundle-total-submit .compare-at-price {
    margin-right: 10px;
    font-size: 28px;
    font-weight: 400;
    line-height: 45px;
    color: var(--primary);
}

.tf-product-bundle-total-submit .price-on-sale {
    font-size: 20px;
    font-weight: 400;
    line-height: 32px;
    color: rgba(0, 0, 0, 0.55);
    text-decoration: line-through;
}

.tf-bundle-check input {
    position: absolute !important;
    overflow: hidden;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
    clip: rect(0 0 0 0);
    word-wrap: normal !important;
}

.tf-bundle-check input:checked + label {
    background-color: var(--primary);
    border-color: var(--primary);
}

.tf-bundle-check input:checked + label i {
    transform: scale(1);
}

.tf-bundle-check label {
    width: 19px;
    height: 19px;
    border: 1px solid;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
}

.tf-bundle-check label i {
    display: block;
    color: var(--white);
    transform: scale(0);
    transition: 0.25s ease-in-out;
    font-size: 8px;
}

.item-has-checkox {
    opacity: 0.2;
}

.item-has-checkox.check {
    opacity: 1;
}

.tf-product-fbt-wrap {
    margin-top: 50px;
}

.tf-product-fbt-wrap > .title {
    font-size: 28px;
    font-weight: 400;
    line-height: 34px;
    color: var(--main);
    margin-bottom: 40px;
}

.tf-product-fbt-wrap form {
    display: flex;
    flex-wrap: wrap;
    gap: 120px;
}

.tf-product-fbt-wrap form .tf-product-fbt-list {
    display: flex;
    gap: 8px;
    align-items: center;
    min-width: max-content;
}

.tf-product-fbt-wrap form .tf-product-fbt-list img {
    width: 122px;
    max-width: 122px;
    height: 170.5px;
    object-fit: cover;
}

.tf-product-fbt-wrap form .tf-product-fbt-list .tf-product-fbt-plus {
    width: 26px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid var(--line);
}

.tf-product-fbt-wrap form .tf-fbt-swatches {
    margin-top: 22px;
    margin-bottom: 22px;
}

.tf-product-fbt-wrap form .tf-product-bundle-infos {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    row-gap: 5px;
}

.tf-product-fbt-wrap form .tf-product-bundle-infos:not(:last-child) {
    margin-bottom: 12px;
}

.tf-product-fbt-wrap form .tf-product-bundle-infos .tf-bundle-check {
    margin-right: 5px;
}

.tf-product-fbt-wrap form .tf-product-bundle-infos .tf-product-bundle-variant,
.tf-product-fbt-wrap form .tf-product-bundle-infos .tf-product-bundle-title {
    margin-bottom: 0;
    margin-right: 20px;
}

.tf-product-fbt-wrap form .tf-fbt-col-total-price {
    padding: 40px 20px;
    background-color: rgb(242, 242, 242);
    height: max-content;
}

.tf-product-fbt-wrap form .tf-fbt-col-total-price .text {
    width: 100%;
}

.tf-product-fbt-wrap form .tf-fbt-col-total-price .tf-btn {
    height: 49px;
    font-size: 16px;
    font-weight: 600;
    line-height: 19px;
}

.tf-product-inventory svg {
    color: rgb(66, 132, 69);
}

.tf-progress-bar {
    height: 4px;
    width: 100%;
    background-color: var(--line);
    position: relative;
}

.tf-progress-bar span {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    bottom: 0;
    background-color: var(--primary);
    border-radius: 3px;
}

.tf-progress-bar .progress-car {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 36px;
    height: 26px;
    color: var(--primary);
    border: 1px solid var(--primary);
    border-radius: 2.5px;
    background-color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
}

.tf-product-notify-stock {
    padding: 36px;
    border: 1px solid var(--main);
}

.tf-product-notify-stock .tf-product-notify-stock-heading {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: var(--main);
}

.tf-product-notify-stock .tf-product-notify-stock-heading div {
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
    color: var(--main);
}

.tf-product-notify-stock p {
    margin-bottom: 20px;
}

.tf-product-notify-stock input,
.tf-product-notify-stock select {
    width: 100%;
    margin-bottom: 20px;
    height: 50px;
}

.tf-qol-head {
    display: flex;
    padding-bottom: 14px;
    border-bottom: 1px solid var(--line);
}

.tf-qol-head > div {
    width: 25%;
}

.tf-qol-head p {
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
}

.tf-quick-order-list-total {
    position: sticky;
    position: -webkit-sticky;
    bottom: 0;
}

.tf-quick-order-list-total .tf-total-wrap {
    padding: 20px 0;
    border-top: 1px solid var(--line);
    display: flex;
    background-color: var(--white);
}

.tf-quick-order-list-total .tf-total-wrap > div {
    width: 25%;
}

.tf-quick-order-list-total .tf-total-item-inner {
    width: 109px;
    text-align: center;
}

.tf-quick-order-list-total .tf-total-price {
    text-align: end;
}

.tf-quick-order-list-total .tf-total-price .price {
    font-size: 18px;
    line-height: 29px;
}

.tf-quick-order-list-total .tf-total-price p {
    color: var(--text);
}

.tf-quick-order-list-total .tf-total-price a {
    color: var(--text);
    text-decoration-line: underline;
    text-underline-offset: 3px;
}

.tf-quick-order-list-total .tf-total-price a:hover {
    color: var(--main);
    text-decoration-thickness: 2px;
}

.tf-variant-item:not(:last-child) {
    border-bottom: 1px dashed var(--line);
}

.tf-variant-item > * {
    width: 25%;
    padding: 17px 0;
}

.tf-variant-item .tf-variant-item-media {
    display: flex;
    gap: 18px;
    align-items: center;
}

.tf-variant-item .tf-variant-item-media .tf-variant-item-image-container {
    width: 80px;
    height: 111.7px;
}

.tf-variant-item .tf-variant-item-media .tf-variant-item-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-variant-item .tf-price-list {
    display: flex;
    gap: 10px;
    justify-content: end;
    align-items: center;
}

.tf-variant-item .tf-price-list .price-on-sale {
    font-size: 13px;
    font-weight: 600;
    line-height: 13px;
    color: rgba(0, 0, 0, 0.55);
    text-decoration: line-through;
}

.tf-variant-item .tf-price-list .compare-at-price {
    font-weight: 600;
    color: var(--primary);
    line-height: 13px;
}

.wrap-btn-viewer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
}

.wrap-btn-viewer i {
    font-size: 20px;
}

.wrap-btn-viewer.style-video .icon {
    width: 20px;
    height: 20px;
    padding-left: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    border: 2px solid var(--main);
    border-radius: 50%;
}

.wrap-btn-viewer.style-video .icon i {
    font-size: 8px;
}

.tf-product-btn-wishlist {
    width: 49px;
    height: 49px;
    flex-shrink: 0;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--line);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tf-product-btn-wishlist .icon,
.tf-product-btn-wishlist i {
    font-size: 18px;
    color: var(--main);
}

.tf-product-btn-wishlist:hover {
    border-color: var(--main);
}

.tf-product-btn-wishlist .tooltip {
    top: -100%;
    margin-top: 5px;
}

.tf-product-btn-wishlist .tooltip::before {
    top: unset;
    bottom: -4px;
}

.btn-icon-action > *:last-child {
    display: none;
}

.btn-icon-action.active > *:first-child {
    display: none;
}

.btn-icon-action.active > *:last-child {
    display: block;
}

.wg-quantity {
    width: 127px;
    display: flex;
    justify-content: space-between;
    background-color: rgb(242, 242, 242);
    border-radius: 3px;
    overflow: hidden;
}

.wg-quantity input {
    width: 51px;
    height: 46px;
    padding: 0;
    background-color: transparent;
    border: 0;
    text-align: center;
    font-size: 16px;
    font-weight: 600;
    line-height: 26px;
    color: var(--main);
}

.wg-quantity .btn-quantity {
    width: 38px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--main);
    cursor: pointer;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.wg-quantity .btn-quantity:hover {
    color: var(--primary);
}

.wg-quantity.small {
    width: 109px;
    height: 30px;
}

.wg-quantity.small .btn-quantity {
    width: 27px;
    height: 30px;
    font-size: 20px;
}

.wg-quantity.small input {
    width: 30px;
    height: 30px;
    font-size: 12px;
    line-height: 30px;
}

@keyframes tf-ani-flash {
    50%,
    from,
    to {
        opacity: 1;
    }

    25%,
    75% {
        opacity: 0;
    }
}

.tf-product-des-demo {
    display: grid;
    gap: 30px;
    grid-template-columns: 4fr 8fr;
}

.tf-product-des-demo h3 {
    margin-bottom: 22px;
    line-height: 19px;
}

.tf-product-des-demo ul {
    margin-top: 15px;
    margin-bottom: 30px;
}

.tf-product-des-demo ul li {
    color: var(--text-2);
    line-height: 25px;
    position: relative;
    padding-left: 20px;
}

.tf-product-des-demo ul li:not(:last-child) {
    margin-bottom: 15px;
}

.tf-product-des-demo ul li::before {
    position: absolute;
    content: "";
    left: 5px;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--text-2);
    width: 4px;
    height: 4px;
    border-radius: 50%;
}

.tf-product-des-demo .left .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    border: 1px solid var(--main);
}

.tf-product-des-demo .left .icon i {
    font-size: 12px;
}

.tf-product-des-demo .left span {
    color: var(--text-2);
}

.tf-pr-attrs {
    border-spacing: 0;
    border-collapse: collapse;
    width: 100%;
    border-radius: 5px;
    border-style: hidden;
    box-shadow: 0 0 0 0.1rem var(--line);
    color: var(--text-2);
}

.tf-pr-attrs tr {
    border: 1px solid var(--line);
    vertical-align: middle;
}

.tf-pr-attrs tr th {
    padding: 10px;
    border-right: 1px solid var(--line);
    font-weight: 700;
}

.tf-pr-attrs tr td {
    padding: 10px;
}

.tf-page-privacy-policy .title {
    margin-bottom: 26px;
    font-size: 22px;
    font-weight: 400;
    line-height: 28px;
}

.tf-page-privacy-policy p {
    margin-bottom: 24px;
    line-height: 24px;
    color: var(--text-2);
}

.tf-page-privacy-policy p:last-child {
    margin-bottom: 0;
}

.stagger-wrap .stagger-item {
    transition: 0.3s ease-in-out;
    transform: scale(0.5) rotate(90deg) skew(15deg);
    opacity: 0;
}

.stagger-wrap .stagger-item.stagger-finished {
    transform: scale(1) rotate(0deg) skew(0deg);
    opacity: 1;
}

.card-product {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.card-product .product-img {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;
    align-items: stretch;
}

.card-product .card-product-wrapper {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
    z-index: 20;
}

.card-product .card-product-wrapper .sold-out {
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--bg-11);
    border-radius: 999px;
    height: 73px;
    width: 73px;
    padding: 0 5px;
}

.card-product .card-product-wrapper .sold-out span {
    font-size: 12px;
    line-height: 16px;
    font-weight: 500;
    background-color: var(--bg-11);
    position: relative;
    z-index: 12;
}

.card-product .card-product-wrapper .sold-out::after {
    height: 1px;
    width: calc(100% - 16px);
    position: absolute;
    z-index: 1;
    opacity: 0.1;
    content: "";
    background-color: var(--main);
    transform: rotate(-45deg);
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}

.card-product .card-product-wrapper img {
    display: block;
    height: 100%;
    width: 100%;
    object-fit: cover;
    object-position: center;
    transition: opacity 0.5s ease, transform 2s cubic-bezier(0, 0, 0.44, 1.18);
}

.card-product .card-product-wrapper .img-hover {
    position: absolute;
    inset: 0;
    opacity: 0;
}

.card-product .card-product-wrapper:hover .product-img .img-product {
    opacity: 0;
}

.card-product .card-product-wrapper:hover .product-img .img-hover {
    display: block;
    z-index: 1;
    opacity: 1;
    -webkit-transform: scale(1.09);
    transform: scale(1.09);
}

.card-product .card-product-wrapper .size-list {
    position: absolute;
    z-index: 20;
    bottom: 0;
    left: 0;
    right: 0;
}

.card-product .card-product-wrapper .list-product-btn {
    z-index: 10;
    position: absolute;
    bottom: 48px;
    left: 15px;
    right: 15px;
}

.card-product .card-product-wrapper .list-product-btn.absolute-2 {
    bottom: 25px;
}

.card-product .card-product-wrapper .list-product-btn.absolute-3 {
    bottom: 57px;
}

.card-product .card-product-wrapper .on-sale-wrap {
    position: absolute;
    top: 5px;
    right: 5px;
    left: 5px;
    z-index: 5;
}

.card-product .card-product-wrapper .on-sale-wrap .on-sale-item {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    word-break: break-word;
    padding: 0 6px;
    min-width: 40px;
    text-transform: capitalize;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    line-height: 22px;
    position: relative;
    background-color: #fc5732;
    color: var(--white);
    border-radius: 999px;
}

.card-product .card-product-wrapper .on-sale-wrap .new {
    background-color: #ff3232;
}

.card-product .card-product-wrapper .on-sale-wrap .best-seller {
    background-color: #44ad55;
}

.card-product .card-product-wrapper .on-sale-wrap .recommend {
    background-color: #bea36e;
}

.card-product .card-product-wrapper .on-sale-wrap .pre-order {
    background-color: #55a653;
    color: var(--white);
}

.card-product .card-product-wrapper .column-left {
    bottom: unset !important;
    right: unset !important;
    top: 15px;
}

.card-product .card-product-wrapper .column-right {
    bottom: unset !important;
    left: unset !important;
    top: 15px;
}

.card-product .btn-quick-add {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    transition: transform 0.4s ease 0s, opacity 0.4s ease 0s;
    z-index: 12;
    height: 32px;
    background-color: var(--main);
    color: var(--white);
    font-size: 12px;
    line-height: 32px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-product .card-product-info {
    padding-top: 10px;
    gap: 8px;
    display: grid;
}

.card-product .card-product-info .title {
    font-size: 16px;
    line-height: 19.2px;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

.card-product .card-product-info .price {
    font-size: 14px;
    line-height: 14px;
    font-weight: 600;
}

.card-product .card-product-info .old-price {
    text-decoration: line-through;
    display: inline-block;
    margin-right: 5px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.5);
}

.card-product .card-product-info .new-price {
    color: var(--red_1);
}

.card-product .card-product-info.has-padding {
    padding-right: 15px;
    padding-left: 15px;
    padding-bottom: 30px;
}

.card-product .countdown-box {
    position: absolute;
    z-index: 3;
    left: 5px;
    right: 5px;
    text-align: center;
    pointer-events: none;
    transition: 0.4s ease-out 0s;
    overflow: hidden;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    padding: 5px;
    max-height: 40px;
    background-color: var(--white);
    border-radius: 3px;
}

.card-product .countdown-box .countdown__timer {
    display: flex;
    gap: 4px;
}

.card-product .countdown-box .countdown__item {
    font-size: 14px;
    line-height: 22.4px;
    color: var(--primary);
    font-weight: 600;
}

.card-product .description {
    display: none;
    font-size: 14px;
    line-height: 22.4px;
    margin-top: 2px;
}

.card-product.none-hover .card-product-wrapper:hover .img-product {
    opacity: 1;
}

.card-product.none-hover .card-product-wrapper:hover .img-hover {
    display: none;
}

.card-product.style-4 .size-list {
    transition: 0.4s ease 0.1s;
}

.card-product.style-5 .list-product-btn .box-icon {
    border-radius: 0;
}

.card-product.style-5 .list-product-btn .box-icon:first-child {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.card-product.style-5 .list-product-btn .box-icon:last-child {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

.card-product.style-6 .btn-quick-add {
    position: relative;
    border-radius: 3px;
    background-color: var(--white);
    color: var(--main);
    border: 1px solid var(--main);
    box-shadow: 0 8px 24px rgba(149, 157, 165, 0.2);
}

.card-product.style-6 .btn-quick-add:hover {
    background-color: var(--main);
    color: var(--white);
}

.card-product.style-7 .btn-quick-add {
    position: relative;
    box-shadow: 0 8px 24px rgba(149, 157, 165, 0.2);
    border-radius: 3px;
}

.card-product.style-7 .card-product-info {
    padding-top: 5px;
    transition: 0.4s ease 0s;
    position: relative;
    padding-bottom: 6px;
}

.card-product.style-8 {
    border: 1px solid var(--line-2);
    border-radius: 10px;
    padding: 5px 5px 20px;
}

.card-product.style-8 .card-product-info {
    padding-left: 5px;
    padding-right: 5px;
}

.card-product.style-8 .card-product-info .tf-btn {
    justify-content: center;
    border: 1px solid var(--line-2);
    font-size: 12px;
    line-height: 42px;
    font-weight: 700;
    background-color: var(--white);
    padding: 0 10px;
    border-radius: 999px;
}

.card-product.style-8 .card-product-info .tf-btn:hover {
    background-color: var(--main);
    color: var(--white);
}

.card-product.style-8 .progress {
    --bs-progress-height: 6px;
    --bs-progress-bar-bg: var(--primary);
    --bs-progress-bg: var(--line-2);
    --bs-progress-border-radius: 3px;
}

.card-product.style-8 .pr-stock {
    margin-top: 10px;
    display: grid;
    gap: 5px;
}

.card-product.style-8 .pr-stock-status span {
    font-size: 14px;
    line-height: 22.4px;
}

.card-product.style-9 {
    border: 1px solid var(--line-2);
    border-radius: 10px;
    padding: 5px 5px 20px;
}

.card-product.style-9 .card-product-info {
    padding-left: 5px;
    padding-right: 5px;
}

.card-product.style-9 .box-icon {
    border-radius: 50%;
}

.card-product.style-9 .card-product-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px;
}

.card-product.style-9 .card-product-info .inner-info {
    display: grid;
    gap: 12px;
}

.card-product.style-9 .card-product-info .box-icon {
    position: relative;
    z-index: 50;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    background-color: var(--primary);
    width: 32px;
    height: 32px;
    color: var(--white);
}

.card-product.style-9 .card-product-info .box-icon .tooltip {
    margin: 0;
    top: -70%;
}

.card-product.lg .card-product-info {
    margin: auto;
    max-width: 277px;
}

.card-product.lg .pr-stock {
    margin-top: 0px;
    margin-bottom: 20px;
    gap: 8px;
}

.card-product.list-layout {
    display: flex;
    gap: 15px;
}

.card-product.list-layout .card-product-wrapper {
    max-width: 338px;
    width: 35%;
}

.card-product.list-layout .card-product-info {
    flex: 1 1 auto;
    padding: 0 !important;
    display: flex;
    flex-direction: column;
    justify-content: start;
    align-items: self-start;
    max-width: 60%;
}

.card-product.list-layout .list-product-btn {
    margin-top: 10px;
}

.card-product.list-layout .list-color-product {
    margin-top: 10px;
}

.card-product.list-layout .size-list {
    background-color: transparent;
    height: auto;
}

.card-product.list-layout .size-list span {
    font-size: 14px;
    line-height: 14px;
    font-weight: 400;
    color: var(--main);
    gap: 10px;
}

.card-product.list-layout:not(:last-child) {
    padding-bottom: 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--line-2);
}

.card-product.style-price {
    background-color: var(--bg-2);
}

.card-product.style-price .card-product-info {
    padding: 26px 37px 0;
    gap: 10px;
}

.card-product.style-price .card-product-info .vendor {
    color: #545454;
    line-height: 17px;
}

.card-product.style-price .card-product-info .title {
    font-size: 20px;
}

.card-product.style-price .card-product-info .price {
    font-size: 20px;
    line-height: 20px;
    margin-top: 6px;
}

.card-product.style-price .card-product-info ul {
    margin-top: 6px;
}

.card-product.style-price .tf-price-table-contents {
    padding: 20px 37px 48px 37px;
}

.card-product.style-price .tf-price-table-contents ul {
    padding-top: 22px;
    border-top: 1px solid var(--line);
    display: flex;
    gap: 18px;
    flex-direction: column;
}

.card-product.style-price .tf-price-table-contents ul li {
    display: flex;
    gap: 10px;
    align-items: center;
    line-height: 26px;
    color: #545454;
}

.card-product.style-price .tf-price-table-contents ul li .icon {
    font-size: 12px;
    color: #303030;
}

.card-product.style-price .tf-price-table-contents .tf-price-table-btn {
    margin-top: 38px;
}

.card-product.style-price.bg_black {
    background-color: #141414;
}

.card-product.style-price.bg_black .tf-price-table-contents ul li,
.card-product.style-price.bg_black .tf-price-table-contents .icon,
.card-product.style-price.bg_black .vendor,
.card-product.style-price.bg_black .title,
.card-product.style-price.bg_black .price {
    color: var(--white);
}

.card-product.style-price.bg_black .tf-price-table-contents ul {
    border-top-color: #333333;
}

.card-product.style-price.bg_black .list-color-product .list-color-item.active,
.card-product.style-price.bg_black .list-color-product .list-color-item:hover {
    border-color: var(--white);
}

.card-product.style-price.bg_blue-6 .tf-price-table-contents ul li,
.card-product.style-price.bg_blue-6 .tf-price-table-contents .icon,
.card-product.style-price.bg_blue-6 .vendor,
.card-product.style-price.bg_blue-6 .title,
.card-product.style-price.bg_blue-6 .price {
    color: var(--white);
}

.card-product.style-price.bg_blue-6 .tf-price-table-btn a {
    background-color: var(--white);
    color: #1c355e;
}

.card-product.style-price.bg_blue-6 .tf-price-table-contents ul {
    border-top-color: #fff;
}

.card-product.style-price.bg_blue-6 .list-color-product .list-color-item.active,
.card-product.style-price.bg_blue-6 .list-color-product .list-color-item:hover {
    border-color: var(--white);
}

.card-product.style-price.bg_blue-6  .list-color-product  .list-color-item.active  .swatch-value,
.card-product.style-price.bg_blue-6  .list-color-product  .list-color-item:hover  .swatch-value {
    border-color: #1c355e;
}

.card-product.style-price.bg_blue-6 .list-product-btn .box-icon {
    color: #1c355e;
}

.card-product.style-price.bg_blue-6 .list-product-btn .box-icon:hover {
    background-color: #1c355e !important;
    color: var(--white);
}

.card-product.style-skincare {
    background-color: var(--white);
    border-radius: 10px;
}

.card-product.style-skincare .card-product-info {
    padding: 20px 0;
}

.card-product.style-skincare .tf-size-list {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.card-product.style-skincare .tf-size-list .tf-size-list-item {
    font-size: 12px;
    line-height: 30px;
    border: 1px solid var(--line);
    padding: 0px 10px;
}

.card-product.style-skincare .tf-size-list .tf-size-list-item:hover {
    border-color: var(--main);
}

.card-product.style-skincare .tf-product-btns {
    margin-top: 15px;
}

.card-product.style-giftcard {
    padding: 10px;
    border: 1px solid var(--line);
}

.card-product.style-giftcard .card-product-info {
    padding: 16px 13.5px 23.5px;
}

.card-product.style-giftcard .card-product-info .price span {
    font-size: 10px;
    line-height: 10px;
}

.card-product.style-giftcard .tf-product-btns a {
    border: 2px solid var(--primary);
    line-height: 40px;
}

.card-product.style-line-hover .card-product-wrapper {
    border-radius: 0;

    /* border: 2px solid var(--main) */
}

.card-product.style-line-hover .card-product-wrapper:hover .img-hover {
    -webkit-transform: unset;
    transform: unset;
}

.card-product.style-line-hover .card-product-wrapper img {
    transition: all 0.3s ease;
}

.card-product.type-line-padding {
    border: 1px solid var(--line);
    padding: 20px 10px;
}

.card-product.type-line-padding-2 {
    border: 1px solid var(--line);
    padding: 10px;
}

.card-product.type-line-padding-2 .card-product-info {
    padding-bottom: 10px;
}

.card-product.style-brown .list-product-btn .box-icon {
    color: #85715e;
}

.card-product.style-brown .list-product-btn .box-icon:hover {
    background-color: #85715e !important;
    color: var(--white);
}

.card-product.style-brown .list-color-item.active,
.card-product.style-brown .list-color-item:hover {
    border-color: var(--white);
}

.card-product.style-brown .list-color-item.active .swatch-value,
.card-product.style-brown .list-color-item:hover .swatch-value {
    border-color: #85715e;
}

.card-product .tf-countdown-v2 .countdown__item .countdown__label {
    text-transform: uppercase;
    color: var(--main);
}

.list-color-product {
    display: flex;
    gap: 8.5px;
    margin-top: 2px;
    flex-wrap: wrap;
}

.list-color-product .list-color-item {
    width: 26px;
    height: 26px;
    padding: 3px;
    border: solid 1px rgb(238, 238, 238);
    background-color: transparent;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.list-color-product .list-color-item .swatch-value {
    width: 18px;
    height: 18px;
    display: inline-block;
    border-radius: 50%;
    border: 3px solid transparent;
    -webkit-transition: all 0.4s ease;
    -moz-transition: all 0.4s ease;
    -ms-transition: all 0.4s ease;
    -o-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

.list-color-product .list-color-item .sold-out::after {
    content: "";
    width: 90%;
    height: 1px;
    background: rgb(34, 34, 34);
    display: block;
    position: absolute;
    z-index: 22;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(135deg);
}

.list-color-product .list-color-item img {
    visibility: hidden;
    width: 18px;
    height: 18px;
    position: absolute;
}

.list-color-product .list-color-item.active,
.list-color-product .list-color-item:hover {
    border-color: var(--main);
}

.list-color-product .list-color-item.active .swatch-value,
.list-color-product .list-color-item:hover .swatch-value {
    border-color: var(--white);
}

.list-color-product .list-color-item:hover .tooltip {
    opacity: 0.9;
    visibility: visible;
}

.bg-multiple-color {
    background-repeat: no-repeat;
    background-size: calc(100% + 10px) calc(100% + 10px) !important;
    transition: border 0.25s ease;
    transform: rotateZ(45deg);
    border: solid 1px transparent;
    background: none;
    background-position: center !important;
}

.green-black {
    background: conic-gradient(#000000 0deg 180deg, #83b735 180deg 360deg);
}

.yellow-black {
    background: conic-gradient(#000000 0deg 180deg, #d5e52e 180deg 360deg);
}

.blue-black {
    background: conic-gradient(#1555d4 0deg 180deg, #000000 180deg 360deg);
}

.blue-white {
    background: conic-gradient(#ffffff 0deg 180deg, #1555d4 180deg 360deg);
}

.red-black {
    background: conic-gradient(#f63400 0deg 180deg, #000000 180deg 360deg);
}

.white-striped {
    background: conic-gradient(#ffffff 0deg 180deg, #000000 180deg 360deg);
}

.tooltip {
    z-index: 202;
    opacity: 0;
    visibility: hidden;
    display: block;
    position: absolute;
    top: 100%;
    margin-top: 8px;
    border-radius: 2px;
    white-space: nowrap;
    background-color: var(--main);
    color: var(--white);
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    padding: 7px 10px 9px;
    max-width: 250px;
    width: max-content;
    transition: transform 0.4s ease 0.2s, opacity 0.4s ease 0.2s;
}

.tooltip::before {
    content: "";
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    top: -4px;
    position: absolute;
    background: var(--main);
    width: 8px;
    height: 8px;
    z-index: 100;
}

.hover-tooltip {
    position: relative;
}

.hover-tooltip:hover .tooltip {
    opacity: 0.9;
    visibility: visible;
}

.hover-tooltip.center .tooltip {
    left: 50%;
    transform: translateX(-50%);
}

.size-list {
    background-color: rgba(0, 0, 0, 0.3);
    gap: 10px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: 0.4s ease 0.1s;
}

.size-list span {
    font-size: 12px;
    font-weight: 600;
    line-height: 22px;
    color: rgb(242, 242, 242);
}

.size-list.style-2 {
    height: 33px;
    gap: 5px;
    background-color: var(--white);
}

.size-list.style-2 span {
    color: var(--main);
    min-width: 30px;
    height: 26px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgb(236, 236, 236);
}

.size-list.style-3 {
    background-color: rgba(255, 255, 255, 0.5);
}

.size-list.style-3 span {
    color: var(--main);
}

.list-product-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.list-product-btn .box-icon {
    width: 32px;
    height: 32px;
    min-width: 32px;
    border-radius: 3px;
    box-shadow: 0 8px 24px rgba(149, 157, 165, 0.2);
    background-color: rgb(242, 242, 242);
    position: relative;
}

.list-product-btn .box-icon .icon {
    font-size: 14px;
    font-weight: 500;
    height: 13px;
}

.list-product-btn .box-icon .text {
    font-size: 12px;
    font-weight: 700;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: auto;
    line-height: 32px;
}

.list-product-btn .box-icon .tooltip {
    top: -100%;
    margin-top: 5px;
}

.list-product-btn .box-icon .tooltip::before {
    top: unset;
    bottom: -4px;
}

.list-product-btn .box-icon.style-2 {
    gap: 8px;
    width: auto;
    padding: 0 10px;
    background-color: var(--white);
}

.list-product-btn .box-icon.style-3 {
    box-shadow: none;
    background-color: var(--white);
    border: 1px solid var(--line-2);
}

.list-product-btn .box-icon:hover {
    background-color: var(--main) !important;
    color: var(--white);
}

.list-product-btn .box-icon:hover .tooltip {
    opacity: 0.9;
    visibility: visible;
}

.list-product-btn.column-left {
    flex-direction: column;
    gap: 5px;
}

.list-product-btn.column-left .tooltip {
    left: 100%;
    margin-left: 5px;
    margin-top: 0;
    top: 50%;
    transform: translateY(-50%);
}

.list-product-btn.column-left .tooltip::before {
    top: 50%;
    transform: translateY(-50%) rotate(45deg);
    left: -4px;
    bottom: unset;
}

.list-product-btn.column-right {
    flex-direction: column;
    gap: 5px;
}

.list-product-btn.column-right .tooltip {
    right: 100%;
    margin-right: 5px;
    margin-top: 0;
    top: 50%;
    transform: translateY(-50%);
}

.list-product-btn.column-right .tooltip::before {
    top: 50%;
    left: unset;
    transform: translateY(-50%) rotate(45deg);
    right: -4px;
    bottom: unset;
}

.list-product-btn.type-wishlist {
    top: 15px;
    left: unset !important;
    bottom: unset !important;
}

.list-product-btn.type-wishlist .tooltip {
    top: unset;
    left: unset;
    right: 115%;
    bottom: unset;
    margin: 0 !important;
}

.list-product-btn.type-wishlist .tooltip::before {
    top: 50%;
    left: unset;
    right: -4px;
    bottom: unset;
    transform: translate(0, -50%) rotate(45deg);
}

.list-product-btn.style-black .box-icon {
    background-color: var(--main) !important;
    color: var(--white);
}

.list-product-btn.style-black .box-icon:hover {
    background-color: var(--white) !important;
    color: var(--main);
}

.list-product-btn.style-blue .box-icon {
    background-color: #1c355e !important;
    color: var(--white);
}

.list-product-btn.style-blue .box-icon:hover {
    background-color: var(--white) !important;
    color: #1c355e;
}

[data-grid="grid-6"] .card-product .countdown-box {
    padding: 5px;
}

[data-grid="grid-6"] .card-product .countdown-box .countdown__item {
    font-size: 12px;
    line-height: 19.2px;
}

.tf-sticky-btn-atc {
    position: fixed;
    width: 100%;
    bottom: 0;
    z-index: 70;
    box-shadow: 4px -4px 5px rgba(0, 0, 0, 0.03);
    background-color: var(--white);
    transition: all 0.3s linear;
}

.tf-sticky-btn-atc .tf-height-observer {
    padding: 10px 0;
    column-gap: 5px;
}

.tf-sticky-btn-atc .tf-sticky-atc-product {
    flex: 1 0 0%;
    gap: 15px;
}

.tf-sticky-btn-atc .tf-sticky-atc-img {
    width: 65px;
    height: 65px;
    flex-shrink: 0;
}

.tf-sticky-btn-atc .tf-sticky-atc-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.tf-sticky-btn-atc .tf-sticky-atc-title {
    font-size: 16px;
    line-height: 19px;
}

.tf-sticky-btn-atc .tf-sticky-atc-btns,
.tf-sticky-btn-atc .tf-sticky-atc-infos form {
    display: flex;
    gap: 12px;
}

.tf-sticky-btn-atc .tf-btn {
    min-width: 190px;
}

@keyframes showProduct {
    0% {
        transform: translateY(40px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.filter-price .tow-bar-block {
    position: relative;
    background: var(--line);
    height: 3px;
    border-radius: 3px;
}

.filter-price .tow-bar-block .progress-price {
    position: absolute;
    height: 3px;
    background: var(--primary);
    left: 0;
    right: 0%;
}

.filter-price .range-input {
    position: relative;
}

.filter-price .range-input input {
    position: absolute;
    top: -3px;
    height: 3px;
    width: 100%;
    background: none;
    outline: none;
    border: none;
    pointer-events: none;
    appearance: none;
}

.filter-price .range-input input::-webkit-slider-thumb {
    cursor: pointer;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    outline: none;
    pointer-events: auto;
    -webkit-appearance: none;
    background: var(--primary);
}

.filter-price .price-block {
    margin-top: 12px;
}

.filter-price .price-block span:first-child {
    color: #9fa09c;
}

.filter-price .price-block .min-price {
    margin-left: 4px;
}

.filter-price .price-block .gap {
    margin: 0 4px;
}

.meta-filter-shop {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.meta-filter-shop .tf-btn.style-3 {
    padding: 0 12px;
    line-height: 30px;
}

/* ------------ blog ---------------- */
.blog-article-item {
    margin-bottom: 37px;
}

.blog-article-item .article-thumb {
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.blog-article-item .article-thumb a {
    width: 100%;
    height: 100%;
}

.blog-article-item .article-thumb a img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 1s;
}

.blog-article-item .article-thumb a:hover img {
    transform: scale(1.1);
}

.blog-article-item .article-thumb.h-460 {
    height: 460px;
}

.blog-article-item .article-label {
    position: absolute;
    bottom: 18px;
    left: 18px;
    text-transform: uppercase;
    display: flex;
    gap: 10px;
}

.blog-article-item .article-label a {
    background-color: var(--white);
    color: var(--main);
    font-size: 10px;
    font-weight: 700;
    line-height: 12px;
    padding: 0 16px;
    height: 31px;
    border-color: var(--white);
}

.blog-article-item .article-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding-top: 30px;
}

.blog-article-item .article-title a {
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    color: var(--main);
}

.blog-article-item .article-title a:hover {
    color: var(--primary);
}

.blog-article-item.style-sidebar {
    display: flex;
    gap: 17px;
    margin-bottom: 0;
}

.blog-article-item.style-sidebar .article-thumb {
    width: 92px;
    flex-shrink: 0;
}

.blog-article-item.style-sidebar .article-thumb a {
    height: 88px;
}

.blog-article-item.style-sidebar .article-content {
    padding-top: 0;
}

.blog-article-item.style-sidebar .article-content .article-label {
    position: unset;
}

.blog-article-item.style-sidebar .article-content .article-label a {
    background-color: rgb(242, 242, 242);
}

.blog-article-item.style-sidebar .article-content .article-title a {
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
}

.blog-article-item.style-row {
    display: flex;
    align-items: center;
    gap: 69px;
    margin-bottom: 0;
}

.blog-article-item.style-row .article-thumb {
    width: 421px;
    height: 406px;
    flex-shrink: 0;
}

.blog-article-item.style-row .article-thumb a {
    height: 406px;
}

.blog-article-item.style-row .article-content {
    padding-top: 0;
}

.blog-article-item.style-row .article-content .article-label {
    position: unset;
}

.blog-article-item.style-row .article-content .article-label a {
    background-color: var(--white);
    border: 1px solid var(--line);
    border-radius: 0px !important;
}

.wg-pagination {
    display: flex;
    gap: 11px;
}

.wg-pagination .pagination-item {
    width: 45px;
    height: 39px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--line);
    border-radius: 3px;
    overflow: hidden;
}

.wg-pagination .active .pagination-item {
    background-color: var(--main);
    color: var(--white);
    box-shadow: rgba(0, 0, 0, 0.2) 1px 1px 10px 0px;
}

.tf-section-sidebar {
    display: flex;
    flex-direction: column;
    gap: 50px;
}

.tf-section-sidebar .sidebar-title {
    font-size: 20px;
    font-weight: 400;
    line-height: 30px;
    color: var(--main);
    padding-bottom: 25px;
    margin-bottom: 28px;
}

.tf-section-sidebar .sidebar-categories ul {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tf-section-sidebar .sidebar-categories ul li a {
    font-weight: 500;
    color: var(--main);
}

.tf-section-sidebar .sidebar-categories ul li a span {
    margin-left: 5px;
}

.tf-section-sidebar .sidebar-categories ul li a:hover {
    color: var(--primary);
}

.tf-section-sidebar .sidebar-post ul {
    display: flex;
    gap: 21px;
    flex-direction: column;
}

.tf-section-sidebar .sidebar-instagram ul {
    display: grid;
    gap: 6px;
    grid-template-columns: repeat(3, 1fr);
}

.tf-section-sidebar .sidebar-instagram ul li {
    border-radius: 5px;
    overflow: hidden;
}

.tf-section-sidebar .sidebar-instagram ul li img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 1s;
}

.tf-section-sidebar .sidebar-instagram ul li:hover img {
    transform: scale(1.1);
}

ul.tags-lists {
    display: flex;
    gap: 10px;
}

ul.tags-lists li a {
    border: 1px solid var(--line);
    padding: 0 14px;
    line-height: 27px;
}

ul.tags-lists li a:hover {
    color: var(--primary);
    border-color: var(--primary);
}

.blog-grid-main {
    padding: 36px 0 37px;
}

.blog-sidebar-main {
    padding: 64px 0 66px;
}

.blog-sidebar-main .list-blog .blog-article-item {
    margin-bottom: 54px;
}

.blog-sidebar-main .list-blog .blog-article-item .article-thumb {
    max-height: 625px;
}

.blog-list-main {
    padding: 64px 0 66px;
}

.blog-list-main .blog-article-item {
    margin-bottom: 37px;
}

.blog-list-main .wg-pagination {
    margin-top: 57px;
}

.blog-detail {
    padding: 30px 0 20px;
    margin-top: 60px;
    letter-spacing: 1px;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .tags-lists {
    margin-bottom: 20px;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .tags-lists a {
    font-size: 10px;
    font-weight: 700;
    line-height: 30px;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .title {
    margin-bottom: 15px;
    font-size: 40px;
    font-weight: 400;
    line-height: 50px;
    color: var(--main);
    text-align: center;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .meta {
    margin-bottom: 30px;
    text-align: center;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .meta span {
    font-weight: 600;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .image {
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 30px;
}

.blog-detail .blog-detail-main .blog-detail-main-heading .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.blog-detail .blog-detail-main .grid-image {
    display: flex;
    gap: 35px;
    margin-bottom: 48px;
}

.blog-detail .blog-detail-main .grid-image img {
    border-radius: 10px;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.blog-detail .blog-detail-main .desc {
    margin-bottom: 30px;
}

.blog-detail .blog-detail-main .bot {
    margin-bottom: 30px;
}

.blog-detail .blog-detail-main .bot .tags-lists a {
    border-radius: 0px;
    border-color: var(--main);
}

.blog-detail .blog-detail-main .bot p {
    font-weight: 600;
}

.blog-detail .blog-detail-main .bot .tf-social-icon {
    gap: 15px;
}

.blog-detail .blog-detail-main .tf-article-navigation {
    display: flex;
    padding: 27px 0 23px;
    margin-bottom: 80px;
    border-top: 1px solid var(--line);
    border-bottom: 1px solid var(--line);
}

.blog-detail .blog-detail-main .tf-article-navigation .item {
    padding-bottom: 25px;
    padding-left: 15px;
    align-items: center;
    gap: 10px;
}

.blog-detail .blog-detail-main .tf-article-navigation .item.prev {
    padding-right: 15px;
    padding-left: 0;
}

.blog-detail .blog-detail-main .tf-article-navigation .item.prev::after {
    position: absolute;
    content: "";
    height: 100%;
    width: 1px;
    background-color: var(--line);
    top: 0;
    right: 0;
}

.blog-detail .blog-detail-main .tf-article-navigation .item .inner > a {
    font-size: 10px;
    font-weight: 700;
    line-height: 16px;
}

.blog-detail .blog-detail-main .tf-article-navigation .item .icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    border: 1px solid var(--main);
    border-radius: 50%;
}

.blog-detail .blog-detail-main .tf-article-navigation .item .icon:hover {
    background-color: var(--main);
    border-color: var(--main);
    color: var(--white);
}

.blog-detail .blog-detail-main .tf-article-navigation .icon {
    width: 26px;
    height: 26px;
}

.blog-detail-main .tf-article-navigation h6 {
    font-size: 16px;
    line-height: 24px;
}

.blog-detail-main .tf-article-navigation h6 a {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

blockquote {
    position: relative;
    padding: 16px 30px 25px;
    margin-bottom: 45px;
    border-left: 2px solid var(--main);
}

blockquote .icon {
    margin-bottom: 20px;
}

blockquote .text {
    font-weight: 500;
}

.tf-page-title {
    padding: 69px 0 65px;

    /* background-image: url(../images/shop/file/page-title-blog.png) */
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

.tf-page-title .heading {
    font-size: 42px;
    font-weight: 400;
    line-height: 50px;
}

.tf-page-title .breadcrumbs {
    gap: 14px;
    margin-top: 5px;
}

.tf-page-title .breadcrumbs li {
    color: rgb(84, 84, 84);
}

.tf-page-title .breadcrumbs i {
    font-size: 10px;
}

.tf-page-title.style-1 {
    padding: 88px 0;
}

.tf-page-title.style-2 {
    padding: 80px 0 86px;
}

/* ------------ testimonial ---------------- */
.testimonial-item .icon {
    margin-bottom: 16px;
}

.testimonial-item .rating {
    margin-bottom: 16px;
}

.testimonial-item .text {
    font-size: 16px;
    line-height: 28.8px;
    margin-bottom: 11px;
}

.testimonial-item .divider {
    margin-bottom: 10px;
    max-width: 147px;
    margin-left: auto;
    margin-right: auto;
    height: 1px;
    border-top: 1px dashed var(--line);
}

.testimonial-item .name {
    font-weight: 600;
}

.testimonial-item .metas {
    color: var(--text);
}

.testimonial-item .metas span {
    font-weight: 600;
}

.testimonial-item .heading {
    font-size: 18px;
    font-weight: 600;
    line-height: 29px;
}

.testimonial-item .product {
    border-top: 1px solid var(--line);
    padding-top: 23px;
    position: relative;
    display: flex;
    align-items: center;
}

.testimonial-item .product .image {
    width: 100%;
    max-width: 64px;
}

.testimonial-item .product .content-wrap {
    padding-left: 24px;
    padding-right: 20px;
}

.testimonial-item .product .content-wrap .product-title {
    margin-bottom: 6px;
}

.testimonial-item .product .content-wrap .product-title a:hover {
    color: var(--primary);
}

.testimonial-item .product .content-wrap .price {
    font-weight: 600;
    color: var(--main);
}

.testimonial-item .product > a {
    position: absolute;
    right: 0;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid var(--main);
    opacity: 0;
    visibility: hidden;
    font-size: 9px;
}

.testimonial-item .product > a:hover {
    background-color: var(--main);
    color: var(--white);
}

.testimonial-item .product > a.text_blue-1 {
    border-color: #1c355e;
}

.testimonial-item .product > a.text_blue-1:hover {
    background-color: #1c355e;
}

.testimonial-item .product > a.text_blue-1:hover i {
    color: var(--white);
}

.testimonial-item:hover .product > a {
    opacity: 1;
    visibility: visible;
}

.testimonial-item.style-column {
    padding: 34px 20px 20px;
    border: 1px solid var(--line);
    border-radius: 10px;
}

.testimonial-item.style-column .rating {
    margin-bottom: 14px;
}

.testimonial-item.style-column .heading {
    margin-bottom: 8px;
}

.testimonial-item.style-column .text {
    margin-bottom: 20px;
}

.testimonial-item.style-column .author {
    margin-bottom: 30px;
}

.testimonial-item.style-column.style-2 {
    background-color: var(--white);
    border: 0;
    border-radius: 0;
    padding: 20px;
}

.testimonial-item.style-column.style-2 .text {
    font-size: 16px;
    line-height: 25.6px;
    margin-bottom: 10px;
}

.testimonial-item.style-column.style-2 .author {
    margin-bottom: 0px;
}

.testimonial-item.style-column.style-2 .author svg {
    color: var(--bg-5);
}

.testimonial-item.style-row {
    display: flex;
    gap: 34px;
}

.testimonial-item.style-row .image {
    width: 244px;
    flex-shrink: 0;
}

.testimonial-item.style-row .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-item.style-row .product {
    border: none;
}

.testimonial-item.style-row .product .img-thumb {
    max-width: 64px;
}

.testimonial-item.style-row .rating {
    margin-bottom: 10px;
}

.testimonial-item.style-row .text {
    font-size: 16px;
    line-height: 25.6px;
}

.testimonial-item.style-box {
    padding: 28px 24px 40px 34px;
    background-color: var(--white);
    border-radius: 10px;
    min-height: 300px;
}

.testimonial-item.style-box .author {
    display: flex;
    flex-wrap: wrap;
    gap: 14px;
    align-items: center;
    margin-bottom: 19px;
}

.testimonial-item.style-box .author .name {
    font-weight: 700;
}

.testimonial-item.style-box .author .metas {
    padding: 6px 16px;
    display: flex;
    align-items: center;
    gap: 9px;
    background-color: var(--main);
    color: var(--white);
    font-size: 12px;
    line-height: 19px;
}

.testimonial-item.style-box .author .metas i {
    font-size: 10px;
}

.testimonial-item.style-box .rating {
    margin-bottom: 19px;
}

.testimonial-item.style-box .text {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.testimonial-item.style-box-1 {
    padding: 37px 33px 34px;
    border-radius: 10px;
    background-color: var(--white);
    min-height: 444px;
    display: flex;
    flex-direction: column;
}

.testimonial-item.style-box-1 .rating {
    margin-bottom: 18px;
}

.testimonial-item.style-box-1 .rating i {
    font-size: 13px;
    color: #00b67a;
}

.testimonial-item.style-box-1 .text {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: var(--main);
    flex-grow: 1;
}

.testimonial-item.style-box-1 .metas {
    display: flex;
    align-items: center;
    gap: 5px;
}

.testimonial-item.style-box-1 .metas i {
    font-size: 16px;
    color: #00b67a;
}

.testimonial-item.lg .icon {
    font-size: 28px;
}

.testimonial-item.lg .text {
    font-size: 16px;
    line-height: 25.6px;
    margin-bottom: 16px;
}

.testimonial-item.lg .divider {
    margin-left: 0;
}

.testimonial-item .box-author {
    display: flex;
    gap: 16px;
}

.testimonial-item .box-author .box-img {
    width: 70px;
    height: 74px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
}

.testimonial-item.lg-2 .box-author {
    margin-bottom: 16px;
}

.rating {
    display: flex;
    align-items: center;
    gap: 7px;
}

.rating i {
    font-size: 16px;
    color: var(--bg-5);
}

.rating.color-black i {
    color: var(--main);
}

.flat-wrap-tes-text {
    margin-bottom: 25px;
}

.flat-wrap-tes-text .testimonial-ver-text {
    padding: 0 50px;
}

.testimonial-ver-text {
    display: flex;
    flex-direction: column;
    text-align: center;
}

.testimonial-ver-text .heading {
    letter-spacing: 0.8px;
    font-size: 16px;
    line-height: 25.6px;
    font-weight: 700;
    color: var(--main);
    margin-bottom: 20px;
}

.testimonial-ver-text .content {
    font-size: 16px;
    line-height: 25.6px;
}

.thumb-tes-brands .swiper-slide {
    width: auto !important;
}

.thumb-tes-brands .swiper-wrapper {
    justify-content: center;
}

.thumb-tes-brands .thumb-tes-item {
    padding-left: 10px;
    padding-right: 10px;
    opacity: 0.3;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.thumb-tes-brands .thumb-tes-item img {
    max-width: 100px;
    cursor: pointer;
}

.thumb-tes-brands .thumb-tes-item:hover {
    opacity: 1;
}

.thumb-tes-brands .swiper-slide-thumb-active .thumb-tes-item {
    opacity: 1;
}

/* ------------ lookbook ---------------- */
.tf-lookbook {
    display: flex;
}

.wrap-lookbook {
    width: 100%;
    position: relative;
}

.wrap-lookbook .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wrap-lookbook.lookbook-sw .navigation-sw-dot {
    position: absolute;
}

.wrap-lookbook.lookbook-sw .nav-next-lookbook {
    top: 5%;
    left: 19%;
}

.wrap-lookbook.lookbook-sw .nav-prev-lookbook {
    top: 52%;
    left: 30%;
}

.lookbook-item {
    position: absolute;
    width: 26px;
    height: 26px;
}

.lookbook-item .tf-pin-btn {
    width: 26px;
    height: 26px;
    background-color: rgb(242, 242, 242);
    border-radius: 50%;
    border: 0;
    outline: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lookbook-item .tf-pin-btn span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--main);
}

.lookbook-item .tf-pin-btn.pin-white {
    background-color: var(--white);
}

.lookbook-item .tf-pin-btn.pin-black {
    background-color: var(--main);
}

.lookbook-item .tf-pin-btn.pin-black span {
    background-color: var(--white);
}

.lookbook-item .dropdown-menu {
    margin: 20px 0 !important;
    top: unset !important;
    bottom: 23px !important;
    transform: translateX(-46%) !important;
}

.lookbook-product {
    padding: 10px 14px 12px 11px;
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: space-between;
    width: 334px;
    background-color: var(--white);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1019607843);
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -ms-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

.lookbook-product .image {
    width: 62px;
    height: 70px;
    flex-shrink: 0;
}

.lookbook-product .image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.lookbook-product .content-wrap {
    flex-grow: 1;
}

.lookbook-product .content-wrap .product-title {
    margin-bottom: 6px;
}

.lookbook-product .content-wrap .product-title a {
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
}

.lookbook-product .content-wrap .product-title a:hover {
    color: var(--primary);
}

.lookbook-product .content-wrap .price {
    font-weight: 600;
    color: var(--main);
}

.lookbook-product > a {
    width: 38px;
    height: 38px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--line);
    color: var(--main);
    font-size: 14px;
}

.lookbook-product > a:hover {
    background-color: var(--main);
    color: var(--white);
}

.lookbook-product::after {
    position: absolute;
    content: "";
    width: 16px;
    height: 16px;
    transform: translate(-50%, 50%) rotate(45deg);
    background-color: var(--white);
    bottom: 0;
    left: 50%;
    z-index: 2;
}

.tf-pin_btn:hover:after,
.tf-pin_btn:hover:before {
    animation: ripple 3s infinite;
}

.lookbook-1 .item-1 {
    top: 85%;
    left: 62%;
}

.lookbook-1 .item-2 {
    top: 63%;
    left: 45%;
}

.lookbook-2 .item-1 {
    top: 20%;
    left: 62%;
}

.tf-sw-lookbook {
    padding-top: 70px;
    margin-top: -70px;
}

.tf-sw-lookbook .wrap-pagination {
    position: absolute;
    bottom: 10px;
    z-index: 10;
    left: 15px;
    right: 15px;
}

.tf-sw-lookbook .wrap-pagination .sw-dots {
    margin: 0;
}

.lookbook-kid {
    position: relative;
}

.lookbook-kid .lookbook-item {
    position: absolute;
}

.lookbook-kid .lookbook-item .tf-pin-btn {
    background-color: var(--white);
}

.lookbook-kid .item-1 {
    left: 24%;
    top: 32%;
}

.lookbook-kid .item-2 {
    left: 24%;
    top: 78%;
}

.lookbook-kid .item-3 {
    left: 50%;
    top: 70%;
}

.lookbook-dog {
    position: relative;
}

.lookbook-dog .lookbook-item {
    position: absolute;
}

.lookbook-dog .lookbook-item .tf-pin-btn {
    background-color: var(--white);
}

.lookbook-dog .item-1 {
    left: 37%;
    top: 32%;
}

.lookbook-dog .item-2 {
    left: 53%;
    top: 70%;
}

.lookbook-dog .item-3 {
    left: 64%;
    top: 25%;
}

.slideshow-lookbook-furniture .lookbook-1 .item-1 {
    left: 40%;
    top: 40%;
}

.slideshow-lookbook-furniture .lookbook-1 .item-2 {
    left: 50%;
    top: 50%;
}

.slideshow-lookbook-furniture .lookbook-2 .item-1 {
    left: 50%;
    top: 50%;
}

.slideshow-lookbook-furniture .lookbook-3 .item-1 {
    top: 45%;
    left: 45%;
}

.lookbook-kitchen-wear form .tf-lookbook {
    height: unset !important;
}

.lookbook-kitchen-wear form .swiper-slide:last-child .tf-bundle-product-item {
    border: 0;
}

.lookbook-kitchen-wear form .tf-btn {
    width: 100%;
    max-width: 461px;
}

.lookbook-kitchen-wear .wrap-lookbook .item-1 {
    top: 65%;
    left: 50%;
}

.lookbook-kitchen-wear .wrap-lookbook .item-2 {
    top: 17%;
    left: 57%;
}

.lookbook-kitchen-wear .wrap-lookbook .item-3 {
    top: 70%;
    left: 85%;
}

.flat-activewear-lookbook .wrap-lookbook .item-1 {
    top: 20%;
    left: 65%;
}

.flat-activewear-lookbook .wrap-lookbook .item-2 {
    top: 60%;
    left: 16%;
}

.flat-activewear-lookbook .wrap-lookbook .item-3 {
    top: 61%;
    left: 80%;
}

.flat-activewear-lookbook .inner-sw-lookbook .tf-btn {
    margin-top: 16px;
    border-radius: 60px;
}

.lookbook-handbag .lookbook-item {
    position: absolute;
}

.lookbook-handbag .item-1 {
    left: 49%;
    top: 28%;
}

.lookbook-handbag .item-2 {
    left: 30%;
    top: 45%;
}

.lookbook-handbag .item-3 {
    left: 50%;
    top: 68%;
}

.lookbook-sneaker .lookbook-item {
    position: absolute;
}

.lookbook-sneaker .item-1 {
    left: 64%;
    top: 21%;
}

.lookbook-sneaker .item-2 {
    left: 38%;
    top: 45%;
}

/* ------------ featured ---------------- */
.tf-featured {
    position: relative;
}

.tf-featured .content .sub-title {
    font-size: 14px;
    font-weight: 700;
    line-height: 17px;
    color: var(--main);
    margin-bottom: 15px;
}

.tf-featured .content h3 {
    margin-bottom: 24px;
}

.tf-featured .content p {
    margin-bottom: 40px;
}

.tf-featured.full-width {
    padding-top: 265px;
    padding-bottom: 290px;
}

.tf-featured.style-1 {
    display: flex;
}

.tf-featured.style-1 .image {
    width: 50%;
}

.tf-featured.style-1 .content {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #f9f3ea;
}

.tf-featured.style-1 .content a {
    width: max-content;
    margin-left: auto;
    margin-right: auto;
}

.tf-hero-image-liquid {
    position: relative;
}

.tf-hero-image-liquid img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-hero-image-liquid .box-content {
    position: absolute;
    left: 0;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.tf-hero-image-liquid .box-content .heading {
    font-size: 52px;
    line-height: 62.4px;
}

.tf-hero-image-liquid .box-content p {
    margin-top: 16px;
    margin-bottom: 20px;
    font-size: 20px;
    line-height: 32px;
}

.tf-hero-image-liquid .box-content .tf-btn {
    margin-top: 20px;
}

.banner-countdown-v2 .box-content {
    left: 3%;
    right: 3%;
}

.tf-countdown .countdown__timer {
    display: flex;
    gap: 16px;
}

.tf-countdown .countdown__timer .countdown__item {
    width: 90px;
    height: 90px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.tf-countdown .countdown__timer .countdown__item .countdown__value {
    font-size: 32px;
    font-weight: 500;
    line-height: 38.4px;
    color: var(--main);
}

.tf-countdown .countdown__timer .countdown__item .countdown__label {
    font-size: 12px;
    font-weight: 700;
    line-height: 14.4px;
    letter-spacing: 0.5px;
    color: var(--main);
    text-transform: uppercase;
}

.tf-countdown.style-rectangle .countdown__item {
    width: 61px;
    height: 72px;
    border-radius: 3px;
}

.tf-countdown.style-1 .countdown__timer {
    gap: 5px;
    justify-content: center;
}

.tf-countdown.style-1 .countdown__timer .countdown__item {
    width: unset;
    height: unset;
    display: block;
    background-color: unset;
}

.tf-countdown.style-1 .countdown__timer .countdown__item .countdown__value,
.tf-countdown.style-1 .countdown__timer .countdown__item .countdown__label {
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: #db1215;
    text-transform: capitalize;
}

.tf-countdown-v2 .countdown__timer {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tf-countdown-v2 .countdown__item {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-width: 60px;
    min-height: 60px;
    padding: 6px 10px;
    border: 1px solid var(--main);
    background-color: transparent;
    border-radius: 3px;
}

.tf-countdown-v2 .countdown__value {
    font-size: 16px;
    line-height: 19.2px;
    font-weight: 500;
}

.tf-countdown-v2 .countdown__label {
    font-size: 12px;
    font-weight: 500;
    line-height: 25.6px;
}

.tf-countdown-v3 {
    display: inline-flex;
    align-items: center;
    color: #ff0b0b;
    gap: 13px;
    background-color: rgba(255, 11, 11, 0.1);
    border-radius: 22px;
    padding: 4px 10px;
}

.tf-countdown-v3 .countdown__timer {
    display: inline-flex;
}

.tf-countdown-v3 .countdown__item {
    font-size: 18px;
    line-height: 28.8px;
    font-weight: 600;
}

.tf-countdown-v3 .countdown__item:not(:last-child)::after {
    content: ":";
    margin-left: 10px;
    margin-right: 10px;
}

/* ------------ accordion ---------------- */
.flat-accordion2 .flat-toggle2 .toggle-title,
.flat-accordion2 .flat-toggle1 .toggle-title,
.flat-accordion2 .flat-toggle .toggle-title,
.flat-accordion1 .flat-toggle2 .toggle-title,
.flat-accordion1 .flat-toggle1 .toggle-title,
.flat-accordion1 .flat-toggle .toggle-title,
.flat-accordion .flat-toggle2 .toggle-title,
.flat-accordion .flat-toggle1 .toggle-title,
.flat-accordion .flat-toggle .toggle-title {
    background-color: #f5f5f5;
    font-size: 18px;
    font-weight: 600;
    line-height: 30px;
    color: var(--main);
    padding: 14px 20px;
    cursor: pointer;
    position: relative;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.flat-accordion2 .flat-toggle2 .toggle-title:hover,
.flat-accordion2 .flat-toggle1 .toggle-title:hover,
.flat-accordion2 .flat-toggle .toggle-title:hover,
.flat-accordion1 .flat-toggle2 .toggle-title:hover,
.flat-accordion1 .flat-toggle1 .toggle-title:hover,
.flat-accordion1 .flat-toggle .toggle-title:hover,
.flat-accordion .flat-toggle2 .toggle-title:hover,
.flat-accordion .flat-toggle1 .toggle-title:hover,
.flat-accordion .flat-toggle .toggle-title:hover {
    color: var(--primary);
}

.flat-accordion2 .flat-toggle2 .toggle-content,
.flat-accordion2 .flat-toggle1 .toggle-content,
.flat-accordion2 .flat-toggle .toggle-content,
.flat-accordion1 .flat-toggle2 .toggle-content,
.flat-accordion1 .flat-toggle1 .toggle-content,
.flat-accordion1 .flat-toggle .toggle-content,
.flat-accordion .flat-toggle2 .toggle-content,
.flat-accordion .flat-toggle1 .toggle-content,
.flat-accordion .flat-toggle .toggle-content {
    display: none;
    padding: 35px;
    border: 1px solid var(--line);
}

.flat-accordion2.has-btns .flat-toggle .toggle-title::after,
.flat-accordion1.has-btns .flat-toggle .toggle-title::after,
.flat-accordion.has-btns .flat-toggle .toggle-title::after {
    position: absolute;
    content: "";
    width: 12px;
    height: 2px;
    background-color: #545454;
    right: 0px;
    border-radius: 2px;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.flat-accordion2.has-btns .flat-toggle .toggle-title::before,
.flat-accordion1.has-btns .flat-toggle .toggle-title::before,
.flat-accordion.has-btns .flat-toggle .toggle-title::before {
    position: absolute;
    content: "";
    width: 2px;
    height: 12px;
    background-color: #545454;
    right: 5px;
    border-radius: 2px;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    opacity: 1;
}

.flat-accordion2.has-btns .flat-toggle .toggle-title.active::before,
.flat-accordion1.has-btns .flat-toggle .toggle-title.active::before,
.flat-accordion.has-btns .flat-toggle .toggle-title.active::before {
    transform: translateY(-50%) rotate(45deg);
    opacity: 0;
}

.flat-accordion2.has-btns .flat-toggle.style-2:not(:last-child),
.flat-accordion1.has-btns .flat-toggle.style-2:not(:last-child),
.flat-accordion.has-btns .flat-toggle.style-2:not(:last-child) {
    margin-bottom: 15px;
}

.flat-accordion2.has-btns .flat-toggle.style-2 .toggle-title::after,
.flat-accordion1.has-btns .flat-toggle.style-2 .toggle-title::after,
.flat-accordion.has-btns .flat-toggle.style-2 .toggle-title::after {
    right: 15px;
}

.flat-accordion2.has-btns .flat-toggle.style-2 .toggle-title::before,
.flat-accordion1.has-btns .flat-toggle.style-2 .toggle-title::before,
.flat-accordion.has-btns .flat-toggle.style-2 .toggle-title::before {
    right: 20px;
}

.flat-accordion2.has-btns-arrow .flat-toggle .toggle-title::after,
.flat-accordion1.has-btns-arrow .flat-toggle .toggle-title::after,
.flat-accordion.has-btns-arrow .flat-toggle .toggle-title::after {
    display: none;
}

.flat-accordion2.has-btns-arrow .flat-toggle .toggle-title::before,
.flat-accordion1.has-btns-arrow .flat-toggle .toggle-title::before,
.flat-accordion.has-btns-arrow .flat-toggle .toggle-title::before {
    position: absolute;
    content: "\e904";
    font-family: "icomoon";
    font-size: 8px;
    color: var(--main);
    right: 0;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.flat-accordion2.has-btns-arrow .flat-toggle .toggle-title.active::before,
.flat-accordion1.has-btns-arrow .flat-toggle .toggle-title.active::before,
.flat-accordion.has-btns-arrow .flat-toggle .toggle-title.active::before {
    transform: rotate(180deg);
}

.flat-accordion2.style-default .toggle-title,
.flat-accordion1.style-default .toggle-title,
.flat-accordion.style-default .toggle-title {
    background-color: transparent;
    font-size: 16px;
    font-weight: 500;
    line-height: 19.2px;
    color: var(--main);
    padding: 15px 0px;
    padding-right: 15px;
    border-bottom: 1px solid var(--line);
}

.flat-accordion2.style-default .toggle-content,
.flat-accordion1.style-default .toggle-content,
.flat-accordion.style-default .toggle-content {
    padding: 24px 0 22px;
    border: 0;
}

/* ------------ zoom ---------------- */
.tf-zoom-main {
    position: sticky;
    top: 30px;
    z-index: 50;
}

.tf-zoom-main .drift-zoom-pane {
    top: 0;
    left: 0;
    height: 520px;
    max-width: 520px;
    width: 100%;
    background: #fff;
    -webkit-transform: translate3d(0, 0, 0);
    box-shadow: 0 1px 5px rgba(127, 127, 127, 0.0196078431),
    0 5px 18px rgba(127, 127, 127, 0.2);
    z-index: 3;
}

.drift-bounding-box.drift-open {
    background: rgba(255, 255, 255, 0.2509803922);
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.4) inset;
    z-index: 5000;
}

.drift-zoom-pane {
    z-index: 5000;
}

.section-image-zoom .other-image-zoom {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.section-image-zoom.zoom-active .other-image-zoom {
    opacity: 0.3;
}

.pswp__bg {
    background: var(--white);
}

.pswp__icn {
    fill: rgb(34, 34, 34);
    color: var(--white);
}

.pswp__icn .pswp__icn-shadow {
    stroke: var(--white);
    stroke-width: 1px;
    fill: none;
}

.pswp__counter {
    color: #222;
    text-shadow: 1px 1px 3px #ffffff;
}

.tf-model-viewer {
    width: 100%;
    height: 100%;
    position: relative;
}

.tf-model-viewer model-viewer {
    display: block;
    position: relative;
    z-index: 5;
    width: 100%;
    height: 100%;
}

.tf-model-viewer model-viewer.disabled {
    pointer-events: none;
}

.tf-model-viewer.active model-viewer {
    pointer-events: all;
}

.tf-model-viewer.active .wrap-btn-viewer {
    display: none;
}

/* ------------ sections ---------------- */
.tf-shop-control {
    margin-bottom: 15px;
    gap: 5px;
}

.tf-control-layout {
    display: flex;
    gap: 12px;
}

.tf-control-layout .tf-view-layout-switch .item {
    color: var(--main);
    opacity: 0.4;
    padding: 0 5px;
    min-height: 34px;
    align-items: center;
    justify-content: center;
    display: flex;
    cursor: pointer;
}

.tf-control-layout .tf-view-layout-switch.active .item {
    opacity: 1;
}

.tf-pagination-wrap {
    padding-top: 45px;
}

.tf-pagination-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 11px;
}

.tf-pagination-list .pagination-link {
    width: 45px;
    height: 39px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    color: var(--main);
    border: 1px solid;
    border: 1px solid rgb(235, 235, 235);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.tf-pagination-list .pagination-link .icon {
    font-size: 12px;
}

.tf-pagination-list .active .pagination-link {
    box-shadow: rgba(0, 0, 0, 0.2) 1px 1px 10px 0px;
    background-color: var(--main);
    color: var(--white);
    border-color: var(--main);
}

.widget-facet {
    margin-bottom: 26px;
}

.widget-facet .facet-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 25px;
    font-size: 16px;
    line-height: 24px;
    cursor: pointer;
}

.widget-facet .facet-title .icon {
    transition: transform 0.35s linear;
    font-size: 8px;
}

.widget-facet .facet-title.collapsed .icon {
    transform: rotate(180deg);
}

.widget-facet.wd-categories {
    border-bottom: 1px solid var(--line);
}

.widget-facet:not(:last-child) {
    border-bottom: 1px solid var(--line);
}

.widget-facet .cate-item a {
    display: flex;
    color: var(--main);
    font-size: 14px;
    line-height: 22.4px;
    font-weight: 500;
}

.widget-facet .cate-item.current a {
    color: var(--primary);
}

.widget-facet .cate-item:not(:first-child) {
    margin-top: 10px;
}

.widget-facet .list-item .label {
    display: flex;
    color: var(--main);
    font-size: 14px;
    line-height: 22.4px;
    font-weight: 500;
}

.widget-facet .list-item:not(:first-child) {
    margin-top: 10px;
}

.widget-facet .current-scrollbar {
    max-height: 200px;
    overflow-y: auto;
    margin-right: 5px;
}

.widget-facet .current-scrollbar::-webkit-scrollbar {
    width: 2px;
}

.widget-facet .current-scrollbar::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.15);
}

.widget-facet .current-scrollbar::-webkit-scrollbar-thumb {
    background: var(--main);
}

.widget-facet .filter-color {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.widget-facet .filter-color .list-item {
    margin: 0;
}

.widget-featured-products {
    display: grid;
    gap: 15px;
}

.widget-featured-products .featured-product-item {
    display: flex;
    gap: 15px;
}

.widget-featured-products .featured-product-item .card-product-wrapper {
    width: 92px;
    min-width: 92px;
    border-radius: 10px;
    overflow: hidden;
}

.widget-featured-products .featured-product-item .card-product-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.widget-featured-products .featured-product-item .card-product-info .title {
    font-size: 16px;
    line-height: 19.2px;
    margin-bottom: 1px;
}

.widget-featured-products .featured-product-item .card-product-info .price {
    font-size: 14px;
    font-weight: 600;
    line-height: 14px;
}

.widget-iconbox-list {
    display: grid;
    gap: 18px;
}

.widget-iconbox-list .iconbox-item {
    display: flex;
    align-items: center;
    gap: 18px;
}

.widget-iconbox-list .iconbox-item .box-icon {
    border: 1px solid var(--line);
}

.widget-iconbox-list .iconbox-item .iconbox-content {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.widget-iconbox-list .iconbox-item .iconbox-content .iconbox-title {
    font-size: 16px;
    line-height: 19.2px;
    font-weight: 600;
}

.widget-iconbox-list .iconbox-item .iconbox-content .iconbox-desc {
    color: var(--text-3);
}

.widget-price {
    margin-top: 24px;
    margin-bottom: 35px;
}

.widget-price .box-title-price {
    margin-top: 30px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-price .caption-price {
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-price .caption-price div {
    width: 68px;
    display: flex;
    justify-content: space-between;
    padding: 8px 10px;
    border: 1px solid var(--line);
    border-radius: 5px;
}

.btn-sidebar-mobile {
    display: none;
    position: fixed;
    top: 30%;
    right: 0;
    z-index: 100;
    width: max-content;
}

.btn-sidebar-mobile button {
    width: 40px;
    height: 48px;
    display: flex;
    border: 0;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 6px 0px;
}

.btn-sidebar-mobile button i {
    font-size: 17px;
    color: var(--main);
}

.btn-sidebar-mobile button.type-hover {
    padding: 0 11.5px;
}

.btn-sidebar-mobile button.type-hover span {
    display: none;
}

.btn-sidebar-mobile button.type-hover:hover {
    width: max-content;
    padding-right: 24px;
    gap: 11.5px;
}

.btn-sidebar-mobile button.type-hover:hover span {
    display: block;
}

.btn-sidebar-mobile.left {
    right: auto;
    left: 0;
}

.btn-sidebar-style2 {
    display: none;
    position: fixed;
    top: 30%;
    left: 0;
    z-index: 100;
}

.btn-sidebar-style2 button {
    width: 38px;
    height: 38px;
    display: flex;
    border: 1px solid var(--line-2);
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    justify-content: center;
    align-items: center;
    background-color: var(--white);
}

.btn-sidebar-style2 button .icon {
    font-size: 17px;
    color: var(--main);
}

.btn-sidebar-style2.right {
    right: 0;
    left: auto;
}

.loadmore-item .fl-item {
    display: none;
}

.loadmore-item2 .fl-item2 {
    display: none;
}

.loadmore-item3 .fl-item3 {
    display: none;
}

.tf-marquee {
    padding-top: 24px;
    padding-bottom: 24px;
    overflow: hidden;
}

.tf-marquee .wrap-marquee {
    display: flex;
    -webkit-animation: slide-har 6s linear infinite;
    animation: slide-har 6s linear infinite;
    transition: animation-duration 300ms;
}

.tf-marquee .wrap-marquee:hover {
    animation-play-state: paused;
}

.tf-marquee .marquee-item {
    padding-left: 18px;
    padding-right: 18px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.tf-marquee .marquee-item .text {
    font-size: 18px;
    line-height: 21.6px;
    white-space: nowrap;
}

.tf-marquee .marquee-item p {
    white-space: nowrap;
}

.tf-marquee.marquee-md {
    padding-top: 30px;
    padding-bottom: 32px;
}

.tf-marquee.marquee-md p {
    font-size: 32px;
    line-height: 38.4px;
}

.tf-marquee.marquee-xl {
    padding-top: 75px;
    padding-bottom: 75px;
}

.tf-marquee.marquee-xl svg {
    width: 29px;
    height: auto;
    fill: var(--main);
}

.tf-marquee.marquee-xl .marquee-item {
    gap: 60px;
    padding: 0 29px;
}

.tf-marquee.marquee-xl .marquee-item .text {
    font-size: 40px;
    line-height: 48px;
}

.tf-marquee.marquee-sm {
    padding-top: 15px;
    padding-bottom: 15px;
}

.tf-marquee.marquee-sm .marquee-item {
    gap: 22.5px;
    padding-left: 12px;
    padding-right: 12px;
}

.tf-marquee.marquee-sm .marquee-item .text {
    font-size: 12px;
    line-height: 19.2px;
}

.tf-marquee.type-big .marquee-item {
    gap: 65px;
    padding: 0 32.5px;
}

.tf-marquee.type-big .marquee-item p {
    font-size: 80px;
    line-height: 96px;
}

.tf-marquee.not-hover .wrap-marquee:hover {
    animation-play-state: running;
}

.tf-marquee.type-brown .marquee-item .text {
    color: #704228;
}

.tf-marquee.type-brown .marquee-item .icon svg {
    fill: #704228;
}

.tf-marquee.type-md .marquee-item {
    gap: 32px;
}

.tf-marquee.type-md .marquee-item .icon svg {
    width: 20px;
    height: auto;
}

.tf-marquee.type-md .marquee-item .text {
    font-size: 16px;
    line-height: 19.2px;
}

.flat-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 18px;
    margin-bottom: 30px;
    padding-left: 15px;
    padding-right: 15px;
}

.flat-title .title {
    font-size: 22px;
    line-height: 26.4px;
}

.flat-title .sub-title {
    font-size: 14px;
    line-height: 20px;
    text-align: center;
}

.flat-title.mb_1 {
    margin-bottom: 30px;
}

.flat-title .sw-dots {
    margin-top: 0;
}

.flat-title.title-upper .title {
    font-size: 18px;
    line-height: 21.6px;
    font-weight: 800;
    text-transform: uppercase;
}

.flat-title-v2 {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 33px;
}

.flat-bg-collection .collection-other-link {
    margin-top: 30px;
    color: var(--white);
}

.flat-bg-collection .collection-other-link::after {
    background-color: var(--white);
}

.flat-bg-collection .collection-other-link:hover {
    color: var(--primary);
}

.flat-bg-collection .collection-other-link:hover::after {
    background-color: var(--primary);
}

.tf-sw-brand {
    border-radius: 0px;
    overflow: hidden;
    border: 1px solid var(--line);
}

.tf-sw-brand .swiper-slide:not(:last-child) .brand-item {
    border-right: 1px solid var(--line);
}

.brand-item {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.brand-item-v2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.hover-img-brand img {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    opacity: 0.3;
}

.hover-img-brand:hover img {
    opacity: 1;
}

.row-brand {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.row-brand .brand-item-v2 {
    width: max-content;
    max-width: 215px;
}

.gallery-item {
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.gallery-item::before {
    position: absolute;
    z-index: 2;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    top: 0;
    left: 0;
    transition: 0.4s ease 0.1s;
    opacity: 0;
    visibility: hidden;
}

.gallery-item .box-icon {
    position: absolute;
    z-index: 5;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    border-radius: 50%;
    background-color: var(--white);
    width: 33px;
    height: 33px;
    font-size: 12px;
}

.gallery-item .box-icon .tooltip {
    top: -100%;
    margin-top: -5px;
}

.gallery-item .box-icon .tooltip::before {
    top: unset;
    bottom: -4px;
}

.gallery-item .box-icon:hover {
    background-color: var(--main);
    color: var(--white);
}

.gallery-item .box-icon:hover .tooltip {
    opacity: 0.9;
    visibility: visible;
}

.masonry-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.masonry-layout .collection-inner {
    width: 100%;
    height: 100%;
}

.masonry-layout .collection-item .img-style {
    height: 220px;
}

.masonry-layout.style-2 {
    grid-template-areas: "item1 item1" "item2 item3" "item4 item4";
}

.masonry-layout.style-2 .collection-image {
    height: 250px;
}

.masonry-layout.style-2 .item-1 {
    grid-area: item1;
}

.masonry-layout.style-2 .item-2 {
    grid-area: item2;
}

.masonry-layout.style-2 .item-3 {
    grid-area: item3;
}

.masonry-layout.style-2 .item-4 {
    grid-area: item4;
}

.masonry-layout-v2 {
    display: grid;
    gap: 30px;
    grid-template-areas: "item1 item1 item2 item3" "item4 item5 item6 item6";
}

.masonry-layout-v2 .item-1 {
    grid-area: item1;
}

.masonry-layout-v2 .item-2 {
    grid-area: item2;
}

.masonry-layout-v2 .item-3 {
    grid-area: item3;
}

.masonry-layout-v2 .item-4 {
    grid-area: item4;
}

.masonry-layout-v2 .item-5 {
    grid-area: item5;
}

.masonry-layout-v2 .item-6 {
    grid-area: item6;
}

.masonry-layout-v3,
.masonry-layout-v4,
.grid-3-layout-md {
    display: grid;
    gap: 15px;
}

.masonry-layout-v3 .collection-inner,
.masonry-layout-v3 .collection-image,
.masonry-layout-v4 .collection-inner,
.masonry-layout-v4 .collection-image,
.grid-3-layout-md .collection-inner,
.grid-3-layout-md .collection-image {
    width: 100%;
    height: 100%;
}

.masonry-layout-v4.style-2 .item-1 .collection-image {
    height: 810px;
}

.masonry-layout-v4.style-2 .item-2 .collection-image,
.masonry-layout-v4.style-2 .item-3 .collection-image {
    height: 390px;
}

.masonry-layout-v5 {
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(2, 1fr);
}

.masonry-layout-v5 .tf-gallery-image {
    grid-area: span 1 / span 1;
}

.masonry-layout-v5 .tf-gallery-image.item-1 {
    grid-area: span 2 / span 2;
}

.masonry-layout-v6 {
    display: grid;
    grid-template-areas: "item1 item1" "item1 item1" "item2 item3" "item4 item4";
    gap: 15px;
}

.masonry-layout-v6 .item-1 {
    grid-area: item1;
}

.masonry-layout-v6 .item-2 {
    grid-area: item2;
}

.masonry-layout-v6 .item-3 {
    grid-area: item3;
}

.masonry-layout-v6 .item-4 {
    grid-area: item4;
}

.tf-gallery-image {
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    width: 100%;
}

.tf-gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-gallery-image::before {
    position: absolute;
    z-index: 2;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    top: 0;
    left: 0;
    transition: 0.4s ease 0.1s;
    opacity: 0;
    visibility: hidden;
}

.tf-gallery-image:hover::before {
    opacity: 1;
    visibility: visible;
}

.tf-img-with-text .tf-content-wrap {
    place-self: center;
}

.tf-img-with-text.style-1 {
    background-color: rgb(247, 247, 247);
}

.tf-img-with-text.style-1 .tf-image-wrap {
    order: unset;
}

.tf-img-with-text.style-2 {
    gap: 0;
}

.tf-img-with-text.style-2 .tf-image-wrap {
    order: unset;
}

.tf-img-with-text.style-2 .tf-image-wrap img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-img-with-text.style-3 {
    background-color: #fbf7f0;
}

.tf-img-with-text.style-3 .tf-image-wrap {
    order: unset;
}

.tf-img-with-text.style-3 .tf-content-wrap {
    width: 100%;
    height: 100%;
    padding-left: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tf-img-with-text.style-4 .grid-img-group {
    padding-bottom: 50px;
}

.tf-img-with-text.style-4 .tf-image-wrap {
    order: unset;
}

.tf-img-with-text.style-4 .heading {
    font-size: 28px;
    line-height: 33.6px;
}

.tf-img-with-text.style-4 .text {
    margin-top: 18px;
    color: var(--text);
}

.tf-img-with-text.style-4 .item-2 {
    margin-left: unset;
    width: 75%;
}

.tf-img-with-text.style-4 .item-1 {
    width: 43%;
    bottom: 0;
    right: 0;
    height: max-content;
    top: unset;
}

.tf-img-with-text.style-5 {
    border-radius: 10px;
    overflow: hidden;
}

.tf-img-with-text.style-5 .tf-image-wrap {
    order: unset;
}

.tf-img-with-text.style-6 {
    background-color: #eaf7f9;
    border-radius: 10px;
    overflow: hidden;
}

.tf-img-with-text .tf-image-wrap img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-img-with-text.bg-f5fbfd {
    background-color: #f5fbfd;
}

.tf-img-with-text.bg_primary {
    background-color: var(--primary);
}

.img-text-3 .grid-img-group .item-1 {
    top: 20%;
    width: 39%;
    height: max-content;
}

.img-text-3 .grid-img-group .item-2 {
    left: 14%;
    margin-left: unset;
    width: 66.67%;
}

.img-text-3 .grid-img-group .item-3 {
    position: absolute;
    top: 50%;
    right: 0;
    display: flex;
    align-items: center;
    z-index: 1;
    width: 30%;
}

.img-text-3 .tf-content-wrap {
    padding: 14px 0 0 0;
    text-align: start;
}

.img-text-3 .tf-content-wrap .desc {
    margin-top: 14px;
}

.img-text-3.img-text-3-style-2 .grid-img-group .item-1 {
    top: -8%;
    width: 33%;
}

.img-text-3.img-text-3-style-2 .grid-img-group .item-3 {
    width: 50%;
    top: 54%;
}

.img-text-3.img-text-3-style-2 .tf-image-wrap {
    border-radius: 10px;
    overflow: hidden;
}

.img-text-3.img-text-3-style-2 .tf-image {
    padding: 30px 0px;
}

.tf-img-video-text .content-wrap {
    padding: 67px 92px 90px 92px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.tf-img-video-text .content-wrap .heading {
    font-size: 52px;
    line-height: 62.4px;
    margin-bottom: 18px;
}

.tf-img-video-text .content-wrap p {
    margin-bottom: 60px;
}

.tf-img-video-text .content-wrap li {
    display: flex;
    gap: 17px;
    align-items: center;
}

.tf-img-video-text .content-wrap li:not(:last-child) {
    padding-bottom: 30px;
    border-bottom: 1px solid rgba(235, 235, 235, 0.15);
    margin-bottom: 30px;
}

.tf-img-video-text .content-wrap li .number {
    flex-shrink: 0;
    width: 53px;
    height: 53px;
    border-radius: 50%;
    border: 1px solid var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
}

.tf-img-video-text .content-wrap li .text {
    font-size: 16px;
    line-height: 24px;
}

.tf-img-video-text video {
    height: 710px;
}

.tf-content-wrap {
    padding: 14px;
    padding-bottom: 24px;
}

.tf-content-wrap .sub-heading {
    font-size: 14px;
    line-height: 16.8px;
}

.tf-content-wrap .heading {
    margin-top: 14px;
    font-size: 24px;
    line-height: 28.8px;
}

.tf-content-wrap .description {
    margin-top: 16px;
    font-size: 14px;
    line-height: 22.4px;
}

.tf-content-wrap .tf-btn {
    margin-top: 16px;
}

.count-down .description {
    margin-bottom: 20px;
    margin-top: 16px;
}

.count-down .tf-countdown-v2 .countdown__item {
    min-width: 60px;
    min-height: 50px;
    padding: 4px 8px;
    border-color: var(--white);
    background-color: var(--white);
}

.count-down .tf-countdown-v2 .countdown__item .countdown__value {
    font-size: 14px;
    line-height: 22.4px;
}

.count-down .tf-countdown-v2 .countdown__item .countdown__label {
    font-weight: 700;
    font-size: 10px;
    line-height: 16px;
}

.tf-content-wrap-v2 .description {
    margin-top: 12px;
    font-size: 16px;
    line-height: 19.2px;
}

.tf-content-wrap-v2 .tf-btn {
    margin-top: 22px;
}

.flat-wrap-countdown .tf-content-wrap-v2 {
    text-align: center;
}

.tf-image-wrap {
    position: relative;
    overflow: hidden;
}

.wrap-sw-over {
    padding-bottom: 40px;
    margin-bottom: -40px;
    padding-left: 30px;
    margin-left: -30px;
}

.widget-card-store {
    background-color: var(--bg-10);
}

.widget-card-store .store-heading {
    margin-bottom: 21px;
}

.widget-card-store .store-img {
    width: 100%;
    height: 100%;
}

.widget-card-store .store-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.widget-card-store .description p {
    font-size: 14px;
    line-height: 22.4px;
    color: var(--text-3);
}

.widget-card-store .description p:not(:last-child) {
    margin-bottom: 12px;
}

.widget-card-store .store-item-info {
    padding: 30px 15px;
}

.widget-card-store.type-1 {
    gap: 0;
}

.widget-card-store.type-1 .description {
    margin-bottom: 20px;
}

.widget-card-store.type-1 .store-heading {
    font-size: 24px;
    line-height: 28.8px;
}

.widget-card-store.type-1 .store-item-info {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: var(--main);
}

.widget-card-store.type-2 .description {
    margin-bottom: 27px;
}

.widget-card-store.type-2 .store-heading {
    font-size: 22px;
    line-height: 26.4px;
    margin-bottom: 15px;
}

.flat-testimonial-v2 {
    padding-top: 26px;
    padding-bottom: 26px;
}

.wrapper-thumbs-testimonial {
    display: flex;
    padding: 0px 100px;
    align-items: center;
}

.wrapper-thumbs-testimonial.wrap-carousel .nav-next-slider {
    left: 0;
}

.wrapper-thumbs-testimonial.wrap-carousel .nav-prev-slider {
    right: 0;
}

.wrapper-thumbs-testimonial .box-left {
    width: 47%;
    padding-left: 24px;
    padding-right: 83px;
}

.wrapper-thumbs-testimonial .box-right {
    width: 53%;
}

.wrapper-thumbs-testimonial .img-sw-thumb {
    border-radius: 10px;
    overflow: hidden;
}

.wrapper-thumbs-testimonial-v2 {
    display: flex;
    align-items: center;
}

.wrapper-thumbs-testimonial-v2 .box-left {
    width: 50%;
    padding-right: 60px;
}

.wrapper-thumbs-testimonial-v2 .box-right {
    width: 50%;
}

.wrapper-thumbs-testimonial-v2 .box-right img {
    width: 100%;
}

.wrapper-thumbs-testimonial-v2.type-1 .box-left {
    width: 60%;
}

.wrapper-thumbs-testimonial-v2.type-1 .box-right {
    width: 40%;
}

.flat-thumbs-testimonial-v2 {
    padding: 30px 15px;
    border-radius: 10px;
    overflow: hidden;
}

.flat-thumbs-testimonial-v2 .box-left {
    width: 70%;
    padding-right: 30px;
}

.flat-thumbs-testimonial-v2 .box-left .text {
    font-size: 16px;
    line-height: 25.6px;
}

.flat-thumbs-testimonial-v2 .box-left .box-sw-navigation {
    margin-top: 40px;
}

.flat-thumbs-testimonial-v2 .box-right {
    width: 30%;
}

.flat-lookbook-v2 {
    display: flex;
    gap: 15px;
    flex-direction: column-reverse;
}

.flat-lookbook-v3 .flat-title-lookbook {
    margin-bottom: 22px;
}

.flat-lookbook-v3 form {
    max-width: 80%;
}

.flat-lookbook-v3 .tf-lookbook {
    height: 830px;
    flex-direction: column;
}

.slider-wrap-lookbook {
    position: relative;
}

.slider-wrap-lookbook .nav-sw {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}

.slider-wrap-lookbook .nav-next-slider {
    left: -7px;
}

.slider-wrap-lookbook .nav-prev-slider {
    right: -7px;
}

.flat-title-lookbook {
    margin-bottom: 20px;
}

.flat-title-lookbook .heading {
    margin-top: 8px;
    font-size: 30px;
    line-height: 36px;
}

.flat-title-lookbook .label {
    font-size: 14px;
    line-height: 22.4px;
    font-weight: 700;
    color: rgb(84, 84, 84);
}

.flat-iconbox-v2 .tf-sw-mobile {
    border-radius: 10px;
    border: 1px solid var(--line);
    padding: 5px 0px;
}

.flat-iconbox-v2 .tf-icon-box {
    padding: 15px;
}

.flat-iconbox-v2 .tf-icon-box .title {
    font-weight: 400;
}

.flat-iconbox-v2 .tf-icon-box .icon {
    margin-bottom: 22px;
}

.flat-iconbox-v2 .tf-icon-box .icon i {
    color: var(--main);
}

.flat-iconbox-v2.style-2 .tf-icon-box {
    padding: 0px;
    border: none !important;
}

.flat-iconbox-v2.style-2 .tf-icon-box .icon i {
    font-size: 36px;
}

.flat-iconbox-v3 .tf-icon-box .icon {
    width: 74px;
    height: 74px;
    border: 1px solid var(--main);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 34px;
}

.flat-iconbox-v3 .tf-icon-box .icon i {
    color: var(--main);
    font-size: 30px;
}

.flat-iconbox-v3 .tf-icon-box .icon.no-border {
    border: 0;
    border-radius: 0;
}

.flat-iconbox-v3 .tf-icon-box .icon.w-40 {
    width: 40px;
    height: 40px;
}

.flat-iconbox-v3 .tf-icon-box .icon.w-40 i {
    font-size: 40px;
}

.flat-iconbox-v3 .tf-icon-box .icon.w-50 {
    width: 50px;
    height: 50px;
}

.flat-iconbox-v3 .tf-icon-box.small .content .title {
    font-size: 18px;
    font-weight: 400;
    line-height: 22px;
}

.flat-iconbox-v3 .tf-icon-box.small .content p {
    font-size: 14px;
}

.wrap-spacing-iconbox {
    padding: 33px 15px;
}

.tf-icon-box-v2 {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 20px;
}

.tf-icon-box-v2 .icon {
    font-size: 26px;
    flex-shrink: 0;
}

.tf-icon-box-v2 .icon svg {
    width: 30px;
    height: 30px;
}

.tf-icon-box-v2 .title {
    font-weight: 500;
    font-size: 18px;
    line-height: 21.6px;
}

.tf-icon-box-v3 .title {
    margin-bottom: 10px;
    font-size: 21px;
    line-height: 25.2px;
}

.flat-wrap-iconbox-v2 {
    padding: 73px 15px;
}

.flat-location {
    position: relative;
}

.flat-location .banner-map {
    width: 100%;
    height: 300px;
}

.flat-location .banner-map img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.flat-location .content {
    padding: 20px;
    text-align: center;
    background-color: var(--white);
}

.flat-location .content .heading {
    font-size: 18px;
    line-height: 21.6px;
}

.flat-location .content .subtext {
    margin-top: 16px;
    color: var(--text);
}

.flat-location .content .tf-btn {
    margin-top: 20px;
}

.scroll-process {
    height: 5px;
    background-color: rgba(0, 0, 0, 0.2);
    position: relative;
}

.scroll-process .value-process {
    position: absolute;
    inset: 0;
    background-color: var(--main);
    width: 30%;
    transition: width 1s;
}

.scroll-snap {
    scrollbar-width: none;
    overscroll-behavior-x: contain;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    scroll-snap-type: x mandatory;
}

.fullwidth {
    display: block;
    width: 100%;
    height: 100%;
}

.fullwidth img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.tf-marquee.marquee-lg p {
    white-space: nowrap;
}

.tf-marquee.marquee-lg p a {
    border-bottom: 1px solid currentColor;
}

.tf-marquee.marquee-lg p {
    font-size: 40px;
    line-height: 56px;
}

.tf-marquee.marquee-lg {
    padding: 30px;
}

.tf-marquee.style-2 .marquee-item {
    gap: 39px;
}

.icv__circle {
    width: 34px;
    height: 34px;
    background-color: var(--main);
    border: 0 !important;
}

.icv__arrow-wrapper svg {
    width: 15px !important;
    height: 15px !important;
    margin: 0px 5px;
}

.flat-wrap-giftcard .wrap-content {
    display: flex;
    gap: 25px;
    align-items: center;
}

.flat-wrap-giftcard .wrap-content p {
    font-size: 20px;
    margin-top: 5px;
}

.tf-breadcrumb-wrap {
    padding: 22.5px 0;
}

.tf-breadcrumb-wrap .tf-breadcrumb-list {
    display: flex;
    gap: 6px;
    align-items: center;
    justify-content: center;
}

.tf-breadcrumb-wrap .tf-breadcrumb-list .icon {
    font-size: 8px;
    font-weight: 700;
}

.tf-breadcrumb-wrap .tf-breadcrumb-list span {
    color: var(--text);
}

.tf-breadcrumb-wrap .tf-breadcrumb-prev-next {
    display: flex;
    gap: 14px;
    align-items: center;
    font-size: 17px;
}

.tf-brands-filter {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    max-width: 1200px;
    margin: 0 auto;
    gap: 10px;
    padding-bottom: 30px;
}

.tf-brands-filter .tf-btns-filter {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: var(--main);
    background-color: transparent;
    border: solid 1px var(--line-2);
    border-radius: 3px;
    padding: 8px 15px;
    min-width: 42px;
    font-weight: 600;
    max-width: 100%;
    text-wrap: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.tf-brands-filter .tf-btns-filter.is--active,
.tf-brands-filter .tf-btns-filter:hover {
    background-color: var(--main);
    color: var(--white);
}

.tf-brands-filter .tf-btns-filter.is-disable {
    pointer-events: none;
}

.tf-brands-filter .tf-btns-filter.is-disable span {
    opacity: 0.2;
}

.tf-brands-source-linklist {
    gap: 15px;
    grid-template-columns: repeat(2, 1fr);
}

.tf-brands-source-linklist.style-row {
    border-top: 1px solid var(--line);
}

.tf-brands-source-linklist.style-row .tf-filter-item {
    border: 0;
    border-bottom: 1px solid var(--line);
    padding: 15px;
}

.tf-brands-source-linklist.style-row .tf-filter-item-inner {
    gap: 20px;
    flex-direction: row;
}

.tf-brands-source-linklist.style-row .tf-titles-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16.66666667%;
}

.tf-brands-source-linklist.style-row .tf-titles-filter h4 {
    width: 100%;
    text-align: center;
    border-right: 1px solid var(--line);
    padding-right: 10px;
}

.tf-brands-source-linklist.style-row .tf-content-brands {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(2, 1fr);
}

.tf-brands-source-linklist.style-row .tf-item-inner {
    display: flex;
    gap: 10px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tf-brands-source-linklist.style-row .tf-item-inner a {
    margin-bottom: 0;
}

.tf-filter-item {
    animation: slideInRight 0.5s ease-in-out;
    padding: 15px;
    border-radius: 2.5px;
    border: 1px solid var(--line);
}

.tf-filter-item.is-disable {
    display: none;
}

.tf-filter-item .tf-filter-item-inner {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tf-filter-item .tf-item-inner a {
    color: var(--text);
    margin-bottom: 5px;
}

.tf-filter-item .tf-item-inner a:hover {
    color: var(--primary);
}

@keyframes slideInRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.tf-content-left.has-mt {
    margin-top: 10px;
}

.tf-accordion-wrap > .box,
.tf-accordion-wrap > .content {
    width: 100%;
}

.tf-accordion-wrap .tf-other-content {
    padding: 30px 31px 64px 35px;
    height: max-content;
}

.tf-accordion-link-list {
    padding: 25px 38px 29px;
}

.tf-accordion-link-list .tf-link-item a {
    padding-top: 15px;
    padding-bottom: 15px;
}

.tf-accordion-link-list .tf-link-item a:hover {
    border-color: var(--main);
}

.tf-accordion-link-list .tf-link-item a:hover .icon {
    opacity: 1;
    visibility: visible;
    transform: translate(0);
}

.tf-accordion-link-list .tf-link-item a .icon {
    font-size: 8px;
    opacity: 0;
    visibility: hidden;
    transition: 0.4s ease 0.1s;
    transform: translate(-20px);
}

.tf-ourstore-img {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.tf-ourstore-img:hover img {
    transform: scale(1.1);
}

.tf-ourstore-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: 0.35s linear;
}

.tf-store-item {
    padding: 18px 15px 20px;
    border-radius: 10px;
    border: 1px solid var(--line);
    cursor: pointer;
}

.tf-store-item .tf-store-title {
    position: relative;
    transition: 0.4s ease 0s;
    margin-bottom: 17px;
    line-height: 24px;
}

.tf-store-item .tf-store-title .icon {
    opacity: 0;
    visibility: hidden;
    transform: scale(0);
    z-index: 1;
    margin: auto;
    transition: 0.4s ease 0s;
    position: absolute;
    left: 0;
    font-size: 16px;
}

.tf-store-item .tf-store-info span {
    font-weight: 600;
}

.tf-store-item.active {
    color: var(--white);
    background-color: var(--main);
}

.tf-store-item.active .tf-store-title {
    padding-left: 26px;
    color: var(--white);
}

.tf-store-item.active .tf-store-title .icon {
    transform: scale(1);
    opacity: 1;
    visibility: visible;
    color: var(--white);
}

.tf-timeline-wrap {
    padding-top: 130px;
    padding-bottom: 20px;
}

.tf-timeline-line {
    position: absolute;
    width: 1px;
    border: 1px dashed var(--line);
    height: 100%;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
}

.tf-timeline-line::after,
.tf-timeline-line::before {
    content: "";
    position: absolute;
    z-index: 3;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 10px;
    z-index: 3;
    background-color: var(--line);
    border-radius: 50%;
}

.tf-timeline-line::before {
    top: 0;
}

.tf-timeline-line::after {
    bottom: 0;
}

.tf-timeline-inner {
    gap: 30px;
    padding-top: 80px;
    padding-bottom: 80px;
    flex-direction: column;
}

.tf-timeline-time {
    position: absolute;
    z-index: 3;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--main);
    color: var(--white);
    border-radius: 2.5px;
    padding: 0 35px;
    font-size: 20px;
    line-height: 36px;
}

.tf-timeline-content,
.tf-timeline-image {
    max-width: 490px;
}

.tf-timeline-content {
    background-color: var(--white);
    text-align: center;
}

.tf-timeline-content .tf-timeline-label {
    margin-bottom: 7px;
}

.tf-timeline-content .tf-timeline-title {
    margin-bottom: 10px;
}

.tf-compare-table {
    border: 1px solid var(--line);
    border-radius: 2.5px;
    overflow-x: scroll;
}

.tf-compare-table::-webkit-scrollbar {
    height: 4px;
}

.tf-compare-table::-webkit-scrollbar-thumb {
    background: var(--line-2);
}

.tf-compare-row {
    display: flex;
}

.tf-compare-row:nth-child(2n + 2) > div {
    background-color: rgba(0, 0, 0, 0.05);
}

.tf-compare-col {
    min-width: 180px;
    border-right: 1px solid var(--line);
    position: relative;
}

.tf-compare-item {
    padding: 16px 10px;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.tf-compare-item .tf-compare-remove {
    font-size: 12px;
    line-height: 19px;
    color: var(--text);
    text-decoration: underline;
    transition: 0.3s ease;
    cursor: pointer;
}

.tf-compare-item .tf-compare-image {
    width: 100%;
    padding: 18px 0;
}

.tf-compare-item .tf-compare-title {
    font-size: 16px;
    line-height: 25.6px;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    overflow: hidden;
}

.tf-compare-item .price {
    margin-top: 10px;
    font-size: 16px;
    line-height: 16px;
    font-weight: 600;
}

.tf-compare-item .price .compare-at-price {
    font-size: 13px;
    line-height: 13px;
    text-decoration: line-through;
    color: rgba(0, 0, 0, 0.55);
    margin-right: 10px;
}

.tf-compare-item .price .price-on-sale {
    color: var(--primary);
}

.tf-compare-item .tf-compare-group-btns {
    margin-top: 21px;
    margin-bottom: 24px;
    justify-content: center;
}

.tf-compare-item .tf-compare-group-btns a {
    padding: 0 15px;
    font-size: 12px;
    font-weight: 700;
    height: 42px;
    gap: 10px;
    border-color: var(--line);
}

.tf-compare-item .tf-compare-group-btns a i {
    font-size: 15px;
}

.tf-compare-item .tf-compare-group-btns a span {
    display: none;
}

.tf-compare-field,
.tf-compare-value {
    padding: 16px 10px;
}

.tf-compare-value {
    display: flex;
    align-items: center;
    justify-content: center;
}

.tf-compare-stock {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
    color: #83b735;
}

.tf-compare-stock .icon {
    width: 16px;
    height: 16px;
    background-color: #83b735;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tf-compare-stock .icon i {
    color: var(--white);
    font-size: 7px;
}

.tf-main-area-page h4 {
    font-size: 22px;
    font-weight: 400;
    color: var(--main);
    margin-bottom: 26px;
    line-height: 28px;
}

.tf-main-area-page p {
    margin-top: 15px;
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 24px;
    color: rgb(134, 134, 134);
}

.tf-main-area-page p:last-child {
    margin-bottom: 0px;
}

.tf-main-area-page .box:not(:last-child) {
    margin-bottom: 15px;
}

.tf-main-area-page .tag-list li {
    color: rgb(134, 134, 134);
    line-height: 26px;
    margin-bottom: 8px;
    margin-top: 15px;
}

.tf-main-area-page .tag-list li:last-child {
    margin-bottom: 0px;
}

.tf-main-area-page .cf-mail {
    text-underline-offset: 3px;
    text-decoration-thickness: 1px;
    text-decoration-line: underline;
    transition: text-decoration-thickness 1s ease;
    color: rgb(134, 134, 134);
}

.tf-main-area-page .cf-mail:hover {
    color: rgba(0, 0, 0, 0.85);
}

.tf-page-delivery h4 {
    margin-bottom: 20px;
}

.tf-page-delivery p {
    margin-bottom: 10px;
}

.tf-terms-conditions .box:not(:last-child) {
    margin-bottom: 50px;
}

.tf-terms-conditions .box span {
    color: rgb(134, 134, 134);
}

.images-group-item.active {
    display: block;
}

.images-group-item.hidden {
    display: none;
}

.progress-wrap {
    position: fixed;
    bottom: 90px;
    right: 40px;
    height: 40px;
    width: 40px;
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border-radius: 999px;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    -webkit-transform: translateY(20px);
    -ms-transform: translateY(20px);
    transform: translateY(20px);
    -webkit-transition: all 400ms linear;
    -o-transition: all 400ms linear;
    transition: all 400ms linear;
    background: var(--white);
    border: 1.2px solid rgba(134, 134, 134, 0.12);
}

.progress-wrap::after {
    position: absolute;
    content: "\e907";
    font-family: "icomoon";
    text-align: center;
    line-height: 40px;
    font-size: 8px;
    color: var(--main);
    height: 40px;
    width: 40px;
    cursor: pointer;
    z-index: 1;
    -webkit-transition: all 400ms linear;
    -o-transition: all 400ms linear;
    transition: all 400ms linear;
}

.progress-wrap.active-progress {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    transform: translateY(0);
}

.progress-wrap svg path {
    fill: none;
}

.progress-wrap svg.progress-circle path {
    box-sizing: border-box;
    stroke: var(--main);
    stroke-width: 1.2;
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
    transition-duration: 0.4s;
    transition-timing-function: linear;
}

.tf-cart-countdown {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.tf-cart-countdown p {
    font-size: 16px;
    line-height: 25.6px;
}

.tf-cart-countdown svg {
    margin-top: -3px;
    animation: tf-ani-flash 2s infinite;
}

.tf-cart-countdown .timer-count {
    color: var(--white);
    background-color: var(--primary);
    padding: 2px 10px;
    font-weight: 600;
    display: inline-block;
    text-align: center;
    min-width: 110px;
    border-radius: 9999px;
    font-size: 16px;
    line-height: 25.6px;
}

.tf-cart-countdown .title-left {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

@keyframes tf-ani-flash {
    50%,
    from,
    to {
        opacity: 1;
    }

    25%,
    75% {
        opacity: 0;
    }
}

.tf-table-page-cart {
    border-spacing: 0;
    border-collapse: collapse;
    margin-bottom: 30px;
    width: 100%;
    line-height: 1.4;
}

.tf-table-page-cart th {
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid var(--line);
    color: var(--main);
    text-align: center;
    padding: 18px 0px;
}

.tf-table-page-cart th:first-child {
    text-align: start;
}

.tf-table-page-cart th:last-child {
    text-align: end;
}

.tf-table-page-cart tr {
    border-bottom: 1px dashed var(--line);
}

.tf-table-page-cart tr:last-child {
    border-bottom-style: solid;
}

.tf-table-page-cart td {
    padding: 18px 10px;
}

.tf-table-page-cart td:first-child {
    text-align: start;
    padding-left: 0;
}

.tf-table-page-cart td:last-child {
    text-align: end;
    padding-right: 0;
}

.tf-cart-item .tf-cart-item_product {
    display: flex;
}

.tf-cart-item .tf-cart-item_product .cart-info .cart-title {
    font-size: 14px;
    line-height: 19.6px;
    display: block;
}

.tf-cart-item .tf-cart-item_product .cart-info .cart-meta-variant {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    line-height: 16.8px;
    color: var(--text);
}

.tf-cart-item .tf-cart-item_product .cart-info .remove-cart {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    line-height: 16.8px;
    text-decoration: underline;
    cursor: pointer;
}

.tf-cart-item .tf-cart-item_product .img-box {
    width: 80px;
    max-height: 110px;
    margin-right: 24px;
    border-radius: 2.5px;
    overflow: hidden;
}

.tf-cart-item .cart-quantity,
.tf-cart-item .cart-total,
.tf-cart-item .cart-price {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: self-end;
    justify-content: safe center;
    gap: 10px;
    line-height: 1;
}

.tf-cart-item .cart-total {
    justify-content: flex-end;
    font-weight: 600;
}

.tf-cart-item .wg-quantity {
    min-width: 86px;
    width: 86px;
    height: 30px;
}

.tf-cart-item .wg-quantity .btn-quantity {
    width: 25px;
    height: 30px;
    line-height: 25px;
}

.tf-cart-item .wg-quantity input {
    width: 30px;
    height: 30px;
    font-size: 12px;
    font-weight: 500;
    line-height: 16.8px;
}

.tf-page-cart-footer .tf-free-shipping-bar {
    margin-bottom: 20px;
    padding: 30px;
    border: 1px dashed var(--line);
    border-radius: 2.5px;
}

.tf-page-cart-footer .tf-free-shipping-bar .tf-progress-msg {
    margin-top: 28px;
}

.tf-page-cart-checkout {
    padding: 30px;
    background-color: var(--bg-11);
    border-radius: 2.5px;
}

.tf-page-cart-checkout .shipping-calculator {
    padding-bottom: 18px;
    border-bottom: 1px solid var(--line);
}

.tf-page-cart-checkout .shipping-calculator .shipping-calculator-title {
    font-size: 16px;
    font-weight: 600;
    line-height: 19.2px;
}

.tf-page-cart-checkout
  .shipping-calculator
  .shipping-calculator_accordion-icon {
    width: 20px;
    height: 20px;
    flex: 0 0 auto;
    margin-inline-start: 5px;
    position: relative;
}

.tf-page-cart-checkout  .shipping-calculator  .shipping-calculator_accordion-icon::after,
.tf-page-cart-checkout  .shipping-calculator  .shipping-calculator_accordion-icon::before {
    position: absolute;
    content: "";
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-90deg);
    background-color: currentColor;
    transition: transform 0.35s ease-in-out, opacity 0.35s ease-in-out;
    width: 12px;
    height: 2px;
    opacity: 1;
    border-radius: 9999px;
}

.tf-page-cart-checkout
  .shipping-calculator
  .shipping-calculator_accordion-icon::after {
    height: 12px;
    width: 2px;
}

.tf-page-cart-checkout  .shipping-calculator  .accordion-shipping-header:not(.collapsed)  .shipping-calculator_accordion-icon::after,
.tf-page-cart-checkout  .shipping-calculator  .accordion-shipping-header:not(.collapsed)  .shipping-calculator_accordion-icon::before {
    transform: translate(-50%, -50%) rotate(90deg);
}

.tf-page-cart-checkout
  .shipping-calculator
  .accordion-shipping-header:not(.collapsed)
  .shipping-calculator_accordion-icon::before {
    opacity: 0;
}

.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content {
    margin-top: 20px;
}

.tf-page-cart-checkout
  .shipping-calculator
  .accordion-shipping-content
  .tf-select {
    height: 50px;
}

.tf-page-cart-checkout .shipping-calculator .accordion-shipping-content .field {
    margin-bottom: 15px;
}

.tf-page-cart-checkout
  .shipping-calculator
  .accordion-shipping-content
  .field
  .label {
    margin-bottom: 8px;
    font-weight: 400;
}

.tf-page-cart-checkout  .shipping-calculator  .accordion-shipping-content  .field  .tf-select,
.tf-page-cart-checkout  .shipping-calculator  .accordion-shipping-content  .field  input {
    border-color: transparent;
}

.tf-page-cart-checkout
  .shipping-calculator
  .accordion-shipping-content
  .tf-btn {
    font-weight: 600;
    min-width: 199px;
}

.tf-page-cart-checkout .cart-checkbox {
    margin-top: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.tf-page-cart-checkout .cart-checkbox .tf-check {
    width: 16px;
    height: 16px;
    border-color: var(--line);
    background-color: var(--white);
    border-radius: 999px;
    overflow: hidden;
}

.tf-page-cart-checkout .cart-checkbox .tf-check:checked {
    border-color: var(--primary);
    background-color: var(--primary);
}

.tf-page-cart-checkout .cart-checkbox .tf-check::before {
    font-size: 6px;
}

.tf-page-cart-checkout .cart-checkbox a {
    text-decoration: underline;
    text-underline-offset: 2px;
    font-weight: 600;
}

.tf-page-cart-checkout .tf-cart-totals-discounts {
    margin-top: 18px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tf-page-cart-checkout .tf-cart-totals-discounts h3 {
    font-size: 20px;
    line-height: 24px;
}

.tf-page-cart-checkout .tf-cart-totals-discounts .total-value {
    font-size: 20px;
    line-height: 24px;
    font-weight: 600;
}

.tf-page-cart-checkout .tf-cart-tax {
    margin-top: 18px;
    color: var(--text);
}

.tf-page-cart-checkout .tf-cart-tax a {
    color: rgba(0, 0, 0, 0.85);
    text-underline-offset: 3px;
    text-decoration-thickness: 1px;
    text-decoration-line: underline;
    transition: text-decoration-thickness 1s ease;
}

.tf-page-cart-checkout .tf-cart-tax a:hover {
    color: var(--main);
    text-decoration-thickness: 2px;
}

.tf-page-cart-checkout .cart-checkout-btn {
    margin-top: 18px;
}

.tf-page-cart-checkout .cart-checkout-btn .tf-btn {
    font-weight: 400;
}

.tf-page-cart-checkout .tf-page-cart_imgtrust {
    margin-top: 18px;
}

.tf-page-cart-checkout .tf-page-cart_imgtrust p {
    margin-bottom: 10px;
}

.tf-page-cart-checkout .tf-page-cart_imgtrust .cart-list-social {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tf-page-cart-note label {
    margin-bottom: 20px;
    display: block;
}

#cart-note {
    min-height: 126px;
    border: 1px solid var(--line);
    padding: 15px 20px;
    width: 100%;
    max-width: 460px;
    resize: none;
    border-radius: 3px;
    outline: none;
}

/* Preload 
------------------------------------------- */
.preload-wrapper .preload-container {
    display: flex;
}

.preload-container {
    display: none;
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    z-index: 99999999999;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.spinner {
    width: 60px;
    height: 60px;
    border: 3px solid transparent;
    border-top: 3px solid var(--line);
    border-radius: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.flat-alert {
    margin-bottom: 10px;
}

.flat-alert.msg-success {
    font-weight: 500;
    color: var(--bs-success);
}

.flat-alert.msg-error {
    font-weight: 500;
    color: var(--bs-danger);
}

#subscribe-msg {
    color: var(--bs-success);
}

#subscribe-msg .notification_ok {
    font-weight: 500;
    color: var(--bs-success);
}

#subscribe-msg .notification_error {
    font-weight: 500;
    color: var(--bs-danger);
}

.flat-wrap-iconbox {
    padding: 51px 15px 85px;
}

.flat-wrap-countdown.countdown-black {
    color: var(--white);
}

.flat-wrap-countdown.countdown-black .heading,
.flat-wrap-countdown.countdown-black .description,
.flat-wrap-countdown.countdown-black .tf-btn {
    color: var(--white);
}

.flat-wrap-countdown.countdown-black .tf-btn::after {
    background-color: var(--white);
}

.flat-wrap-countdown.countdown-black .tf-btn:hover {
    color: var(--primary);
}

.flat-wrap-countdown.countdown-black .tf-btn:hover::after {
    background-color: var(--primary);
}

.flat-wrap-countdown.countdown-black .countdown__item {
    border-color: var(--white);
}

.tf-flash-sale {
    border: 2px solid var(--primary);
    padding: 75px 20px 20px;
    border-radius: 10px;
    position: relative;
}

.tf-flash-sale .heading-flash-sale {
    position: absolute;
    left: 0;
    top: -4%;
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--white);
    padding: 4px 16px;
    margin-left: 15px;
    color: var(--primary);
    font-weight: 400;
    font-size: 22px;
    line-height: 26.4px;
}

.wrap-mobile .collection-item-v2 .collection-content {
    inset: 7%;
}

.form-checkout .box:not(:last-child) {
    margin-bottom: 15px;
}

.form-checkout .grid-2 {
    gap: 15px;
}

.form-checkout label {
    margin-bottom: 10px;
    position: relative;
}

.form-checkout label::after {
    content: "*";
    position: absolute;
    color: var(--primary);
}

.form-checkout .tf-select {
    height: 50px;
    color: var(--main);
}

.form-checkout .select-custom::after {
    right: 15px;
}

.form-checkout input,
.form-checkout textarea {
    color: var(--main);
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.form-checkout input:focus,
.form-checkout textarea:focus {
    border-color: var(--main);
}

.widget-wrap-checkout {
    display: grid;
    gap: 20px;
    background-color: #fbfbfc;
    border: 10px;
}

.widget-wrap-checkout .checkout-product-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.widget-wrap-checkout .checkout-product-item:not(:last-child) {
    margin-bottom: 15px;
}

.widget-wrap-checkout .checkout-product-item .img-product {
    width: 64px;
    height: 64px;
    border: 1px solid var(--line);
    border-radius: 3px;
    position: relative;
}

.widget-wrap-checkout .checkout-product-item .img-product .quantity {
    position: absolute;
    right: -8px;
    top: -8px;
    width: 20px;
    height: 20px;
    border-radius: 999px;
    background-color: #666;
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.widget-wrap-checkout .checkout-product-item .img-product img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.widget-wrap-checkout .checkout-product-item .content {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.widget-wrap-checkout .checkout-product-item .content .variant {
    font-size: 12px;
    line-height: 18px;
    color: var(--text);
}

.widget-wrap-checkout .coupon-box {
    display: flex;
    gap: 10px;
}

.widget-wrap-checkout .coupon-box .tf-btn {
    flex-shrink: 0;
}

.widget-wrap-checkout .wd-check-payment .fieldset-radio {
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-wrap-checkout .wd-check-payment .fieldset-radio label {
    margin-top: 2px;
    font-weight: 400;
    color: var(--text);
}

.widget-wrap-checkout .wd-check-payment input {
    width: 20px;
    height: 20px;
    border-radius: 50%;
}

.widget-wrap-checkout .wd-check-payment input:checked ~ label {
    color: var(--main);
}

.widget-wrap-checkout .wd-check-payment .box-checkbox input {
    border-radius: 3px;
    width: 18px;
    height: 18px;
}

.hover-sw-nav .tf-sw-lookbook .nav-sw {
    top: 55%;
}

.hover-sw-nav .tf-sw-lookbook .nav-next-slider {
    left: 40px;
}

.hover-sw-nav .tf-sw-lookbook .nav-prev-slider {
    right: 40px;
}

.page-search-inner .tf-search-head {
    max-width: 608px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 77px;
}

.page-search-inner .tf-col-quicklink {
    display: flex;
    flex-wrap: wrap;
    margin-top: 14px;
}

.page-search-inner .tf-col-quicklink .title {
    font-weight: 600;
    margin-right: 9px;
}

.page-search-inner .tf-search-content-title {
    text-align: center;
    margin-bottom: 30px;
    font-weight: 500;
    font-size: 20px;
    line-height: 24px;
}

.pagination-wrap-spacing {
    margin-top: 57px;
}

.box-video-wrap video {
    max-height: 702px;
}

.tf-form-sneaker .flat-title .title {
    font-size: 18px;
    line-height: 21px;
}

.tf-form-sneaker .box-content {
    max-width: 650px;
    padding: 20px;
    margin: 0 auto;
}

.my-account-nav {
    display: flex;
    flex-direction: column;
    gap: 10px;
    position: sticky;
    top: 0;
}

.my-account-nav .my-account-nav-item {
    display: flex;
    width: 100%;
    border: 1px solid var(--line);
    padding: 15px 20px;
    border-radius: 3px;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    position: relative;
}

.my-account-nav .my-account-nav-item::before {
    position: absolute;
    content: "";
    bottom: 0;
    left: auto;
    right: 0;
    width: 0;
    height: 1px;
    background-color: #db1215;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.my-account-nav .my-account-nav-item:hover,
.my-account-nav .my-account-nav-item.active {
    background-color: var(--bg-11);
    color: var(--primary);
    border-color: transparent;
}

.account-order .wrap-account-order {
    overflow-x: auto;
}

.account-order .wrap-account-order::-webkit-scrollbar {
    height: 3px;
}

.account-order .wrap-account-order::-webkit-scrollbar-thumb {
    background: var(--bg-11);
    border-radius: 999px;
}

.account-order table {
    width: 100%;
    min-width: 700px;
}

.account-order table thead,
.account-order table td,
.account-order table th {
    padding: 15px 20px;
}

.account-order table thead tr {
    border: 1px solid var(--line);
    background-color: var(--bg-11);
}

.account-order table tbody {
    border: 1px solid var(--line);
    border-top: 0;
}

.account-order table tbody tr:not(:last-child) {
    border-bottom: 1px solid var(--line);
}

.show-form-address,
.edit-form-address {
    display: none;
}

.widget-order-tab {
    margin-top: 30px;
}

.widget-order-tab.widget-tabs {
    border: none;
}

.widget-order-tab.widget-tabs .widget-menu-tab {
    margin: 0 !important;
    gap: 20px !important;
}

.widget-order-tab.widget-tabs .widget-menu-tab .item-title {
    padding: 5px 0px;
    font-weight: 500;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

.widget-order-tab.widget-tabs .widget-menu-tab .item-title::after {
    left: 50%;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.widget-order-tab.widget-tabs .widget-menu-tab .item-title.active,
.widget-order-tab.widget-tabs .widget-menu-tab .item-title:hover {
    color: var(--primary);
}

.widget-order-tab.widget-tabs .widget-menu-tab .item-title.active::after,
.widget-order-tab.widget-tabs .widget-menu-tab .item-title:hover::after {
    background-color: var(--primary);
    width: 100%;
}

.widget-order-tab.widget-tabs .widget-content-tab .widget-content-inner {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

.widget-timeline .timeline {
    list-style: none;
    position: relative;
}

.widget-timeline .timeline::before {
    top: 20px;
    bottom: 48px;
    position: absolute;
    content: " ";
    width: 2px;
    left: 10px;
    border-right: 1px dashed var(--text-2);
}

.widget-timeline .timeline > li {
    margin-bottom: 15px;
    position: relative;
}

.widget-timeline .timeline > li .timeline-box {
    padding: 10px 10px 10px 15px;
    position: relative;
    display: block;
    margin-left: 40px;
}

.widget-timeline .timeline > li .timeline-badge {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    height: 22px;
    left: 0;
    position: absolute;
    top: 10px;
    width: 22px;
    padding: 4px;
    background-color: var(--white);
    border: 1.5px solid var(--text-2);
}

.widget-timeline .timeline > li .timeline-badge::after {
    content: "";
    width: 10px;
    height: 10px;
    border-radius: 100%;
    display: block;
    background: var(--text-2);
}

.widget-timeline .timeline > li .timeline-badge.success {
    border-color: var(--success);
}

.widget-timeline .timeline > li .timeline-badge.success::after {
    background: var(--success);
}

.wrapper-invoice {
    background-color: var(--bg-11);
    display: flex;
    justify-content: center;
    align-items: center;
}

.invoice-section {
    padding: 120px 0px;
    width: 100%;
}

.invoice-section .top {
    text-align: right;
    margin-bottom: 36px;
}

.box-invoice {
    background: var(--white);
    box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.05);
    border-radius: 16px;
}

.box-invoice .header {
    padding: 133px 100px 122px;
    border-bottom: 1px solid var(--line);
}

.box-invoice .wrap-top {
    display: flex;
    margin-bottom: 56px;
    align-items: center;
    gap: 15px;
}

.box-invoice .box-left {
    width: 60%;
}

.box-invoice .wrap-top .box-right {
    width: 40%;
}

.box-invoice .wrap-top .title {
    font-size: 28px;
    line-height: 36.46px;
    color: var(--theme-color-dark);
    font-weight: 700;
    white-space: nowrap;
}

.box-invoice .wrap-top .price {
    font-size: 17px;
    line-height: 28px;
    font-weight: 500;
}

.box-invoice .wrap-date {
    display: flex;
    margin-bottom: 60px;
}

.box-invoice .wrap-date label {
    font-size: 15px;
    line-height: 28px;
    display: block;
}

.box-invoice .wrap-date .date {
    font-size: 15px;
    line-height: 28px;
    font-weight: 500;
}

.box-invoice .wrap-info {
    margin-bottom: 40px;
    display: flex;
}

.box-invoice .wrap-info .title {
    font-size: 20px;
    line-height: 26.04px;
    font-weight: 500;
    margin-bottom: 12px;
}

.box-invoice .wrap-info .sub {
    font-size: 15px;
    line-height: 28px;
    font-weight: 500;
}

.box-invoice .wrap-info .desc {
    font-size: 15px;
    line-height: 28px;
    margin-bottom: 0;
    font-weight: 400;
}

.invoice-table {
    width: 100%;
}

.invoice-table thead {
    background-color: #f6f6f6;
}

.invoice-table .title th {
    padding: 20px 65px 20px 40px;
    color: var(--primary);
    font-weight: 500;
    font-size: 17px;
    line-height: 28px;
    text-wrap: nowrap;
}

.invoice-table .title th:first-child {
    border-radius: 8px 0 0 8px;
    width: 40%;
}

.invoice-table .title th:last-child {
    border-radius: 0px 8px 8px 0px;
}

.invoice-table .content td {
    padding: 20px 65px 20px 40px;
    font-size: 15px;
    line-height: 28px;
    border-bottom: 1px solid rgb(225, 225, 225);
    text-wrap: nowrap;
}

.invoice-table .content .total {
    font-size: 17px;
    line-height: 28px;
    font-weight: 500;
}

.invoice-table .content:last-child td {
    border-bottom: none;
}

.box-invoice .footer {
    padding: 43px 30px;
}

.box-invoice .footer .box-contact {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.box-invoice .footer .box-contact li {
    font-size: 15px;
    line-height: 28px;
}

.box-invoice .footer .box-contact li:first-child {
    color: var(--primary);
}

/* -------------- Responsive ----------------- */
/* Media Queries
-------------------------------------------------------------- */
@media (min-width: 576px) {
    .card-product .description {
        display: block;
    }

    .card-product .countdown-box {
        bottom: 46px;
    }

    .card-product .btn-quick-add {
        height: 36px;
        line-height: 36px;
    }

    .card-product.style-4 .size-list {
        bottom: 36px;
    }

    .card-product.style-9 .card-product-info .box-icon {
        width: 36px;
        height: 36px;
    }

    .list-product-btn .box-icon {
        width: 36px;
        height: 36px;
    }

    .modal-newleter .modal-dialog {
        max-width: 625px;
    }
}

@media (min-width: 768px) {
    .wd-form-address {
        padding: 10px 40px 20px;
    }

    .widget-tab-5 .nav-tab-item a {
        font-size: 16px;
        line-height: 19.2px;
    }

    .flat-title-tab {
        gap: 30px;
    }

    .collection-item-v4.st-lg .collection-content .heading {
        font-size: 32px;
        line-height: 38.4px;
        margin-bottom: 14px;
    }

    .collection-item-circle.circle-line .collection-image {
        padding: 17px;
    }

    .flat-categories-bg {
        padding: 48px 30px;
    }

    .flat-categories-bg .collection-item-circle .collection-content .title {
        font-size: 20px;
        line-height: 24px;
    }

    .tf-countdown-v3 {
        padding: 8px 14px;
    }

    .tf-form-sneaker .flat-title .title {
        font-size: 28px;
        line-height: 33px;
    }

    .tf-form-sneaker .box-content {
        padding: 50px 140px;
        max-width: 750px;
    }

    .tf-form-sneaker form .tf-btn {
        border-radius: 3px;
    }

    .tf-grid-layout .collection-item-centered .tf-btn {
        padding-left: 20px;
        padding-right: 20px;
    }

    .flat-wrap-iconbox-v2 {
        padding: 83px 15px;
    }

    .tf-icon-box-v3 .title {
        font-size: 28px;
        line-height: 33.6px;
    }

    .pagination-wrap-spacing {
        margin-top: 76px;
    }

    .border-start-md-0 {
        border-left: 0 !important;
    }

    .masonry-layout-v5 {
        grid-template-columns: repeat(10, 1fr);
    }

    .masonry-layout-v5 .tf-gallery-image {
        grid-area: span 1 / span 2;
    }

    .masonry-layout-v5 .tf-gallery-image.item-1 {
        grid-area: span 2 / span 4;
    }

    .flat-thumbs-testimonial-v2 {
        padding: 85px 110px;
    }

    .tf-icon-box-v2 {
        flex-direction: row;
    }

    .tf-icon-box-v2 .icon {
        font-size: 30px;
    }

    .flat-testimonial-bg {
        padding: 85px 0px 60px;
    }

    .tf-slideshow .card-box-2 .title {
        font-size: 20px;
        line-height: 24px;
    }

    .tf-slideshow .card-box-2 .price {
        margin-bottom: 30px;
    }

    .img-text-3 .tf-content-wrap {
        padding: 20px 20px 20px 20px;
    }

    .banner-parallax {
        height: 780px;
    }

    .slider-home-decor .reverse .img-slider {
        margin-left: unset;
    }

    .slider-home-decor .reverse .content-left {
        left: 50%;
    }

    .slider-home-decor .content-left .box-content {
        width: 80%;
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .tf-compare-col {
        min-width: 300px;
    }

    .form-register-wrap {
        width: 551px;
        margin: 0 auto;
    }

    .tf-timeline-content {
        text-align: right;
        width: 50%;
    }

    .tf-timeline-image {
        width: 50%;
    }

    .tf-timeline-inner {
        gap: 100px;
        flex-direction: row;
    }

    .tf-timeline-inner::after,
    .tf-timeline-inner::before {
        position: absolute;
        content: "";
        width: 20px;
        background-color: var(--line);
        height: 1px;
        z-index: 3;
        top: calc(50% + 5px);
    }

    .tf-timeline-inner::before {
        left: calc(50% + 20px);
        transform: translate(-50%);
    }

    .tf-timeline-inner::after {
        right: calc(50% + 20px);
        transform: translate(50%);
    }

    .tf-timeline-inner.tf-timeline-content-end {
        flex-direction: row-reverse;
    }

    .tf-timeline-inner.tf-timeline-content-end .tf-timeline-content {
        text-align: left;
    }

    .tf-timeline-item::before {
        position: absolute;
        content: "";
        top: 50%;
        left: 50%;
        transform: translate(-50%);
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: var(--primary);
    }

    .tf-store-item {
        padding: 18px 17px 20px;
    }

    .tf-ourstore-content {
        padding-left: 40px;
        padding-right: 20px;
    }

    .tf-accordion-wrap > .content {
        width: 58.33333333%;
    }

    .tf-accordion-wrap .box {
        width: 41.66666667%;
    }

    .tf-content-left.has-mt {
        margin-top: 40px;
    }

    .tf-filter-item {
        padding: 25px 30px;
    }

    .tf-brands-source-linklist {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }

    .tf-brands-source-linklist.style-row .tf-content-brands {
        grid-template-columns: repeat(4, 1fr);
    }

    .tf-brands-source-linklist.style-row .tf-item-inner {
        gap: 20px;
    }

    .tf-brands-filter {
        padding-bottom: 47px;
    }

    .tf-brands-filter .tf-tab-link_all {
        padding: 8px 20px;
    }

    .tf-breadcrumb-wrap {
        padding: 25.5px 0;
    }

    .tf-breadcrumb-wrap .tf-breadcrumb-list .icon {
        margin: 0 10px;
    }

    .tf-slideshow .banner-wrapper .description {
        font-size: 20px;
        line-height: 24px;
    }

    .logo-header img {
        width: 100%;
        max-height: 70px;
    }

    .canvas-mb {
        width: 100% !important;
        max-width: 367px;
    }

    .container-full,
    .container {
        padding-left: 30px;
        padding-right: 30px;
    }

    .sw-wrapper-right {
        margin-right: -30px;
    }

    .slider-giftcard .wrap-slider {
        height: 700px;
    }

    .slider-collection .collection-item .collection-title {
        line-height: 54px;
        height: 54px;
        min-width: 100px;
        font-size: 20px;
    }

    .slider-collection .collection-item .collection-title .icon {
        display: block;
    }

    .slider-collection .collection-item .collection-image {
        border-radius: 3px;
    }

    .tf-hero-image-liquid .box-content .tf-btn {
        margin-top: 36px;
    }

    .hover-sw-nav .sw-dots {
        margin-top: 30px;
    }

    .slideshow-effect-zoom {
        padding-top: 62px;
        padding-bottom: 85px;
    }

    .card-product .card-product-info {
        padding-top: 15px;
        gap: 10px;
    }

    .card-product.list-layout {
        gap: 30px;
    }

    .card-product.list-layout:not(:last-child) {
        padding-bottom: 30px;
        margin-bottom: 30px;
    }

    .card-product.list-layout .countdown-box {
        bottom: 56px;
    }

    .card-product .countdown-box {
        max-width: 212px;
        bottom: 86px;
    }

    .card-product .card-product-wrapper .list-product-btn {
        bottom: 40px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-2 {
        bottom: 10px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-3 {
        bottom: 43px;
    }

    .card-product .card-product-wrapper .column-left {
        left: 10px;
        top: 10px;
    }

    .card-product .card-product-wrapper .column-right {
        right: 10px;
        top: 10px;
    }

    .card-product .card-product-wrapper .sold-out {
        height: 83px;
        width: 83px;
        padding: 0 10px;
    }

    .card-product .card-product-wrapper .sold-out span {
        font-size: 14px;
        line-height: 22px;
    }

    .card-product .card-product-wrapper .on-sale-wrap {
        top: 10px;
        left: 10px;
        right: 10px;
    }

    .card-product .card-product-wrapper .on-sale-wrap .on-sale-item {
        padding: 0 10px;
        min-width: 59px;
        font-size: 14px;
        line-height: 29px;
    }

    .card-product.style-7 .card-product-info {
        padding-top: 10px;
    }

    .card-product.style-8 {
        padding: 10px 10px 30px;
    }

    .wrapper-shop .card-product {
        margin-bottom: 30px;
    }

    .tf-btn-filter {
        padding: 8px 12px;
    }

    .canvas-filter {
        max-width: 410px;
    }

    .canvas-filter .canvas-body {
        padding: 32px 36px;
    }

    .widget-facet .facet-title {
        font-size: 20px;
        line-height: 30px;
    }

    .featured-product-item .card-product-info {
        gap: 10px;
    }

    .canvas-sidebar .canvas-header {
        padding: 0 20px;
    }

    .canvas-sidebar .canvas-body {
        padding: 30px;
    }

    .canvas-sidebar-blog .canvas-header .title {
        font-size: 20px;
        line-height: 24px;
    }

    .canvas-sidebar-blog .canvas-body {
        padding: 20px;
    }

    .tf-pagination-wrap {
        padding-top: 60px;
    }

    .tf-grid-layout.md-col-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .tf-grid-layout.md-col-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .collection-item .collection-title {
        line-height: 42px;
        height: 42px;
        padding: 0 20px;
    }

    .collection-item .collection-content {
        bottom: 30px;
    }

    .collection-item.style-2 .collection-content {
        bottom: 25px;
    }

    .collection-item.style-left .collection-content {
        bottom: 30px;
        left: 15px;
    }

    .collection-item-v2 .collection-content .heading {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 10px;
    }

    .collection-item-v2 .collection-content .subheading {
        margin-bottom: 10px;
    }

    .collection-item-v3 .collection-image .box-icon {
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
    }

    .collection-item-v3 .collection-content {
        margin-top: 20px;
    }

    .collection-item-v3 .collection-content .title {
        font-size: 18px;
        line-height: 21.6px;
    }

    .collection-item-v4 .collection-content .tf-btn {
        margin-top: 22px;
    }

    .collection-item-v4 .collection-content .subheading {
        margin-bottom: 8px;
    }

    .collection-item-v4 .collection-content .heading {
        font-size: 20px;
        line-height: 24px;
    }

    .collection-item-v5 .collection-content {
        left: 25px;
        bottom: 25px;
    }

    .collection-item-v5 .collection-content .collection-title {
        padding: 12px 14px;
        font-size: 18px;
        line-height: 21.6px;
    }

    .collection-item-circle .collection-content {
        margin-top: 30px;
    }

    .collection-item-circle .collection-content .title {
        font-size: 18px;
        line-height: 21.6px;
    }

    .tf-grid-layout {
        gap: 30px;
    }

    .tf-marquee {
        padding-top: 26px;
        padding-bottom: 26px;
    }

    .tf-marquee .wrap-marquee {
        -webkit-animation: slide-har 10s linear infinite;
        animation: slide-har 10s linear infinite;
    }

    .tf-marquee .marquee-item .text {
        font-size: 24px;
        line-height: 28.8px;
    }

    .tf-marquee.marquee-sm {
        padding-top: 17px;
        padding-bottom: 17px;
    }

    .tf-marquee.marquee-sm .marquee-item {
        gap: 25px;
    }

    .tf-marquee.marquee-lg p {
        font-size: 60px;
        line-height: 76px;
    }

    .flat-title {
        margin-bottom: 50px;
    }

    .flat-title .title {
        font-size: 32px;
        line-height: 38.4px;
    }

    .flat-title .sub-title {
        font-size: 16px;
        line-height: 22px;
    }

    .flat-title.title-upper .title {
        font-size: 28px;
        line-height: 33.6px;
    }

    .tf-loading-default.style-2 {
        height: 46px;
        min-width: 130px;
    }

    .flat-seller .grid-layout {
        row-gap: 50px;
    }

    .testimonial-item .text {
        font-size: 18px;
        line-height: 28.8px;
    }

    .testimonial-item.style-column {
        padding: 44px 36px 24px;
    }

    .testimonial-item.style-column .text {
        margin-bottom: 26px;
    }

    .testimonial-item.style-column .author {
        margin-bottom: 34px;
    }

    .testimonial-item.style-column .rating {
        margin-bottom: 16px;
    }

    .testimonial-item.style-column.style-2 {
        padding: 36px 32px;
    }

    .testimonial-item.style-column.style-2 .text {
        margin-bottom: 20px;
    }

    .flat-iconbox .wrap-iconbox {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .flat-iconbox-v2 .wrap-iconbox {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .flat-iconbox-v2 .wrap-iconbox .swiper-slide:last-child {
        grid-column: 1/3;
    }

    .flat-iconbox-v2 .tf-icon-box {
        max-width: 450px;
        margin: auto;
        width: 100%;
    }

    .flat-iconbox-v2 .tf-icon-box .icon {
        margin-bottom: 28px;
    }

    .flat-iconbox-v2.style-2 .tf-icon-box .icon i {
        font-size: 40px;
    }

    .flat-iconbox-v3 .wrap-iconbox {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .flat-iconbox-v3 .wrap-iconbox .swiper-slide:last-child {
        grid-column: 1/3;
    }

    .flat-iconbox-v3 .tf-icon-box .icon {
        width: 60px;
        height: 60px;
    }

    .flat-iconbox-v3 .tf-icon-box .icon.w-74 {
        width: 74px;
        height: 74px;
    }

    .flat-iconbox-v3 .tf-icon-box .content .title {
        font-size: 28px;
        line-height: 34px;
    }

    .wrap-iconbox.lg .tf-icon-box-v3,
    .wrap-iconbox.lg .tf-icon-box {
        max-width: 450px;
        margin: auto;
        width: 100%;
    }

    .wrap-mobile .grid-mobile-1 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .flat-price .wrap-price {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 30px;
    }

    .masonry-layout {
        display: grid;
        grid-template-areas: "item1 item2 item4" "item1 item3 item4";
        gap: 15px;
        grid-template-columns: 1fr 1fr 1fr;
    }

    .masonry-layout .item-1 {
        grid-area: item1;
    }

    .masonry-layout .item-2 {
        grid-area: item2;
    }

    .masonry-layout .item-3 {
        grid-area: item3;
    }

    .masonry-layout .item-4 {
        grid-area: item4;
    }

    .masonry-layout .collection-item .img-style {
        height: 100%;
    }

    .masonry-layout.style-2 {
        grid-template-areas: "item1 item2" "item3 item4";
        grid-template-columns: 1fr 1fr;
    }

    .masonry-layout.style-2 .collection-image {
        height: 390px;
    }

    .tf-img-with-text .tf-image-wrap {
        order: 1;
    }

    .tf-content-wrap {
        padding: 20px;
    }

    .tf-content-wrap .description {
        margin-top: 18px;
        font-size: 16px;
        line-height: 25.6px;
    }

    .tf-content-wrap .tf-btn {
        margin-top: 24px;
    }

    .tf-content-wrap .heading {
        font-size: 40px;
        line-height: 48px;
    }

    .tf-content-wrap .count-down .tf-countdown-v2 .countdown__timer {
        justify-content: flex-start;
    }

    .tf-content-wrap-v2 .description {
        font-size: 20px;
        line-height: 24px;
    }

    .widget-tab-2 {
        gap: 20px;
    }

    .widget-card-store .description {
        margin-bottom: 20px;
    }

    .widget-card-store .description p:not(:last-child) {
        margin-bottom: 22px;
    }

    .widget-card-store .store-item-info {
        padding-left: 32px;
    }

    .tf-countdown-v2 {
        justify-content: flex-end;
    }

    .tf-countdown-v2 .countdown__item {
        min-height: 60px;
        min-width: 70px;
    }

    .tf-countdown-v2 .countdown__label {
        font-size: 16px;
        line-height: 25.6px;
    }

    .tf-countdown-v2 .countdown__value {
        font-size: 26px;
        line-height: 31.2px;
    }

    .tf-countdown-v2.justify-content-end .countdown__timer {
        justify-content: end;
    }

    .flat-wrap-countdown .tf-content-wrap-v2 {
        text-align: left;
    }

    .flat-testimonial-v2 {
        padding-top: 110px;
        padding-bottom: 110px;
    }

    .testimonial-item.lg .icon {
        font-size: 34px;
    }

    .flat-lookbook-v2 {
        flex-direction: row;
        gap: 30px;
    }

    .flat-lookbook-v2 .col-left,
    .flat-lookbook-v2 .col-right {
        width: 50%;
    }

    .flat-lookbook-v2 .col-right {
        padding: 0 60px;
    }

    .flat-lookbook-v2 .col-right .nav-next-slider {
        left: -50px;
    }

    .flat-lookbook-v2 .col-right .nav-prev-slider {
        right: -50px;
    }

    .flat-title-lookbook {
        margin-bottom: 30px;
    }

    .flat-title-lookbook .heading {
        font-size: 40px;
        line-height: 48px;
    }

    .flat-location .banner-map {
        height: 450px;
    }

    .flat-location .content {
        max-width: 350px;
        width: 100%;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 7%;
        text-align: start;
    }

    .flat-location .content .heading {
        font-size: 28px;
        line-height: 33.6px;
    }

    .flat-location .content .tf-btn {
        margin-top: 30px;
    }

    .flat-location .content .subtext {
        margin-top: 22px;
    }

    .tf-grid-layout-v2 {
        gap: 30px;
        grid-template-columns: repeat(12, minmax(0, 1fr));
    }

    .tf-grid-layout-v2 .widget-tab-4 {
        grid-column: span 4;
    }

    .tf-grid-layout-v2 .tab-content {
        grid-column: span 8;
    }

    .widget-tab-4 {
        flex-direction: column;
        padding-top: 27px;
        padding-bottom: 27px;
        gap: 0;
    }

    .widget-tab-4 .nav-tab-item:not(:last-child) .nav-tab-link {
        border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link {
        justify-content: space-between;
        padding: 10px 0px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link .text {
        font-size: 22px;
        line-height: 26.4px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link .count {
        font-size: 14px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link.active {
        border-color: var(--main);
    }

    .collection-item-v4.style-2 .collection-content {
        bottom: 7%;
        left: 8%;
        right: 8%;
        top: unset;
        transform: unset;
        text-align: left;
    }

    .collection-item-v4.style-2 .collection-content .heading {
        font-size: 32px;
        line-height: 38.4px;
    }

    .collection-item-v4.style-2 .collection-content .subtext {
        margin-top: 14px;
    }

    .canvas-mb .mb-canvas-content {
        min-width: 367px;
    }

    .tf-marquee.marquee-lg {
        padding: 85px;
    }

    .sw-pagination-wrapper .box-sw-navigation {
        position: absolute;
        right: 0;
        top: 0;
    }

    .sw-pagination-wrapper .box-sw-navigation .sw-dots {
        margin-top: 10px;
    }

    .tf-img-with-text.style-3 {
        border-top-right-radius: 18px;
        border-bottom-right-radius: 18px;
        overflow: hidden;
    }

    .widget-card-store.type-1 .store-item-info {
        padding: 30px;
    }

    .widget-card-store.type-1 .store-heading {
        font-size: 40px;
        line-height: 48px;
    }

    .blog-detail .blog-detail-main .tf-article-navigation .icon {
        width: 30px;
        height: 30px;
    }

    .blog-detail .blog-detail-main .tf-article-navigation .item {
        gap: 30px;
    }

    .blog-detail-main .tf-article-navigation h6 {
        font-size: 20px;
        line-height: 30px;
    }

    .box-nav-pagination {
        padding: 12px;
        min-width: 94px;
    }

    .box-nav-pagination .dots-default {
        gap: 8px;
    }

    .dots-default .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
    }

    .masonry-layout-v3 {
        grid-template-areas: "item1 item2" "item3 item4";
    }

    .masonry-layout-v3 .item-1 {
        grid-area: item1;
    }

    .masonry-layout-v3 .item-2 {
        grid-area: item2;
    }

    .masonry-layout-v3 .item-3 {
        grid-area: item3;
    }

    .masonry-layout-v3 .item-4 {
        grid-area: item4;
    }

    .banner-collection-men-wrap .img-wrap {
        height: 780px;
    }

    .banner-collection-men-wrap .card-box {
        padding: 40px 20px;
    }

    .banner-collection-men-wrap .card-box .heading {
        font-size: 40px;
        line-height: 48px;
    }

    .banner-collection-men-wrap .card-box .text {
        margin-top: 25px;
    }

    .banner-collection-men-wrap .card-box .tf-btn {
        margin-top: 35px;
    }

    .card-product.lg .tf-countdown-v2 .countdown__item .countdown__value {
        font-size: 20px;
    }

    .count-down .tf-countdown-v2 .countdown__item {
        min-width: 60px;
        min-height: 70px;
        padding: 6px 10px;
    }

    .count-down .tf-countdown-v2 .countdown__item .countdown__value {
        font-size: 30px;
        line-height: 36px;
    }

    .tf-marquee.style-2 .marquee-item {
        gap: 44.2px;
    }

    .tf-marquee.marquee-md {
        padding-top: 70px;
        padding-bottom: 80px;
    }

    .tf-flash-sale {
        padding: 95px 20px 20px;
    }

    .tf-flash-sale .heading-flash-sale {
        font-size: 32px;
        line-height: 38.4px;
    }

    .masonry-layout-v4 {
        grid-template-areas: "item1 item2" "item1 item3";
        gap: 30px;
    }

    .masonry-layout-v4 .item-1 {
        grid-area: item1;
    }

    .masonry-layout-v4 .item-2 {
        grid-area: item2;
    }

    .masonry-layout-v4 .item-3 {
        grid-area: item3;
    }

    .masonry-layout-v6 {
        grid-template-areas: "item1 item1 item2 item3" "item1 item1 item4 item4";
        gap: 30px;
    }

    .grid-3-layout-md {
        gap: 30px;
        grid-template-columns: repeat(3, 1fr);
    }

    .collection-line-upper .collection-content .collection-title {
        padding: 12px 24px;
    }

    .collection-line-upper .collection-content .heading {
        margin-bottom: 17px;
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 17px;
    }

    .tf-marquee.type-md .marquee-item .text {
        font-size: 20px;
        line-height: 24px;
    }

    .flat-wrap-tes-text {
        margin-bottom: 30px;
    }

    .testimonial-ver-text {
        gap: 10px;
    }

    .testimonial-ver-text .heading {
        margin-bottom: 30px;
    }

    .testimonial-ver-text .content {
        font-size: 20px;
        line-height: 32px;
    }

    .tf-thumb-tes .thumb-tes-item {
        padding-left: 20px;
        padding-right: 20px;
    }

    .slideshow-tee .wrap-slider .box-content .heading {
        font-size: 44px;
        line-height: 52.8px;
    }

    .slideshow-tee.tf-slideshow .wrap-slider {
        height: 840px;
    }

    .flat-testimonial-bg-v2 {
        padding: 90px 0px;
    }

    .flat-testimonial-bg-v2 .wrap-content-left .rating {
        margin-bottom: 14px;
    }

    .flat-testimonial-bg-v2 .wrap-content-right {
        margin-top: 30px;
    }
}

@media (min-width: 992px) {
    .row-brand {
        gap: 30px;
    }

    .wd-form-order {
        padding: 30px;
    }

    .list-product-btn .box-icon {
        width: 40px;
        height: 40px;
    }

    .card-product .countdown-box {
        bottom: 90px;
    }

    .card-product:where(.style-2, .style-3) .countdown-box {
        bottom: 100px;
    }

    .card-product.list-layout .countdown-box {
        bottom: 60px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-3 {
        bottom: 50px;
    }

    .card-product .btn-quick-add {
        height: 40px;
        line-height: 40px;
    }

    .card-product.style-4 .size-list {
        bottom: 40px;
    }

    .card-product.style-height .card-product-wrapper img {
        min-height: 430px;
    }

    .card-product.style-9 .card-product-info .box-icon {
        width: 40px;
        height: 40px;
    }

    .size-list {
        height: 30px;
        gap: 15px;
    }

    .size-list.style-2 {
        height: 40px;
        gap: 6px;
    }

    .size-list.style-2 span {
        height: 30px;
        min-width: 35px;
    }

    .modal-newleter .modal-content .modal-bottom {
        padding: 39px 37px 30px;
    }

    .modal-newleter .modal-content .modal-bottom h6 {
        padding: 0px 40px;
    }

    .collection-item-v2.type-small .collection-content {
        inset: 27px;
    }

    .form-checkout .box:not(:last-child) {
        margin-bottom: 30px;
    }

    .form-checkout .grid-2 {
        gap: 30px;
    }

    .slideshow-tee .wrap-slider .box-content p {
        font-size: 44px;
        line-height: 52.8px;
    }
}

@media (min-width: 1025px) {
    .collection-item-v4.st-lg .collection-content .heading {
        font-size: 42px;
        line-height: 50.4px;
        margin-bottom: 18px;
    }

    .testimonial-ver-text .heading {
        margin-bottom: 40px;
    }

    .widget-card-store.type-2 .store-heading {
        font-size: 32px;
        line-height: 38.4px;
    }

    .tf-sticky-btn-atc .tf-sticky-atc-img {
        width: 80px;
        height: 80px;
    }

    .flat-title-lookbook .heading {
        font-size: 52px;
        line-height: 62.4px;
    }

    .flat-location .content {
        padding: 40px;
        padding-bottom: 48px;
    }

    .collection-item-v4.style-2 .collection-content .heading {
        font-size: 42px;
        line-height: 50.4px;
    }

    .collection-item-v4.style-2 .collection-content .heading.fs-52 {
        font-size: 52px;
        line-height: 62px;
    }

    .banner-collection-men-wrap .card-box .heading {
        font-size: 52px;
        line-height: 62.4px;
    }

    .tf-flash-sale .heading-flash-sale {
        font-size: 42px;
        line-height: 50.4px;
    }

    .tf-marquee.marquee-md p {
        font-size: 68px;
        line-height: 81.6px;
    }

    .slideshow-tee .wrap-slider .box-content .heading {
        font-size: 80px;
        line-height: 96px;
    }

    .slideshow-tee .wrap-slider .box-content p {
        font-size: 80px;
        line-height: 96px;
    }
}

@media (min-width: 1150px) {
    .grid-2-lg {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .img-text-3.img-text-3-style-2 .tf-image {
        padding: 50px 0px;
    }

    .img-text-3.img-text-3-style-2 .subheading {
        font-size: 18px;
    }

    .img-text-3.img-text-3-style-2 .heading {
        font-size: 46px;
        line-height: 55.2px;
    }

    .slider-baby .wrap-slider .subheading {
        font-size: 30px;
        line-height: 36px;
    }

    .slider-baby .wrap-slider .heading {
        margin-bottom: 38px;
    }

    .tf-grid-layout.lg-col-4 {
        grid-template-columns: repeat(4, 1fr);
    }

    .tf-grid-layout.lg-col-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    #header .nav-icon .cart-lg {
        padding-left: 15px;
        border-left: 1px solid var(--line);
    }

    .collection-item-v4.st-lg .collection-content .subtext {
        font-size: 20px;
        line-height: 24px;
    }

    .collection-item-v6 .collection-content {
        padding: 24px 20px 26px;
    }

    .collection-item-v6 .collection-content .heading {
        font-size: 28px;
        line-height: 33.6px;
    }

    .collection-item-v6 .collection-content .subheading {
        font-size: 20px;
        line-height: 24px;
    }

    .collection-item-circle.has-bg {
        padding: 20px 0px;
    }

    .flat-categories-bg {
        padding: 56px 50px;
    }

    .tf-countdown-v3 {
        padding: 10px 16px;
    }

    .tf-form-sneaker .box-content {
        max-width: 950px;
        padding: 81px 182px 89px 231px;
        z-index: 10;
    }

    .tf-grid-layout .collection-item-centered .tf-btn {
        padding-left: 30px;
        padding-right: 30px;
    }

    .flat-wrap-iconbox-v2 {
        padding: 98px 15px;
    }

    .tf-icon-box-v3 .title {
        font-size: 42px;
        line-height: 50.4px;
    }

    .tf-icon-box-v3 .desc {
        font-size: 18px;
        line-height: 28.8px;
    }

    .flat-testimonial-bg-v2 {
        padding: 106px 0px;
    }

    .flat-testimonial-bg-v2 .wrap-content-right {
        margin-top: 0px;
    }

    .testimonial-item.style-column.style-2 .text {
        margin-bottom: 70px;
    }

    .slideshow-tee .wrap-slider .box-content .heading {
        font-size: 150px;
        line-height: 180px;
    }

    .lg-mt-50 {
        margin-top: 50px;
    }

    .flat-wrap-tes-text {
        margin-bottom: 88px;
    }

    .testimonial-ver-text .content {
        font-size: 28px;
        line-height: 44.8px;
    }

    .tf-thumb-tes .thumb-tes-item {
        padding-left: 42px;
        padding-right: 42px;
    }

    .tf-thumb-tes .thumb-tes-item img {
        max-width: 157px;
    }

    .testimonial-item.style-row {
        padding-right: 40px;
    }

    .testimonial-item.style-row .rating {
        margin-bottom: 18px;
    }

    .testimonial-item.style-row .text {
        margin-bottom: 38px;
    }

    .lookbook-sw .navigation-sw-dot {
        width: 24px;
        height: 24px;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -ms-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .lookbook-sw .navigation-sw-dot span {
        width: 8px;
        height: 8px;
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -ms-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .lookbook-sw .navigation-sw-dot:hover {
        width: 32px;
        height: 32px;
    }

    .lookbook-sw .navigation-sw-dot:hover span {
        width: 12px;
        height: 12px;
    }

    .flat-activewear-lookbook .inner-sw-lookbook .tf-btn {
        padding: 0 94px;
    }

    .flat-thumbs-testimonial-v2 .box-left .text {
        font-size: 28px;
        line-height: 44.8px;
    }

    .col-xl-8 .flat-accordion .flat-toggle .toggle-title {
        font-size: 18px;
        line-height: 21.6px;
    }

    .widget-card-store.type-2 .store-heading {
        font-size: 42px;
        line-height: 50.4px;
    }

    .flat-testimonial-bg {
        padding: 100px 0px 68px;
    }

    .img-text-3 .tf-content-wrap {
        padding: 20px 20px 20px 90px;
    }

    .sw-pagination-wrapper .box-sw-navigation .sw-dots {
        margin-top: 15px;
    }

    .collection-item-circle .tf-shopall-icon .icon {
        -webkit-transition: all 0.3s ease;
        -moz-transition: all 0.3s ease;
        -ms-transition: all 0.3s ease;
        -o-transition: all 0.3s ease;
        transition: all 0.3s ease;
    }

    .collection-item-circle .tf-shopall-icon:hover {
        border-color: var(--primary);
    }

    .collection-item-circle .tf-shopall-icon:hover .icon {
        color: var(--primary);
    }

    .tf-compare-field,
    .tf-compare-value {
        padding: 16px 34px;
    }

    .tf-compare-item {
        padding: 16px 34px;
    }

    .tf-compare-item .tf-compare-group-btns a span {
        display: block;
    }

    .tf-compare-col {
        min-width: 360px;
    }

    .tf-login-wrap .tf-login-content,
    .tf-login-wrap .tf-login-form {
        width: 551px;
        margin: 0 auto;
    }

    .tf-login-wrap form button {
        max-width: 270px;
    }

    .tf-timeline-inner {
        gap: 190px;
        padding-top: 90px;
        padding-bottom: 90px;
    }

    .tf-timeline-inner::before,
    .tf-timeline-inner::after {
        width: 40px;
    }

    .tf-timeline-inner::before {
        left: calc(50% + 35px);
    }

    .tf-timeline-inner::after {
        right: calc(50% + 35px);
    }

    .tf-store-item {
        padding: 28px 27px 30px;
    }

    .tf-ourstore-content {
        padding-left: 93px;
    }

    .tf-ourstore-content.pl-124 {
        padding-left: 124px;
    }

    .tf-accordion-wrap > .content {
        width: 58.33333333%;
    }

    .tf-accordion-wrap .box {
        width: 33.33%;
    }

    .form-contact.mw-705 {
        max-width: 705px;
    }

    .tf-content-left {
        margin-left: 122px;
    }

    .tf-content-left.has-mt {
        margin-top: 72px;
    }

    .tf-content-right {
        margin-right: 122px;
    }

    .tf-brands-source-linklist {
        grid-template-columns: repeat(4, 1fr);
    }

    .tf-brands-source-linklist.style-row .tf-content-brands {
        grid-template-columns: repeat(6, 1fr);
    }

    .tf-pickup-availability-list {
        gap: 42px;
    }

    .tf-breadcrumb-wrap {
        padding: 30px 0;
    }

    .tf-categories-wrap {
        justify-content: center;
    }

    .tf-categories-wrap .tf-categories-container {
        overflow-x: auto;
    }

    .animate-hover-btn:hover:after {
        animation: shine 0.75s cubic-bezier(0.01, 0.56, 1, 1);
    }

    .lg-px_40 {
        padding-left: 40px;
        padding-right: 40px;
    }

    .sw-wrapper-right {
        margin-right: -40px;
    }

    .tf-md-hidden {
        display: block;
    }

    .tf-lg-hidden {
        display: none;
    }

    .header-default .wrapper-header {
        min-height: 94px;
    }

    .header-style-2 .wrapper-header {
        min-height: 68px;
    }

    .header-style-3 .wrapper-header {
        min-height: 86px;
    }

    .header-style-4 .wrapper-header {
        min-height: 103px;
    }

    .header-absolute {
        margin-bottom: -94px;
    }

    .tf-slideshow .banner-wrapper .tf-btn {
        margin-top: 40px;
    }

    .container {
        padding-left: 50px;
        padding-right: 50px;
    }

    .container-full {
        padding-left: 40px;
        padding-right: 40px;
    }

    .slider-collection .collection-item .collection-title {
        line-height: 64px;
        height: 64px;
        min-width: 297px;
    }

    .card-product.type-line-padding {
        padding: 40px 30px;
    }

    .card-product .card-product-info {
        padding-top: 20px;
        gap: 12px;
    }

    .card-product .card-product-info .price-primary {
        font-size: 20px;
        line-height: 20px;
        color: var(--red_1);
        font-weight: 600;
    }

    .card-product.list-layout .countdown-box,
    .card-product .countdown-box {
        bottom: 20px;
        padding: 10px;
        max-height: 42px;
    }

    .card-product .size-list {
        height: 33px;
    }

    .card-product .btn-quick-add {
        transform: translateY(20px);
        opacity: 0;
        visibility: hidden;
        height: 42px;
        line-height: 42px;
    }

    .card-product .btn-quick-add:hover {
        background-color: var(--white);
        color: var(--main);
    }

    .card-product:not(.list-layout) .box-icon {
        transform: translateY(20px);
        opacity: 0;
        visibility: hidden;
    }

    .card-product:not(.list-layout) .box-icon:nth-child(1) {
        transition: transform 0.4s ease 0s, opacity 0.4s ease 0s;
    }

    .card-product:not(.list-layout) .box-icon:nth-child(2) {
        transition: transform 0.4s ease 0.2s, opacity 0.4s ease 0s;
    }

    .card-product:not(.list-layout) .box-icon:nth-child(3) {
        transition: transform 0.4s ease 0.3s, opacity 0.4s ease 0s;
    }

    .card-product:not(.list-layout) .box-icon:nth-child(4) {
        transition: transform 0.4s ease 0.4s, opacity 0.4s ease 0s;
    }

    .card-product:not(.list-layout) .size-list {
        opacity: 0;
        visibility: hidden;
        transform: translateY(100%);
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-2 {
        bottom: 20px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-3 {
        bottom: 52px;
    }

    .card-product .card-product-wrapper .column-left .box-icon {
        transform: translate(-20px);
    }

    .card-product .card-product-wrapper .column-right .box-icon {
        transform: translate(20px);
    }

    .card-product .card-product-wrapper .sold-out {
        height: 93px;
        width: 93px;
    }

    .card-product .card-product-wrapper .on-sale-wrap {
        top: 15px;
        left: 15px;
        right: 15px;
    }

    .card-product.style-4 .column-right .box-icon {
        transform: translateY(20px);
    }

    .card-product.style-4 .size-list {
        bottom: 42px;
    }

    .card-product.style-6 .list-product-btn {
        gap: 10px;
    }

    .card-product.style-6 .card-product-info {
        margin-bottom: -52px;
        background-color: transparent;
    }

    .card-product.style-7 .btn-quick-add {
        transform: translateY(-100%);
    }

    .card-product.style-7 .card-product-info {
        padding-top: 15px;
        transform: translateY(-52px);
    }

    .card-product.style-7 .card-product-info::before {
        content: "";
        position: absolute;
        z-index: -1;
        width: calc(100% + 16px);
        height: calc(100% + 9px);
        left: -8px;
        right: -8px;
        pointer-events: none;
        background-color: var(--white);
        transition: 0.4s ease 0s;
        opacity: 0;
        visibility: hidden;
        top: 0;
        bottom: -8px;
        border-bottom-left-radius: 3px;
        border-bottom-right-radius: 3px;
        box-shadow: rgba(0, 0, 0, 0.08) 0px 6px 9px 0px;
    }

    .card-product.style-7:hover .card-product-info,
    .card-product.style-7:hover .card-product-info::before {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
        margin: 0;
        pointer-events: auto;
    }

    .card-product.style-8 .card-product-wrapper .on-sale-wrap {
        top: 10px;
        left: 10px;
        right: 10px;
    }

    .card-product.style-9 .card-product-info .box-icon {
        width: 42px;
        height: 42px;
    }

    .card-product.style-9 .card-product-info .title {
        font-weight: 600;
    }

    .card-product.style-9 .card-product-info .price {
        font-size: 20px;
        line-height: 24px;
    }

    .card-product:hover .countdown-box {
        transform: translateY(100%);
        opacity: 0;
        visibility: hidden;
    }

    .card-product:hover .btn-quick-add,
    .card-product:hover .box-icon,
    .card-product:hover .size-list {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .card-product:hover .column-right .box-icon,
    .card-product:hover .column-left .box-icon {
        transform: translate(0px);
    }

    .card-product.lg .card-product-info .title {
        font-size: 20px;
        line-height: 24px;
    }

    .card-product.lg .card-product-info .price {
        font-size: 20px;
        line-height: 24px;
    }

    .card-product.lg .card-product-info .price .old-price {
        opacity: 0.5;
    }

    .card-product.lg .tf-countdown-v2 .countdown__item .countdown__value {
        font-size: 26px;
    }

    .list-product-btn {
        gap: 6px;
    }

    .list-product-btn .box-icon {
        width: 42px;
        height: 42px;
    }

    .list-product-btn.column-left {
        gap: 6px;
    }

    .list-product-btn.column-right {
        gap: 6px;
    }

    .size-list.style-2 {
        height: 42px;
        gap: 9px;
    }

    .tf-shop-control {
        margin-bottom: 28px;
    }

    .tf-btn-filter {
        padding: 10px 12px;
    }

    .tf-btn-filter:hover {
        border-color: var(--main);
    }

    .tf-dropdown-sort {
        padding: 10px 18px;
        min-width: 190px;
    }

    .tf-row-flex .tf-shop-sidebar {
        width: calc(25% - 15px);
    }

    .tf-row-flex .tf-shop-content {
        width: calc(75% - 15px);
    }

    .featured-product-item .card-product-info {
        gap: 12px;
    }

    .tf-grid-layout.lg-col-3 {
        grid-template-columns: repeat(3, 1fr);
    }

    .tf-grid-layout.lg-col-2 {
        grid-template-columns: repeat(2, 1fr);
    }

    .collection-item .collection-title {
        line-height: 46px;
        height: 46px;
        padding: 0 30px;
        font-size: 18px;
    }

    .collection-item .collection-title.px-16 {
        padding: 0 16px;
    }

    .collection-item .collection-content {
        bottom: 36px;
    }

    .collection-item.large .collection-content {
        bottom: 46px;
    }

    .collection-item-v2 .collection-content {
        inset: 40px;
    }

    .collection-item-v2 .collection-content .heading {
        font-size: 28px;
        line-height: 33.6px;
        margin-bottom: 13px;
    }

    .collection-item-v2 .collection-content .subheading {
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 13px;
    }

    .flat-banner-cls-kid .collection-item-v2 .collection-content {
        inset: 30px;
    }

    .collection-item-v3 .collection-image .box-icon {
        width: 45px;
        height: 45px;
    }

    .collection-item-v4 .collection-content .tf-btn {
        margin-top: 30px;
    }

    .collection-item-v4 .collection-content .subheading {
        margin-bottom: 11px;
    }

    .collection-item-v4 .collection-content .heading {
        font-size: 28px;
        line-height: 33.6px;
    }

    .collection-item-v5 .collection-content .collection-title {
        padding: 14px 16px;
        gap: 21px;
        flex-direction: column-reverse;
        align-items: flex-end;
    }

    .collection-item-circle .collection-content {
        margin-top: 45px;
    }

    .collection-line-upper .collection-content .heading {
        margin-bottom: 22px;
        font-size: 28px;
        line-height: 33.6px;
    }

    .tf-marquee {
        padding-top: 31px;
        padding-bottom: 31px;
    }

    .tf-marquee .wrap-marquee {
        -webkit-animation: slide-har 15s linear infinite;
        animation: slide-har 15s linear infinite;
    }

    .tf-marquee.marquee-sm {
        padding-top: 20px;
        padding-bottom: 20px;
    }

    .tf-marquee.marquee-sm .marquee-item {
        padding-left: 15px;
        padding-right: 15px;
        gap: 30px;
    }

    .tf-marquee.marquee-lg p {
        font-size: 80px;
        line-height: 96px;
    }

    .tf-marquee.marquee-xl .marquee-item {
        gap: 70px;
        padding: 0 35px;
    }

    .tf-marquee.marquee-xl .marquee-item .text {
        font-size: 52px;
        line-height: 62.4px;
    }

    .blog-sidebar-main,
    .blog-list-main {
        display: flex;
        gap: 30px;
    }

    .blog-sidebar-main > .list-blog,
    .blog-list-main > .list-blog {
        width: calc(100% - 15px);
    }

    .blog-sidebar-main > .tf-section-sidebar,
    .blog-list-main > .tf-section-sidebar {
        width: calc(25% - 15px);
    }

    .flat-title {
        margin-bottom: 60px;
    }

    .flat-title .title {
        font-size: 42px;
        line-height: 50.4px;
    }

    .flat-title.lg .sub-title {
        font-size: 20px;
        line-height: 22px;
    }

    .flat-title-v4 {
        margin-bottom: 50px;
    }

    .flat-title-v5 {
        margin-bottom: 80px;
    }

    .flat-seller .grid-layout {
        row-gap: 80px;
    }

    .flat-iconbox .wrap-iconbox {
        grid-template-columns: repeat(4, 1fr);
    }

    .wrap-mobile .grid-mobile-1 {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }

    .flat-iconbox-v2 .wrap-iconbox {
        grid-template-columns: repeat(3, 1fr);
        gap: 0;
    }

    .flat-iconbox-v2 .wrap-iconbox .swiper-slide:last-child {
        grid-column: unset;
    }

    .flat-iconbox-v2 .wrap-iconbox .swiper-slide:not(:last-child) .tf-icon-box {
        border-right: 1px solid var(--line);
    }

    .flat-iconbox-v2.style-2 .wrap-iconbox {
        gap: 30px;
    }

    .flat-iconbox-v2 .tf-sw-mobile {
        padding-top: 36px;
        padding-bottom: 36px;
    }

    .flat-iconbox-v2 .tf-icon-box .icon {
        margin-bottom: 34px;
    }

    .flat-iconbox-v3 .wrap-iconbox {
        grid-template-columns: repeat(3, 1fr);
        gap: 30px;
    }

    .flat-iconbox-v3 .wrap-iconbox .swiper-slide:last-child {
        grid-column: unset;
    }

    .flat-iconbox-v3.lg .tf-icon-box .icon {
        width: 99px;
        height: 99px;
    }

    .flat-iconbox-v3.lg .tf-icon-box .icon i {
        font-size: 40px;
    }

    .flat-iconbox-v3.lg .wrap-iconbox {
        gap: 30px;
    }

    .masonry-layout {
        gap: 30px;
    }

    .masonry-layout.style-2 {
        grid-template-areas: "item1 item2 item4" "item1 item3 item4";
        grid-template-columns: 1fr 1fr 1fr;
    }

    .masonry-layout.style-2 .item-4 .collection-image,
    .masonry-layout.style-2 .item-1 .collection-image {
        height: 850px;
    }

    .masonry-layout.style-2 .item-2 .collection-image,
    .masonry-layout.style-2 .item-3 .collection-image {
        height: 410px;
    }

    .masonry-layout.style-2 .collection-image {
        height: 100%;
    }

    .tf-img-with-text .tf-content-wrap {
        place-self: center start;
    }

    .tf-img-with-text.style-5 .tf-content-wrap {
        padding-left: 78px;
    }

    .tf-image-wrap {
        position: relative;
    }

    .tf-image-wrap::before {
        content: "";
        position: absolute;
        top: -100%;
        left: 0;
        width: 50%;
        height: 100%;
        background: rgb(0, 0, 0);
        transition: 0.25s;
        z-index: 3;
        opacity: 0.1;
    }

    .tf-image-wrap::after {
        content: "";
        position: absolute;
        bottom: -100%;
        right: 0;
        width: 50%;
        height: 100%;
        background: rgb(0, 0, 0);
        transition: 0.25s;
        z-index: 3;
        opacity: 0.1;
    }

    .tf-image-wrap:hover::after {
        bottom: 0;
        transition-delay: 0.25s;
    }

    .tf-image-wrap:hover::before {
        top: 0;
    }

    .tf-content-wrap {
        padding-left: 0;
    }

    .tf-content-wrap .heading {
        font-size: 52px;
        line-height: 62.4px;
    }

    .tf-content-wrap .heading.fs-42 {
        font-size: 42px;
        line-height: 50px;
    }

    .tf-content-wrap .description {
        margin-top: 24px;
    }

    .tf-content-wrap .tf-btn {
        margin-top: 34px;
    }

    .widget-tab-2 {
        gap: 30px;
        margin-bottom: 57px;
    }

    .widget-tab-2 .nav-tab-item a {
        padding-bottom: 14px;
    }

    .widget-card-store .store-heading {
        margin-bottom: 31px;
    }

    .widget-card-store .description p:not(:last-child) {
        margin-bottom: 22px;
    }

    .widget-card-store .store-item-info {
        padding-left: 122px;
    }

    .widget-card-store.type-1 .store-heading {
        font-size: 52px;
        line-height: 62.4px;
    }

    .tf-countdown-v2 .countdown__item {
        padding: 0px 20px;
        min-height: 84px;
        min-width: 90px;
    }

    .tf-countdown-v2 .countdown__value {
        font-size: 32px;
        line-height: 38.4px;
    }

    .flat-bg-collection .collection-other-link {
        margin-top: 50px;
    }

    .flat-testimonial-v2 {
        padding-top: 130px;
        padding-bottom: 130px;
    }

    .testimonial-item .icon {
        margin-bottom: 40px;
    }

    .testimonial-item.lg .text {
        font-size: 20px;
        line-height: 32px;
        margin-bottom: 36px;
    }

    .testimonial-item .rating {
        margin-bottom: 26px;
    }

    .testimonial-item .divider {
        margin-bottom: 22px;
    }

    .testimonial-item.lg-2 .icon {
        margin-bottom: 22px;
    }

    .testimonial-item.lg-2 .rating {
        margin-bottom: 24px;
    }

    .testimonial-item.lg-2 .text {
        margin-bottom: 24px;
    }

    .testimonial-item.lg-2 .box-author {
        margin-bottom: 40px;
    }

    .testimonial-item.lg-3 .text {
        font-size: 28px;
        line-height: 44px;
    }

    .wrapper-thumbs-testimonial .box-right {
        padding-right: 80px;
    }

    .gallery-item .box-icon {
        opacity: 0;
        visibility: hidden;
    }

    .gallery-item:hover::before {
        opacity: 1;
        visibility: visible;
    }

    .gallery-item:hover .box-icon {
        opacity: 1;
        visibility: visible;
    }

    .tf-icon-box.style-row .icon {
        width: 74px;
        height: 74px;
    }

    .flat-lookbook-v2 .col-right {
        padding: 0 90px;
    }

    .flat-lookbook-v2 .col-right .nav-next-slider {
        left: -60px;
    }

    .flat-lookbook-v2 .col-right .nav-prev-slider {
        right: -60px;
    }

    .flat-title-lookbook {
        margin-bottom: 40px;
    }

    .flat-location .banner-map {
        height: 600px;
    }

    .flat-location .content {
        max-width: 460px;
    }

    .widget-tab-4 {
        padding: 46px 30px 58px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link {
        padding: 15px 0px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link .text {
        font-size: 26px;
        line-height: 31.2px;
    }

    .collection-item-v4.style-2 .collection-content {
        bottom: 15%;
        left: 10%;
        right: 10%;
    }

    .collection-item-v4.style-2 .collection-content .subtext {
        margin-top: 18px;
        font-size: 20px;
        line-height: 32px;
    }

    .collection-item-v4.style-2 .collection-content .tf-btn {
        margin-top: 28px;
    }

    .collection-item-v4.style-3 .collection-content {
        bottom: 7%;
        left: 7%;
        right: 7%;
    }

    .collection-item-v4.style-4 .collection-content {
        bottom: 10%;
    }

    .lg_py_30 {
        padding: 30px 0;
    }

    .lg_fs_18 {
        font-size: 18px;
    }

    .tf-cart-countdown {
        margin-bottom: 68px;
    }

    .tf-cart-countdown p {
        font-size: 20px;
        line-height: 32px;
    }

    .tf-cart-countdown .timer-count {
        font-size: 20px;
        line-height: 32px;
        min-width: 117px;
    }

    .tf-page-cart-wrap {
        display: grid;
        grid-template: repeat(2, auto) / 66% 1fr;
        column-gap: 3rem;
    }

    .tf-page-cart-wrap.layout-2 {
        grid-template: repeat(2, auto) / 60% 1fr;
    }

    .tf-cart-item .cart-info .cart-title {
        font-size: 16px;
        list-style: 22.4px;
    }

    .tf-cart-footer-inner {
        position: sticky;
        top: 100px;
        transition: top 0.3s ease;
    }

    .tf-marquee.marquee-lg {
        padding: 100px;
    }

    .collection-item-v4.lg .collection-content {
        left: 60px;
        right: 60px;
    }

    .widget-card-store.type-1 .store-item-info {
        padding: 50px;
    }

    .footer.background-black .footer-body {
        padding-top: 60px;
    }

    .blog-detail .blog-detail-main .tf-article-navigation .icon {
        width: 46px;
        height: 46px;
    }

    .flat-wrap-iconbox {
        padding: 109px 90px 132px;
    }

    .lookbook-item .tf-pin-btn::after,
    .lookbook-item .tf-pin-btn::before {
        position: absolute;
        content: "";
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        border-radius: 9999px;
    }

    .lookbook-item .tf-pin-btn::after {
        animation-delay: 0.5s;
    }

    .lookbook-item .tf-pin-btn::before {
        animation-delay: 0.9s;
    }

    .lookbook-item .tf-pin-btn:hover::after,
    .lookbook-item .tf-pin-btn:hover::before {
        animation: ripple 3s infinite;
    }

    .lookbook-item .tf-pin-btn.pin-black:hover::after,
    .lookbook-item .tf-pin-btn.pin-black:hover::before {
        animation: ripple_black 3s infinite;
    }

    .masonry-layout-v3 {
        gap: 30px;
        grid-template-areas: "item1 item2 item3" "item1 item2 item4";
    }

    .banner-collection-men-wrap .card-box {
        padding: 80px 44px;
        max-width: 700px;
    }

    .banner-collection-men-wrap .box-content {
        left: 0;
        right: 0;
    }

    .widget-tab-3.style-2 {
        gap: 45px;
    }

    .tf-marquee.style-2 .marquee-item {
        gap: 52px;
    }

    .tf-flash-sale {
        padding: 60px 30px;
    }

    .tf-flash-sale .heading-flash-sale {
        margin-left: 35px;
        padding: 4px 20px;
        gap: 15px;
    }
}

@media (min-width: 1441px) {
    #header .nav-icon .cart-lg {
        padding-left: 22px;
    }

    #header .nav-icon .cart-lg .icon {
        font-size: 26px;
    }

    .collection-item-circle.has-bg-2 {
        padding: 35px 45px;
    }

    .box-video-wrap {
        max-width: 612px;
        margin-left: auto;
    }

    .flat-testimonial-bg-v2 .wrap-content-right {
        padding-left: 50px;
    }

    .slider-home-decor .content-left .box-content {
        width: max-content;
    }

    .card-product.list-layout .countdown-box,
    .card-product .countdown-box {
        bottom: 25px;
    }

    .card-product .card-product-wrapper .list-product-btn {
        bottom: 48px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-2 {
        bottom: 25px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-3 {
        bottom: 57px;
    }

    .list-product-btn.column-left {
        gap: 8px;
    }

    .list-product-btn.column-right {
        gap: 8px;
    }

    .flat-lookbook-v2 .col-left {
        width: calc(58.3333333333% - 15px);
    }

    .flat-lookbook-v2 .col-right {
        width: calc(41.6666666667% - 15px);
        margin-top: 63px;
    }

    .flat-title-lookbook {
        margin-bottom: 60px;
    }

    .widget-tab-4 {
        padding: 66px 79px 78px 56px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link {
        padding: 20px 0px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link .text {
        font-size: 30px;
        line-height: 36px;
    }

    .widget-tab-4 .nav-tab-item .nav-tab-link:hover .icon,
    .widget-tab-4 .nav-tab-item .nav-tab-link:hover span {
        color: var(--primary);
    }

    .header-default .box-nav-ul {
        gap: 30px;
    }

    .header-default .box-nav-ul.gap-40 {
        gap: 40px;
    }

    .tf-flash-sale {
        padding: 60px 40px;
    }

    .tf-flash-sale .heading-flash-sale {
        margin-left: 55px;
        padding: 4px 30px;
        gap: 20px;
    }

    .card-product.style-9 .card-product-info .box-icon {
        width: 57px;
        height: 57px;
    }
}

@media (min-width: 1600px) {
    .flat-lookbook-v2 .col-right {
        margin-left: 20px;
    }
}

@media (min-width: 1660px) {
    .radius-100 {
        border-radius: 100px;
    }
}

@media only screen and (max-width: 1599px) {
    .wrap-carousel .nav-next-slider {
        left: -24px;
    }

    .wrap-carousel .nav-prev-slider {
        right: -24px;
    }

    .wrap-carousel.style-2 .nav-next-slider {
        left: -50px;
    }

    .wrap-carousel.style-2 .nav-prev-slider {
        right: -50px;
    }
}

@media only screen and (max-width: 1470px) {
    .footer .footer-wrap .footer-body::before {
        left: 15px;
        right: 15px;
        transform: unset;
        width: unset;
    }

    .has-line-bottom {
        position: relative;
    }

    .has-line-bottom::after {
        left: 15px;
        right: 15px;
        transform: unset;
        width: unset;
    }
}

@media only screen and (max-width: 1399px) {
    .canvas-compare .close-popup {
        top: 4px;
        right: 4px;
    }

    .card-product .count-down .countdown__item {
        min-width: 42px;
        min-height: 60px;
        padding: 0;
    }
}

@media only screen and (max-width: 1200px) {
    .tf-top-bar_wrap .tf-social-icon,
    .tf-top-bar_wrap .top-bar-language,
    .tf-top-bar_left {
        display: none !important;
    }

    .tf-top-bar .grid-3 {
        grid-template-columns: 1fr;
    }

    .wrapper-header .nav-icon {
        gap: 14px;
    }

    .footer .footer-logo,
    .footer .footer-heading {
        margin-top: 40px;
        margin-bottom: 22px;
    }

    .footer.has-all-border .footer-col,
    .footer.has-all-border .footer-newsletter {
        width: 100% !important;
        border-right: 0 !important;
        padding: 0px 20px 0 0 !important;
    }

    .footer.has-border .footer-col,
    .footer.has-border .footer-newsletter {
        width: 50% !important;
        border-right: 0 !important;
        padding: 0px 20px 0 0 !important;
    }

    .mega-menu .row-demo {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media only screen and (max-width: 1149px) {
    .card-product .count-down .countdown__timer {
        gap: 5px;
    }

    .card-product .count-down .countdown__item {
        min-height: 50px;
        padding: 0;
    }

    .sidebar-mobile-append .wrap-price {
        display: none;
    }

    .flat-testimonial-bg-v2 .wrap-content-right .sw-wrapper-right {
        margin-right: 0;
    }

    .header-style-2 .header-bottom .box-navigation {
        display: none;
    }

    .header-style-3 .box-navigation {
        display: none;
    }

    .header-style-3 .wrap-header-left {
        justify-content: center;
    }

    .header-style-4#header .nav-icon .nav-search {
        display: inline-flex;
    }

    .header-style-4#header .nav-icon .nav-account,
    .header-style-4#header .nav-icon .nav-wishlist,
    .header-style-4#header .nav-icon .nav-compare {
        display: none;
    }

    .logo-header {
        display: flex;
        justify-content: center;
    }

    .mega-menu .row-demo {
        grid-template-columns: repeat(3, 1fr);
    }

    .header-style-3 .wrap-header-left {
        justify-content: center;
    }

    .tf-sticky-btn-atc {
        bottom: 67px;
    }

    .tf-accordion-wrap {
        gap: 30px;
    }

    .tf-product-bundle-wrap {
        padding: 14px;
    }

    .tf-bundle-product-item {
        gap: 10px;
    }

    .tf-product-bundle-total-submit .compare-at-price {
        font-size: 22px;
    }

    .tf-model-viewer model-viewer {
        height: 780px;
    }

    .toggle-title {
        font-size: 14px !important;
        line-height: 22.4px !important;
    }

    .tf-img-video-text .content-wrap {
        padding: 30px 15px;
    }

    .tf-img-video-text .content-wrap li:not(:last-child) {
        padding-bottom: 20px;
        margin-bottom: 20px;
    }

    .card-product.style-price .card-product-info {
        padding: 15px 20px 0 20px;
    }

    .card-product.style-price .card-product-info .title {
        font-size: 16px;
    }

    .card-product.style-price .tf-price-table-contents {
        padding: 20px;
    }

    .card-product.style-price .tf-price-table-contents ul li {
        font-size: 14px !important;
    }

    .card-product.style-price .tf-price-table-contents .tf-price-table-btn {
        margin-top: 20px !important;
    }

    .flat-lookbook-v3 form {
        max-width: 90%;
    }

    .flat-lookbook-v3 .tf-lookbook {
        height: 304px;
    }

    .flat-lookbook-v3 .sw-dots {
        margin-bottom: 16px;
    }

    .tf-bundle-product-item.type-lg {
        gap: 20px;
        margin-top: 15px;
        padding-bottom: 15px;
        border-bottom: 0;
    }

    .form-sign-in .tf-login-form .bottom {
        flex-direction: column;
    }

    .canvas-search {
        max-width: 400px;
        padding-top: 10px;
    }

    .canvas-search .tf-search-head {
        padding: 0 15px;
        margin-bottom: 22px;
    }

    .canvas-search .tf-search-head .title {
        line-height: 40px;
        margin-bottom: 20px;
    }

    .canvas-search .tf-search-content {
        padding: 0 15px 16px 15px;
    }

    .canvas-search .tf-search-content-title {
        margin-bottom: 22px !important;
    }

    .canvas-search .tf-col-quicklink {
        margin-bottom: 17px;
    }

    .canvas-search .tf-search-hidden-inner {
        padding-top: 0;
    }

    .tf-mini-cart-tool-openable .tf-mini-cart-tool-content {
        padding: 20px;
    }

    .modal-shopping-cart .modal-content {
        max-width: 420px !important;
    }

    .modal-shopping-cart .header {
        margin: 0 15px;
    }

    .modal-shopping-cart .tf-mini-cart-threshold {
        margin: 0 15px;
    }

    .modal-shopping-cart .tf-mini-cart-threshold .tf-progress-msg {
        font-size: 14px;
        line-height: 22.4px;
    }

    .modal-shopping-cart .tf-mini-cart-item {
        margin: 0 15px;
    }

    .modal-shopping-cart .tf-mini-cart-item .tf-mini-cart-info a {
        font-size: 14px;
        line-height: 14px;
    }

    .modal-shopping-cart .tf-minicart-recommendations {
        padding: 15px;
        margin: 15px;
    }

    .modal-shopping-cart .tf-mini-cart-bottom-wrap {
        padding: 10px 15px 15px;
    }

    .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout {
        flex-direction: column;
    }

    .slider-collection {
        margin-bottom: 10px;
    }

    .tf-product-modal .modal-dialog .modal-content {
        padding: 20px;
    }

    #quick_view .tf-product-media-wrap {
        width: 50% !important;
        padding-right: 15px;
    }

    #quick_view .tf-product-info-wrap .tf-product-info-list {
        padding: 20px 30px 20px 15px;
    }

    #quick_view .modal-content {
        margin: 0 !important;
    }

    #quick_view .modal-content .tf-product-info-title a {
        font-size: 26px !important;
    }

    #ask_question input {
        height: 42px !important;
    }

    #ask_question textarea {
        height: 120px;
    }

    .tf-product-info-wrap .tf-product-info-title h5 {
        font-size: 26px;
        line-height: 31.2px;
    }

    .tf-product-modal .modal-dialog .modal-content {
        padding: 20px;
        border-radius: 3px;
    }

    #compare_color .modal-dialog {
        max-width: min(900px, 90vw);
        min-width: min(900px, 90vw);
    }

    #compare_color .modal-dialog .modal-content {
        padding: 20px;
    }

    .tf-compare-color-item {
        padding: 14px 15px 20px;
        min-width: 280px;
        max-width: 280px;
    }

    .blog-article-item.style-row .article-thumb {
        width: 300px;
        height: 300px;
    }

    .widget-tabs.style-has-border .widget-menu-tab {
        gap: 10px 25px;
        margin: 0 15px;
    }

    .widget-tabs.style-has-border .widget-menu-tab .item-title {
        padding: 12px 0;
        font-size: 14px;
        line-height: 18px;
    }

    .widget-tabs.style-has-border .widget-content-tab .widget-content-inner {
        padding: 20px 15px;
    }

    .widget-tabs.style-two-col {
        display: block;
    }

    .widget-tabs.style-two-col .widget-menu-tab {
        flex-direction: row;
        gap: 10px 25px;
        border: 0;
    }

    .widget-tabs.style-two-col .widget-menu-tab .item-title {
        padding: 12px 0;
        font-size: 14px;
        line-height: 18px;
    }

    .widget-tabs.style-two-col .widget-menu-tab .item-title::after {
        top: unset;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px !important;
    }

    .widget-tabs.style-two-col .widget-menu-tab .item-title.active::after {
        width: 100%;
    }

    .widget-tabs.style-two-col .widget-content-tab {
        margin-top: 20px;
        border-radius: 5px;
    }

    .widget-tabs.style-two-col .widget-content-tab .widget-content-inner {
        padding: 20px 15px;
    }

    .tf-product-des-demo {
        grid-template-columns: unset;
    }

    .thumbs-slider {
        flex-direction: column !important;
    }

    .thumbs-slider > div {
        width: 100%;
    }

    .thumbs-slider .tf-product-media-thumbs {
        order: 1;
    }

    .thumbs-slider .tf-product-media-thumbs .swiper-slide {
        width: auto;
        height: 107px;
    }

    .tf-product-info-list {
        padding-left: 0;
    }

    .tf-product-info-countdown .countdown-wrap {
        min-width: unset;
    }

    .tf-product-info-countdown .countdown-wrap .countdown__timer {
        flex-wrap: wrap;
    }

    .tf-product-delivery {
        margin-bottom: 30px;
    }

    .tf-product-fbt-wrap {
        margin-top: 30px;
    }

    .tf-product-fbt-wrap .title {
        margin-bottom: 20px;
    }

    .tf-product-fbt-wrap form {
        gap: 30px;
    }

    .wrap-sidebar-mobile {
        display: none;
    }

    .btn-sidebar-style2,
    .btn-sidebar-mobile {
        display: block;
    }

    .tf-toolbar-bottom.type-1150 {
        display: flex;
    }

    .announcement-bar {
        padding-right: 40px;
    }

    .announcement-bar .close-announcement-bar {
        right: 0;
    }

    .wrap-slider .box-content p {
        margin-bottom: 32px;
    }

    .tf-slideshow .sw-absolute-2 .sw-dots {
        justify-content: center;
    }

    .tf-slideshow .card-box {
        background-color: rgba(0, 0, 0, 0.3);
        padding: 20px;
        border-radius: 10px;
        width: max-content;
    }

    .tf-slideshow .card-box .tf-btn {
        font-size: 14px;
        line-height: 46px;
    }

    .tf-slideshow .card-box .heading br {
        display: none;
    }

    .tf-slideshow .sw-absolute-3 {
        left: 0;
        right: 0;
    }

    .tf-slideshow .sw-absolute-3 .sw-dots {
        flex-direction: row;
        justify-content: center;
    }

    .sw-dots.style-2 span::before {
        width: 6px;
        height: 6px;
    }

    .sw-dots.style-2 span.swiper-pagination-bullet-active {
        border-width: 1px;
    }

    .sw-dots.style-2 span.swiper-pagination-bullet-active::before {
        width: 4px;
        height: 4px;
    }

    .slider-skincare .sw-absolute-3 {
        top: unset;
        transform: unset;
        bottom: 57px;
    }

    .testimonial-item.style-row {
        flex-direction: column;
    }

    .testimonial-item.style-row .image {
        margin-left: auto;
        margin-right: auto;
    }

    .hover-sw-nav .sw-dots {
        display: flex;
    }

    .hover-sw-nav .nav-sw {
        display: none;
    }

    .slider-effect .box-content .heading {
        font-size: 40px;
        line-height: 48px;
        margin-bottom: 14px;
    }

    .slider-effect .box-content .heading br {
        display: none;
    }

    .slideshow-effect-zoom .content-left .tf-btn {
        margin-top: 32px;
    }

    .slider-radius .tf-sw-slideshow {
        border-radius: 20px;
    }

    .slider-radius .tf-sw-slideshow .heading {
        margin-bottom: 20px;
    }

    .list-color-product .list-color-item {
        width: 20px;
        height: 20px;
        padding: 2px;
    }

    .list-color-product .list-color-item .swatch-value {
        width: 14px;
        height: 14px;
        border-width: 1px;
    }

    .tf-control-layout .sw-layout-4,
    .tf-control-layout .sw-layout-5,
    .tf-control-layout .sw-layout-6 {
        display: none;
    }

    [data-grid="grid-4"],
    [data-grid="grid-5"],
    [data-grid="grid-6"] {
        grid-template-columns: repeat(3, 1fr);
    }

    .discovery-new-item {
        padding: 30px;
    }

    .flat-spacing-4 {
        padding-top: 46px;
        padding-bottom: 85px;
    }

    .flat-spacing-5 {
        padding: 64px 0px;
    }

    .flat-spacing-6 {
        padding: 34px 0px 85px;
    }

    .flat-spacing-7 {
        padding-bottom: 50px;
    }

    .flat-spacing-8 {
        padding-top: 30px;
        padding-bottom: 60px;
    }

    .flat-spacing-9 {
        padding: 55px 0px;
    }

    .flat-spacing-10 {
        padding: 46px 0px;
    }

    .flat-spacing-11 {
        padding-top: 68px;
        padding-bottom: 60px;
    }

    .flat-spacing-12 {
        padding-top: 85px;
        padding-bottom: 85px;
    }

    .flat-spacing-13 {
        padding-top: 28px;
        padding-bottom: 34px;
    }

    .flat-spacing-14 {
        padding-top: 70px;
        padding-bottom: 50px;
    }

    .flat-spacing-15 {
        padding-top: 66px;
        padding-bottom: 85px;
    }

    .flat-spacing-16 {
        padding-top: 100px;
        padding-bottom: 55px;
    }

    .flat-spacing-17 {
        padding-top: 85px;
        padding-bottom: 67px;
    }

    .flat-spacing-18 {
        padding-top: 68px;
        padding-bottom: 80px;
    }

    .flat-spacing-23 {
        padding-top: 68px;
        padding-bottom: 80px;
    }

    .flat-spacing-24 {
        padding-top: 80px;
        padding-bottom: 80px;
    }

    .flat-spacing-27 {
        padding-top: 75px;
        padding-bottom: 75px;
    }

    .flat-spacing-29 {
        padding-top: 22px;
        padding-bottom: 76px;
    }

    .flat-spacing-30 {
        padding-top: 55px;
        padding-bottom: 35px;
    }

    .flat-image-text-section {
        padding-bottom: 0;
    }

    .nav-sw.lg {
        width: 40px;
        height: 40px;
    }

    .wrap-carousel .nav-next-slider {
        left: -18px;
    }

    .wrap-carousel .nav-prev-slider {
        right: -18px;
    }

    .wrap-carousel.style-2 .nav-next-slider {
        left: -18px;
    }

    .wrap-carousel.style-2 .nav-prev-slider {
        right: -18px;
    }

    .wrap-brand-v2 .sw-dots {
        margin-top: 30px;
    }

    .banner-gr-item .content > a {
        margin-top: 14px;
    }

    .wrapper-thumbs-testimonial {
        padding: 0 50px;
    }

    .wrapper-thumbs-testimonial .box-left {
        padding-right: 33px;
        padding-left: 10px;
    }

    .tf-hero-image-liquid .box-content p {
        font-size: 16px;
        line-height: 25.6px;
    }

    .tf-hero-image-liquid .box-content .heading {
        font-size: 40px;
        line-height: 48px;
    }

    .tf-hero-image-liquid .tf-countdown .countdown__item .countdown__value {
        font-size: 20px;
        line-height: 24px;
    }

    .tf-hero-image-liquid .tf-countdown .countdown__item .countdown__label {
        font-size: 10px;
        line-height: 12px;
    }

    .lookbook-sw .navigation-sw-dot {
        width: 30px;
        height: 30px;
    }

    .lookbook-sw .navigation-sw-dot span {
        width: 12px;
        height: 12px;
    }

    .flat-iconbox-v2 .tf-icon-box .title {
        margin-bottom: 8.5px;
    }

    .widget-tab-3 {
        margin-bottom: 30px;
        gap: 30px;
    }

    .widget-tab-3 .nav-tab-item a {
        padding-bottom: 10px;
        font-size: 26px;
        line-height: 31.2px;
    }

    .collection-item-v4.style-2 .collection-image {
        height: 450px;
    }

    .masonry-layout-v2 {
        gap: 15px;
        grid-template-areas: "item1 item1 item2 item2" "item1 item1 item3 item3" "item4 item5 item6 item6" "item4 item5 item6 item6";
    }

    .masonry-layout-v2 .item-1,
    .masonry-layout-v2 .item-6 {
        height: 455px;
    }

    .masonry-layout-v2 .item-2,
    .masonry-layout-v2 .item-3 {
        height: 220px;
    }

    .tf-page-cart-footer {
        margin-top: 30px;
    }

    .flat-accordion.style-default .toggle-content {
        padding: 20px 0;
    }

    #quick_add .modal-content {
        padding: 15px 0px 15px;
    }

    #quick_add .modal-content > .wrap {
        padding: 0px 15px;
    }

    #quick_add .modal-content .icon-close-popup {
        right: 15px;
    }

    #quick_add .tf-btn {
        font-size: 14px !important;
    }

    #quick_view .tf-btn {
        font-size: 14px !important;
    }

    .tf-product-btn-wishlist {
        width: 46px;
        height: 46px;
    }

    .tf-content-wrap .heading br,
    .tf-content-wrap .description br {
        display: none;
    }

    .description p br {
        display: none;
    }

    .nav-sw.w_46 {
        width: 40px;
        height: 40px;
    }

    .canvas-sidebar-blog.canvas-sidebar {
        max-width: 410px;
        width: 100%;
    }

    .masonry-layout-v3 .collection-image {
        height: 415px;
    }

    .col-xl-8 .flat-accordion {
        margin-top: 30px;
    }

    .masonry-layout-v4.style-2 {
        gap: 15px;
    }

    .masonry-layout-v4.style-2 .item-1 .collection-image {
        height: 795px;
    }

    .slider-sock .tf-slideshow .wrap-pagination {
        bottom: 20px;
    }
}

@media only screen and (max-width: 1024px) {
    .tf-slideshow.about-us-page .text {
        font-size: 50px;
        line-height: 60px;
    }

    .tf-sticky-btn-atc {
        bottom: 67px;
    }

    .tf-toolbar-bottom.type-1024 {
        display: flex;
    }

    .tf-marquee.type-big .marquee-item p {
        font-size: 60px;
        line-height: 72px;
    }

    .canvas-compare .canvas-body {
        padding: 20px 0;
    }

    .tf-compare-list {
        flex-direction: column;
        max-height: 100%;
        overflow-y: auto;
        gap: 20px;
    }

    .tf-compare-list > div {
        flex: 0 0 100% !important;
    }

    .tf-compare-list .tf-compare-offcanvas {
        width: 100%;
    }

    .tf-compare-list .tf-compare-offcanvas .tf-compare-item {
        flex: 0 0 25%;
    }

    .tf-compare-list .tf-compare-buttons {
        width: 100%;
    }

    .tf-compare-list .tf-compare-buttons .tf-compare-buttons-wrap {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 12px;
    }

    .tf-compare-list .tf-compare-buttons .tf-compare-buttons-wrap a {
        max-width: 147px;
    }

    .tf-page-title .heading {
        font-size: 32px;
        line-height: 38px;
    }

    .blog-detail-main-heading .title {
        font-size: 32px !important;
        line-height: 38px !important;
    }

    h1 {
        font-size: 44px;
        line-height: 52.8px;
    }

    h2 {
        font-size: 50px;
        line-height: 60px;
    }

    h4 {
        font-size: 32px;
        line-height: 30.4px;
    }

    .tf-slideshow .card-box .heading {
        font-size: 40px;
        line-height: 48px;
    }

    .tf-countdown-v2 .countdown__timer {
        gap: 10px;
    }
}

@media only screen and (max-width: 991px) {
    .wrap-table-invoice {
        overflow: auto;
    }

    .invoice-table .title th {
        padding: 15px 30px;
    }

    .invoice-table .content td {
        padding: 15px 30px;
    }

    .box-invoice .footer {
        padding: 30px;
    }

    .box-invoice .header {
        padding: 30px;
    }

    .box-invoice .wrap-top,
    .box-invoice .wrap-date,
    .box-invoice .wrap-info {
        gap: 20px;
    }

    .box-invoice .box-left,
    .box-invoice .box-right {
        width: 50%;
    }

    .my-account-nav {
        gap: 10px;
        margin-bottom: 30px;
    }

    .my-account-nav .my-account-nav-item {
        font-size: 16px;
        line-height: 28px;
    }

    .tf-slideshow .wrap-pagination {
        bottom: 20px;
    }

    .card-product .list-product-btn .box-icon .tooltip {
        margin-top: 0;
    }

    .collection-item-v2.type-small .collection-content .tf-btn {
        padding: 12px 24px;
    }

    .tf-marquee.type-big .marquee-item {
        gap: 50;
        padding: 0 20px;
    }
}

@media only screen and (max-width: 850px) {
    .slider-wrap-lookbook .flat-title-lookbook .heading {
        font-size: 30px;
        line-height: 36px;
    }
}

@media only screen and (max-width: 767px) {
    .flat-title-tab {
        display: grid;
        justify-content: center !important;
    }

    .flat-title-tab .title {
        text-align: center;
    }

    .card-product .count-down {
        display: none;
    }

    .box-video-wrap {
        margin-bottom: 35px;
    }

    .slider-sock .img-mb {
        display: block;
    }

    .slider-sock .img-dk {
        display: none;
    }

    .slider-sock.tf-slideshow .wrap-slider {
        height: 300px;
    }

    .slider-home-decor .wrap-pagination .sw-dots {
        justify-content: center !important;
    }

    .tf-product-btn-wishlist:hover .tooltip,
    .list-color-product .list-color-item:hover .tooltip {
        display: none;
    }

    .tf-store-list {
        margin-bottom: 15px;
    }

    .tf-store-list .tf-store-item.active .tf-store-title {
        margin-bottom: 17px;
    }

    .tf-store-list .tf-store-item.active .tf-store-info {
        display: block;
    }

    .tf-store-list .tf-store-title {
        font-size: 16px;
        line-height: 19px;
        margin-bottom: 0;
    }

    .tf-store-list .tf-store-info {
        display: none;
    }

    .tf-accordion-wrap {
        gap: 50px;
        flex-direction: column;
    }

    .tf-slideshow.about-us-page .text {
        font-size: 24px;
        line-height: 29px;
    }

    .tf-product-bundle-image img {
        width: 60px;
        min-width: 60px;
        max-width: 60px;
    }

    .tf-sticky-btn-atc .tf-sticky-atc-product {
        display: none !important;
    }

    .tf-sticky-btn-atc .tf-sticky-atc-infos {
        width: 100%;
    }

    .tf-sticky-btn-atc .tf-sticky-atc-infos form {
        flex-direction: column;
    }

    .flat-iconbox-v3 .tf-icon-box .icon {
        width: 60px;
        height: 60px;
        margin-bottom: 22px;
    }

    .tf-img-video-text .content-wrap .heading {
        font-size: 24px;
        line-height: 29px;
    }

    .tf-img-video-text .content-wrap .number {
        width: 40px !important;
        height: 40px !important;
    }

    .tf-img-video-text video {
        height: 410px;
    }

    .tf-marquee.type-big .marquee-item svg {
        width: 20px;
    }

    .tf-marquee.type-big .marquee-item p {
        font-size: 40px;
        line-height: 48px;
    }

    .tf-banner-collection img {
        min-height: 300px;
    }

    .tf-banner-collection .box-content .heading {
        font-size: 22px;
        line-height: 26px;
    }

    .tf-banner-collection .box-content p {
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 15px;
    }

    .flat-lookbook-v3 {
        flex-direction: column-reverse;
        gap: 15px;
    }

    .flat-lookbook-v3 > div {
        width: 100% !important;
    }

    .flat-lookbook-v3 form {
        max-width: 100%;
        text-align: center;
    }

    .flat-lookbook-v3 form button {
        width: unset !important;
    }

    .toolbar-shop-mobile .sub-nav-menu {
        margin-left: 17px !important;
    }

    .toolbar-shop-mobile .sub-menu-level-2 {
        margin-left: 34px !important;
    }

    .form-sign-in .modal-dialog {
        margin-top: 8px;
        margin-bottom: 8px;
        height: calc(100vh - 16px);
        max-height: calc(100vh - 16px);
    }

    .form-sign-in .modal-dialog .modal-content {
        padding: 20px;
    }

    .form-sign-in .modal-dialog .modal-content .bottom {
        gap: 15px;
    }

    .canvas-search {
        max-width: 320px;
    }

    .canvas-search .tf-search-head .title {
        font-size: 18px;
    }

    .canvas-search .tf-search-content-title {
        font-size: 16px;
        line-height: 19px;
    }

    .modal-shopping-cart .modal-content {
        max-width: 90% !important;
    }

    .modal-shopping-cart .tf-mini-cart-threshold {
        padding: 10px 0;
    }

    .modal-shopping-cart .tf-mini-cart-tool .tf-mini-cart-tool-btn {
        width: 50px;
        height: 38px;
        line-height: 38px;
        margin: 18px 8px;
        font-size: 18px;
    }

    .modal-shopping-cart .tf-mini-cart-bottom-wrap .tf-mini-cart-view-checkout a {
        height: 39px;
    }

    .modal-shopping-cart .tf-mini-cart-threshold .tf-progress-msg {
        font-size: 13px;
        line-height: 20.8px;
    }

    .modal-shopping-cart .tf-mini-cart-item {
        gap: 14px;
    }

    #quick_view .wrap {
        flex-direction: column;
        overflow-y: auto;
    }

    #quick_view .wrap .tf-product-media-wrap {
        width: 100% !important;
        padding-right: 0 !important;
    }

    #quick_view .wrap .tf-product-info-wrap .tf-product-info-list {
        overflow-y: unset;
        position: unset;
        padding: 20px 15px;
    }

    .tf-compare-list .tf-compare-head .title {
        font-size: 24px;
    }

    .tf-compare-list .tf-compare-offcanvas .tf-compare-item {
        flex: 0 0 33.333%;
        padding: 0 8px;
    }

    .modal-part-content .modal-dialog {
        min-width: unset;
        align-items: flex-end;
    }

    .modal-part-content .modal-dialog .header {
        margin-bottom: 22px;
    }

    .tf-compare-color-item {
        min-width: 180px;
        max-width: 180px;
        padding: 10px 10px 12px;
    }

    .tf-compare-color-item form a {
        height: 41px;
    }

    .modal-dialog {
        max-width: 500px !important;
    }

    .modal-dialog .demo-title {
        font-size: 18px !important;
        font-weight: 400 !important;
        line-height: 22px !important;
    }

    #ask_question button {
        font-size: 14px !important;
        height: 39px;
    }

    #find_size .modal-dialog {
        margin-top: 8px !important;
        margin-bottom: 8px !important;
    }

    #find_size .tf-page-size-chart-content {
        grid-template-columns: unset;
    }

    #find_size .tf-page-size-chart-content > div:last-child {
        text-align: center;
    }

    [qol-data-title]:before {
        content: attr(qol-data-title);
        color: var(--main);
        text-align: start;
        flex: 1 1 auto;
    }

    .tf-quick-order-list-total {
        bottom: 67px;
    }

    .tf-quick-order-list-total .tf-total-wrap {
        flex-direction: column;
        gap: 5px;
    }

    .tf-quick-order-list-total .tf-total-wrap > div {
        width: 100%;
    }

    .tf-quick-order-list-total .tf-total-wrap .tf-total-item-inner {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-direction: row-reverse;
        font-size: 18px;
        line-height: 29px;
    }

    .tf-quick-order-list-total .tf-total-wrap .tf-viewcart {
        order: 999;
        margin-top: 10px;
    }

    .tf-quick-order-list-total .tf-total-wrap .tf-total-price {
        text-align: start;
    }

    .tf-quick-order-list-total
    .tf-total-wrap
    .tf-total-price
    .tf-totals-variant-total {
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
    }

    .tf-quick-order-list-total
    .tf-total-wrap
    .tf-total-price
    .tf-totals-variant-total
    p {
        font-size: 18px;
    }

    .tf-variant-item {
        display: block;
        position: relative;
        padding-left: 90px;
        margin-bottom: 15px;
    }

    .tf-variant-item > * {
        width: 100%;
        display: flex;
        padding: 10px 0;
    }

    .tf-variant-item > *:first-child {
        padding: 0;
    }

    .tf-variant-item .tf-variant-item-image-container {
        position: absolute;
        top: 0;
        left: 0;
    }

    .tf-qol-head {
        padding-bottom: 15px;
        margin-bottom: 10px;
    }

    .blog-article-item.style-row {
        flex-direction: column;
        gap: 15px;
    }

    .blog-article-item.style-row .article-thumb {
        width: 100%;
        height: unset;
    }

    .blog-article-item.style-row .article-content {
        gap: 10px;
    }

    .blog-article-item.style-row .article-title a {
        font-size: 16px;
        line-height: 19px;
    }

    .blog-article-item.style-row .article-btn {
        margin-top: 10px;
    }

    .tf-product-fbt-wrap form .tf-product-fbt-list img {
        width: 90px;
        max-width: 90px;
        height: unset;
    }

    .tf-product-fbt-wrap form .tf-fbt-col-total-price {
        width: 100%;
    }

    .tf-product-media-wrap {
        margin-bottom: 15px;
    }

    .tf-product-info-countdown .countdown-wrap {
        width: 100%;
    }

    h1 {
        font-size: 34px;
        line-height: 40.8px;
    }

    h2 {
        font-size: 32px;
        line-height: 38.4px;
    }

    h4 {
        font-size: 22px;
        line-height: 26.4px;
    }

    h5 {
        font-size: 18px;
        line-height: 21.6px;
    }

    h6 {
        font-size: 16px;
        line-height: 26px;
    }

    .footer .footer-heading-desktop {
        display: none;
    }

    .footer .footer-col-block .footer-heading-moblie {
        margin-top: 30px;
        margin-bottom: 15px;
        display: block;
        position: relative;
    }

    .footer .footer-col-block .footer-heading-moblie::after {
        position: absolute;
        content: "";
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 1px;
        background-color: var(--main);
        transition: 0.25s ease-in-out;
    }

    .footer .footer-col-block .footer-heading-moblie::before {
        position: absolute;
        content: "";
        right: 15px;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 1px;
        height: 12px;
        background-color: var(--main);
        transition: 0.25s ease-in-out;
    }

    .footer .footer-col-block.open .footer-heading-moblie::before {
        opacity: 0;
    }

    .footer .footer-col-block.open .footer-heading-moblie::after {
        transform: translate(0%, -50%) rotate(180deg);
    }

    .footer .tf-collapse-content {
        display: none;
    }

    .footer .footer-bottom .footer-bottom-wrap {
        flex-wrap: wrap;
    }

    .footer .footer-bottom .footer-bottom-wrap > div {
        width: 100%;
        text-align: center;
        justify-content: center;
    }

    .footer.has-all-border .footer-col,
    .footer.has-all-border .footer-newsletter,
    .footer.has-border .footer-col,
    .footer.has-border .footer-newsletter {
        width: 100% !important;
    }

    .footer.background-black .footer-heading-moblie::after,
    .footer.background-black .footer-heading-moblie::before {
        background-color: var(--white);
    }

    #header .nav-icon .nav-account,
    #header .nav-icon .nav-wishlist,
    #header .nav-icon .nav-compare {
        display: none;
    }

    .mega-menu .row-demo {
        grid-template-columns: 1fr;
    }

    .box-sw-announcement-bar {
        animation: slide-har 4s linear infinite;
    }

    .speed-1 {
        animation: slide-har 15s linear infinite !important;
    }

    .tf-slideshow .wrap-slider {
        height: 450px;
    }

    .tf-slideshow .banner-wrapper {
        height: 500px;
    }

    .tf-slideshow.flat-banner-sock .wrap-slider {
        height: 300px;
    }

    .tf-slideshow.slider-grocery .wrap-slider {
        height: 350px;
    }

    .slider-home-2 .wrap-slider {
        height: 300px;
        margin-right: -80px;
    }

    .slider-home-4 .wrap-slider {
        height: 250px;
    }

    .slider-women .wrap-slider {
        height: 400px;
        overflow: hidden;
    }

    .slider-women .wrap-slider img {
        position: absolute;
        top: 0;
        right: 0;
        width: 700px;
        min-width: max(100%, 700px);
    }

    .slider-baby .wrap-slider {
        height: 300px;
    }

    .slider-radius .wrap-slider {
        height: 350px;
    }

    .tf-hero-image-liquid {
        height: 350px;
    }

    .tf-hero-image-liquid .box-content {
        text-align: center;
    }

    .tf-hero-image-liquid .box-content .heading {
        font-size: 24px;
        line-height: 28.8px;
    }

    .tf-hero-image-liquid .tf-countdown .countdown__timer {
        justify-content: center;
        gap: 10px;
    }

    .tf-hero-image-liquid .tf-countdown .countdown__item {
        width: 60px;
        height: 60px;
    }

    .banner-countdown-v2 {
        height: 250px;
    }

    .tf-btn.btn-xl {
        font-size: 16px;
        line-height: 40px;
        padding-top: 0;
        padding-bottom: 0;
    }

    .wrap-slider .box-content p {
        display: none;
    }

    .wrap-slider .card-box .tf-btn {
        font-size: 14px;
        line-height: 38px;
    }

    .wrap-slider .card-box p {
        display: block;
    }

    .sw-dots {
        gap: 4px;
    }

    .sw-dots .swiper-pagination-bullet {
        width: 16px;
        height: 16px;
    }

    .md-hidden {
        display: none;
    }

    .slider-home-5 .box-content p {
        display: block;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 24px;
    }

    .slider-giftcard .box-content {
        padding-right: 40px;
    }

    .slider-giftcard .box-content .heading br {
        display: none;
    }

    .slider-giftcard .box-content p {
        display: block;
        font-size: 16px;
        line-height: 19.2px;
        margin-bottom: 24px;
    }

    .tf-page-title {
        padding: 52px 0 49px;
    }

    .tf-page-title .heading {
        font-size: 22px;
        line-height: 26px;
    }

    .blog-detail-main-heading .title {
        font-size: 22px !important;
        line-height: 26px !important;
    }

    .wrap-slider .card-box {
        margin: auto;
    }

    .wrap-slider .card-box p {
        display: none;
    }

    .wrap-slider .card-box .heading {
        font-size: 24px;
        line-height: 28.8px;
        margin-bottom: 24px;
    }

    .slider-effect {
        display: flex;
        flex-direction: column-reverse;
    }

    .slider-effect .box-content .heading {
        font-size: 34px;
        line-height: 40.8px;
    }

    .slider-effect .box-content .desc {
        display: block;
        margin-bottom: 22px;
        font-size: 16px;
        line-height: 19.2px;
    }

    .slider-effect .content-left {
        position: unset;
        padding: 40px 0px;
        text-align: center;
    }

    .slider-effect .img-slider {
        width: 100%;
        height: 300px;
    }

    .slideshow-effect .wrap-pagination {
        bottom: 15px;
    }

    .slideshow-effect .wrap-pagination .sw-dots {
        justify-content: center;
    }

    .slideshow-effect.style-padding .content-left {
        padding-bottom: 60px;
    }

    .slideshow-effect-zoom {
        padding: 0;
    }

    .slideshow-effect-zoom .content-left {
        padding: 14px;
        padding-bottom: 24px;
    }

    .slideshow-effect-zoom .content-left .desc {
        margin-top: 16px;
        font-size: 18px;
        line-height: 28.8px;
    }

    .slideshow-effect-zoom .content-left .tf-btn {
        margin-top: 18px;
    }

    .slideshow-effect-zoom .wrap-content {
        flex-direction: column-reverse;
        gap: 15px;
    }

    .slideshow-effect-zoom .wrap-content .content-left,
    .slideshow-effect-zoom .wrap-content .content-right {
        width: 100%;
    }

    .slideshow-effect-zoom .wrap-content .content-left {
        text-align: center;
    }

    .slideshow-effect-zoom .wrap-content .content-left .heading br {
        display: none;
    }

    .slider-radius {
        padding: 0;
    }

    .slider-radius .tf-sw-slideshow {
        border-radius: 0px;
    }

    .slider-radius .tf-sw-slideshow .subheading {
        display: block;
    }

    .slider-radius .tf-sw-slideshow .heading {
        margin-bottom: 20px;
    }

    .card-product .countdown-box,
    .card-product .size-list,
    .card-product .wishlist,
    .card-product .compare {
        display: none;
    }

    .card-product .card-product-wrapper .list-product-btn {
        bottom: 5px;
        left: 5px;
        right: 5px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-2 {
        bottom: 5px;
    }

    .card-product .card-product-wrapper .list-product-btn.absolute-3 {
        bottom: 5px;
    }

    .card-product .card-product-wrapper .column-right {
        top: 5px;
        right: 5px;
    }

    .card-product .card-product-wrapper .column-left {
        top: 5px;
        left: 5px;
    }

    .card-product.style-6 .list-product-btn,
    .card-product.style-7 .list-product-btn {
        justify-content: flex-start;
    }

    .tf-control-layout .sw-layout-3 {
        display: none;
    }

    [data-grid="grid-2"],
    [data-grid="grid-3"],
    [data-grid="grid-4"],
    [data-grid="grid-5"],
    [data-grid="grid-6"] {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .flat-title-v2 {
        margin-bottom: 30px;
        flex-direction: row-reverse;
        justify-content: space-between;
    }

    .nav-sw {
        width: 24px;
        height: 24px;
    }

    .discovery-new-item {
        height: auto;
        margin-top: 12px;
        padding: 10px 15px;
        gap: 15px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .discovery-new-item h5 {
        font-size: 22px;
        line-height: 26.4px;
    }

    .discovery-new-item a {
        width: 40px;
        height: 40px;
    }

    .flat-spacing-1 {
        padding: 52px 0px;
    }

    .flat-spacing-3 {
        padding: 30px 0;
    }

    .flat-spacing-4 {
        padding-top: 33px;
        padding-bottom: 60px;
    }

    .flat-spacing-5 {
        padding: 38px 0px;
    }

    .flat-spacing-6 {
        padding: 30px 0px 70px;
    }

    .flat-spacing-7 {
        padding-bottom: 45px;
    }

    .flat-spacing-8 {
        padding-top: 15px;
        padding-bottom: 35px;
    }

    .flat-spacing-9 {
        padding: 48px 0px;
    }

    .flat-spacing-10 {
        padding: 40px 0px;
    }

    .flat-spacing-11 {
        padding-top: 35px;
        padding-bottom: 22px;
    }

    .flat-spacing-12 {
        padding-top: 50px;
        padding-bottom: 50px;
    }

    .flat-spacing-13 {
        padding-top: 25px;
        padding-bottom: 30px;
    }

    .flat-spacing-14 {
        padding-top: 42px;
        padding-bottom: 30px;
    }

    .flat-spacing-15 {
        padding-top: 40px;
        padding-bottom: 50px;
    }

    .flat-spacing-16 {
        padding-top: 65px;
        padding-bottom: 32px;
    }

    .flat-spacing-17 {
        padding-top: 30px;
        padding-bottom: 23px;
    }

    .flat-spacing-18 {
        padding-top: 26px;
        padding-bottom: 30px;
    }

    .flat-spacing-19 {
        padding: 50px 0;
    }

    .flat-spacing-21 {
        padding-top: 28px;
        padding-bottom: 35px;
    }

    .flat-spacing-27 {
        padding-top: 45px;
        padding-bottom: 45px;
    }

    .flat-spacing-29 {
        padding-top: 19px;
        padding-bottom: 67px;
    }

    .flat-spacing-30 {
        padding-top: 48px;
        padding-bottom: 50px;
    }

    .lookbook-item .tf-pin-btn {
        width: 20px;
        height: 20px;
    }

    .lookbook-item .tf-pin-btn span {
        width: 6px;
        height: 6px;
    }

    .wrap-carousel .nav-sw {
        display: none;
    }

    .wrap-carousel .sw-dots {
        display: flex;
        margin-top: 15px;
    }

    .tf-img-with-text .tf-content-wrap {
        text-align: center;
    }

    .tf-btn.style-3,
    .tf-btn.style-2 {
        line-height: 38px;
    }

    .tf-btn.btn-md {
        padding: 0px 24px;
    }

    .banner-gr-item .img-style {
        margin-bottom: 15px;
    }

    .banner-gr-item .content .title {
        font-size: 20px;
        line-height: 24px;
    }

    .banner-gr-item .content > a {
        margin-top: 10px;
    }

    .md-pb-70 {
        padding-bottom: 70px;
    }

    .wrapper-thumbs-testimonial {
        padding: 0;
    }

    .wrapper-thumbs-testimonial .box-left {
        display: none;
    }

    .wrapper-thumbs-testimonial .box-right {
        width: 100%;
    }

    .tf-sw-testimonial.mb_60 {
        margin-bottom: 15px;
    }

    .testimonial-item .box-author .content {
        margin-top: 5px;
    }

    .testimonial-item.style-box {
        padding: 15px 24px;
    }

    .wrapper-thumbs-testimonial-v2 .box-right {
        display: none;
    }

    .wrapper-thumbs-testimonial-v2 .box-left {
        width: 100% !important;
        padding: 0;
    }

    .tf-icon-box.style-row {
        flex-direction: column;
        text-align: center;
    }

    .slider-wrap-lookbook .nav-sw {
        display: none;
    }

    .slider-wrap-lookbook .sw-dots {
        justify-content: center;
    }

    .slider-wrap-lookbook .card-product {
        display: flex;
        gap: 10px;
    }

    .slider-wrap-lookbook .card-product .card-product-wrapper {
        max-width: 100px;
    }

    .slider-wrap-lookbook .card-product .card-product-info {
        place-self: flex-start;
        padding-top: 0px;
    }

    .lookbook-sw .navigation-sw-dot {
        width: 24px;
        height: 24px;
    }

    .lookbook-sw .navigation-sw-dot span {
        width: 8px;
        height: 8px;
    }

    .lookbook-sw .navigation-sw-dot.swiper-button-disabled {
        width: 30px;
        height: 30px;
    }

    .flat-iconbox-v2 .tf-icon-box .title {
        margin-bottom: 6.5px;
    }

    .widget-tab-3 .nav-tab-item a {
        padding-bottom: 5px;
        font-size: 20px;
        line-height: 24px;
    }

    .collection-item-v4.style-2 .collection-image {
        height: 300px;
    }

    .masonry-layout-v2 {
        gap: 15px;
        grid-template-areas: "item1 item1" "item2 item3" "item4 item5" "item6 item6";
    }

    .masonry-layout-v2 .item-1,
    .masonry-layout-v2 .item-6 {
        height: 455px;
    }

    .masonry-layout-v2 .item-2,
    .masonry-layout-v2 .item-3,
    .masonry-layout-v2 .item-4,
    .masonry-layout-v2 .item-5 {
        height: 200px;
    }

    .tf-table-page-cart thead {
        display: none;
    }

    .tf-cart-item td[cart-data-title]:before {
        content: attr(cart-data-title);
        color: var(--main);
        text-align: start;
        flex: 1 1 auto;
    }

    .tf-cart-item {
        margin-bottom: 15px;
        padding-inline-start: 98px;
        min-height: 140px;
        display: block;
        font-size: 12px;
        font-weight: 600;
        position: relative;
    }

    .tf-cart-item td {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 10px 0;
        margin: 0;
    }

    .tf-cart-item td:not(:last-child) {
        border-bottom: 1px dashed var(--line);
    }

    .tf-cart-item .img-box {
        position: absolute;
        top: 0;
        left: 0;
        overflow: hidden;
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .tf-page-cart-checkout {
        padding: 30px 15px;
    }

    .tf-page-cart-checkout .tf-btn {
        height: 38px;
    }

    .lookbook-product {
        gap: 10px;
        width: 260px;
    }

    .tf-img-with-text.style-3 .tf-content-wrap {
        order: 2;
    }

    .tf-img-with-text.style-3 .tf-image-wrap {
        order: 1;
    }

    .btn-md {
        min-height: 40px;
    }

    .masonry-layout-v3 .collection-image {
        height: 350px;
    }

    .masonry-layout-v4 .collection-inner {
        height: 315px;
    }

    .tf-btn:not(.btn-xl, .btn-md, .btn-line, .style-2, .style-3) {
        padding: 10px 24px;
    }

    .tf-btn.style-2 {
        line-height: 36px;
    }

    .lookbook-product .content-wrap .product-title a {
        font-size: 14px;
        line-height: 18px;
    }

    .lookbook-product .content-wrap .price {
        font-size: 14px;
        line-height: 18px;
    }

    .lookbook-product .image {
        width: 52px;
        height: 60px;
    }

    .lookbook-product > a {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .wrap-mobile .collection-item-v2 .collection-image {
        height: 300px;
    }

    .form-checkout .grid-2 {
        grid-template-columns: 1fr;
    }

    .coupon-box input,
    .coupon-box .tf-btn {
        height: 40px;
    }

    .sw-dots.style-2 span,
    .sw-dots.medium span {
        width: 16px;
        height: 16px;
        border-width: 1px !important;
    }

    .flat-control-sw {
        position: relative;
    }

    .flat-control-sw .box-sw-navigation {
        position: absolute;
        bottom: 15px;
        left: 15px;
        right: 15px;
    }

    .grid-3-layout-md .collection-line-upper .collection-image {
        height: 313px;
    }

    .masonry-layout-v4.style-2 .item-1 .collection-image,
    .masonry-layout-v4.style-2 .item-2 .collection-image,
    .masonry-layout-v4.style-2 .item-3 .collection-image {
        height: 250px;
    }

    .text-22 {
        font-size: 18px !important;
    }

    .wrap-slider .box-content p {
        margin-bottom: 24px;
        font-size: 16px;
        line-height: 19.2px;
    }
}

@media only screen and (max-width: 575px) {
    .box-invoice .wrap-top,
    .box-invoice .wrap-date,
    .box-invoice .wrap-info {
        flex-wrap: wrap;
    }

    .box-invoice .box-left,
    .box-invoice .box-right {
        width: 100%;
    }

    .tf-compare-list .tf-compare-offcanvas .tf-compare-item {
        flex: 0 0 50%;
    }

    .card-product .list-product-btn .box-icon .tooltip {
        display: none;
    }

    .card-product:not(.style-3) .list-product-btn .box-icon .text {
        display: none;
    }

    .card-product:not(.style-3) .list-product-btn .box-icon.style-2 {
        width: 32px;
    }

    .card-product .list-color-product {
        overflow: hidden;
    }

    .tf-dropdown-sort .dropdown-menu {
        min-width: 150px;
    }

    .lookbook-1 .item-1 {
        top: 78%;
        left: 57%;
    }

    .lookbook-1 .item-2 {
        top: 58%;
        left: 40%;
    }

    .lookbook-2 .item-1 {
        top: 20%;
        left: 57%;
    }

    .widget-tab-3.style-2 {
        justify-content: flex-start !important;
    }

    .lookbook-kid .item-1 {
        left: 35%;
    }

    .lookbook-kid .item-2 {
        left: 35%;
    }

    .lookbook-kid .item-3 {
        left: 56%;
    }

    .banner-countdown-v2 .box-content {
        text-align: left;
        left: 10%;
        right: 10%;
    }

    .banner-countdown-v2 .tf-countdown .countdown__timer {
        justify-content: start;
    }
}

/* custom */
.content-single p {
    margin-bottom: 25px;
    font-size: 16px;
    line-height: 24px;
    font-family: "haas_light";
}

.content-single h1,
.content-single h2,
.content-single h3,
.content-single h4,
.content-single h5,
.content-single h6 {
    margin-bottom: 24px;
    margin-top: 24px;
    font-family: "haas_mediu";
}

.content-single ol {
    padding: 20px;
}

.content-single ul {
    padding: 20px;
}

.content-single ol li {
    padding: 5px;
    margin-left: 15px;
    list-style-type: auto;
}

.content-single ul li {
    margin: 15px;
    list-style-type: circle;
}

.content-single blockquote {
    padding: 8px;
    border-left: none;
    border-top: 1px solid #eaeaea;
    border-bottom: 1px solid #eaeaea;
    margin: 5px;
    background-position: top left;
    background-repeat: no-repeat;
    text-indent: 23px;
}

.content-single blockquote span {
    display: block;
    background-repeat: no-repeat;
    background-position: bottom right;
}

/* custom */
/* Garis loading di atas halaman */
#ajax-loading-bar {
    position: fixed;
    top: 0;
    left: 0;
    height: 3px;
    background-color: #db1215;
    width: 0;
    z-index: 9999;
    transition: width 0.4s ease, opacity 0.4s ease;
}

#ajax-loading-bar.complete {
    width: 100%;
    opacity: 0;
    transition: width 0.4s ease, opacity 0.6s ease;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
