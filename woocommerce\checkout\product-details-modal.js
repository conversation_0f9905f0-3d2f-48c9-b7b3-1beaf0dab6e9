/**
 * Product Details Modal JavaScript
 * Enhanced functionality for product details modal in checkout
 */

class ProductDetailsModal {
    constructor() {
        this.modal = null;
        this.showBtn = null;
        this.closeBtn = null;
        this.closeFooterBtn = null;
        this.isOpen = false;
        this.focusableElements = [];
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        // Get modal elements
        this.modal = document.getElementById('product-details-modal');
        this.showBtn = document.getElementById('show-product-details');
        this.closeBtn = document.getElementById('close-product-modal');
        this.closeFooterBtn = document.getElementById('close-product-modal-footer');
        
        if (!this.modal || !this.showBtn) {
            console.warn('Product details modal elements not found');
            return;
        }
        
        this.bindEvents();
        this.setupAccessibility();
    }
    
    bindEvents() {
        // Show modal
        this.showBtn.addEventListener('click', (e) => this.openModal(e));
        
        // Close modal events
        if (this.closeBtn) {
            this.closeBtn.addEventListener('click', () => this.closeModal());
        }
        
        if (this.closeFooterBtn) {
            this.closeFooterBtn.addEventListener('click', () => this.closeModal());
        }
        
        // Close on outside click
        this.modal.addEventListener('click', (e) => this.handleOutsideClick(e));
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
    }
    
    setupAccessibility() {
        // Set ARIA attributes
        this.modal.setAttribute('role', 'dialog');
        this.modal.setAttribute('aria-modal', 'true');
        this.modal.setAttribute('aria-labelledby', 'product-modal-title');
        
        // Add title ID if not exists
        const title = this.modal.querySelector('.product-modal-header h3');
        if (title && !title.id) {
            title.id = 'product-modal-title';
        }
    }
    
    openModal(e) {
        if (e) e.preventDefault();
        
        this.isOpen = true;
        
        // Show modal with smooth animation
        this.modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Add opening class for animation
        setTimeout(() => {
            this.modal.classList.add('modal-opening');
        }, 10);
        
        // Update focusable elements
        this.updateFocusableElements();
        
        // Focus on first focusable element
        setTimeout(() => {
            if (this.focusableElements.length > 0) {
                this.focusableElements[0].focus();
            }
        }, 300);
        
        // Trigger custom event
        this.triggerEvent('modalOpened');
        
        // Add body class for additional styling
        document.body.classList.add('product-modal-open');
    }
    
    closeModal() {
        if (!this.isOpen) return;
        
        this.isOpen = false;
        
        // Add closing animation
        this.modal.classList.add('modal-closing');
        this.modal.classList.remove('modal-opening');
        
        setTimeout(() => {
            this.modal.style.display = 'none';
            this.modal.classList.remove('modal-closing');
            document.body.style.overflow = 'auto';
            document.body.classList.remove('product-modal-open');
            
            // Return focus to trigger button
            if (this.showBtn) {
                this.showBtn.focus();
            }
        }, 300);
        
        // Trigger custom event
        this.triggerEvent('modalClosed');
    }
    
    handleOutsideClick(e) {
        if (e.target === this.modal) {
            this.closeModal();
        }
    }
    
    handleKeydown(e) {
        if (!this.isOpen) return;
        
        switch (e.key) {
            case 'Escape':
                e.preventDefault();
                this.closeModal();
                break;
                
            case 'Tab':
                this.handleTabKey(e);
                break;
        }
    }
    
    handleTabKey(e) {
        if (this.focusableElements.length === 0) return;
        
        const firstElement = this.focusableElements[0];
        const lastElement = this.focusableElements[this.focusableElements.length - 1];
        
        if (e.shiftKey) {
            // Shift + Tab
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            // Tab
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
    
    handleResize() {
        if (this.isOpen) {
            // Recalculate modal positioning if needed
            this.updateModalPosition();
        }
    }
    
    updateFocusableElements() {
        const focusableSelectors = [
            'button:not([disabled])',
            '[href]',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
            '[tabindex]:not([tabindex="-1"])'
        ].join(', ');
        
        this.focusableElements = Array.from(
            this.modal.querySelectorAll(focusableSelectors)
        ).filter(el => {
            return el.offsetWidth > 0 && el.offsetHeight > 0;
        });
    }
    
    updateModalPosition() {
        // Ensure modal is properly centered on resize
        const content = this.modal.querySelector('.product-modal-content');
        if (content) {
            const rect = content.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            
            if (rect.height > viewportHeight * 0.9) {
                content.style.maxHeight = '90vh';
            }
        }
    }
    
    triggerEvent(eventName, data = {}) {
        const event = new CustomEvent(`productModal:${eventName}`, {
            detail: {
                modal: this.modal,
                ...data,
                timestamp: Date.now()
            }
        });
        document.dispatchEvent(event);
    }
    
    // Public methods
    open() {
        this.openModal();
    }
    
    close() {
        this.closeModal();
    }
    
    isModalOpen() {
        return this.isOpen;
    }
    
    // Static method to get instance
    static getInstance() {
        if (!window.productDetailsModalInstance) {
            window.productDetailsModalInstance = new ProductDetailsModal();
        }
        return window.productDetailsModalInstance;
    }
}

// Enhanced Image Loading
class ImageLoader {
    static loadImages() {
        const images = document.querySelectorAll('.product-detail-item img');
        
        images.forEach(img => {
            if (img.complete) {
                img.classList.add('loaded');
            } else {
                img.addEventListener('load', () => {
                    img.classList.add('loaded');
                    img.style.opacity = '1';
                });
                
                img.addEventListener('error', () => {
                    img.style.opacity = '0.5';
                    console.warn('Failed to load product image:', img.src);
                });
            }
        });
    }
}

// Smooth Scrolling for Modal Content
class SmoothScroll {
    static init() {
        const modalBody = document.querySelector('.product-modal-body');
        if (!modalBody) return;
        
        // Add smooth scrolling behavior
        modalBody.style.scrollBehavior = 'smooth';
        
        // Add scroll indicators
        this.addScrollIndicators(modalBody);
    }
    
    static addScrollIndicators(container) {
        const indicator = document.createElement('div');
        indicator.className = 'scroll-indicator';
        indicator.innerHTML = '↓ Scroll for more';
        indicator.style.cssText = `
            position: absolute;
            bottom: 10px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1000;
        `;
        
        container.parentElement.appendChild(indicator);
        
        const checkScroll = () => {
            const isScrollable = container.scrollHeight > container.clientHeight;
            const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
            
            if (isScrollable && !isAtBottom) {
                indicator.style.opacity = '1';
            } else {
                indicator.style.opacity = '0';
            }
        };
        
        container.addEventListener('scroll', checkScroll);
        setTimeout(checkScroll, 100);
    }
}

// Animation Controller
class AnimationController {
    static init() {
        // Add staggered animation to product items
        const items = document.querySelectorAll('.product-detail-item');
        
        items.forEach((item, index) => {
            item.style.animationDelay = `${index * 0.1}s`;
            item.classList.add('animate-in');
        });
    }
    
    static addHoverEffects() {
        const items = document.querySelectorAll('.product-detail-item');
        
        items.forEach(item => {
            item.addEventListener('mouseenter', () => {
                item.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            item.addEventListener('mouseleave', () => {
                item.style.transform = 'translateY(0) scale(1)';
            });
        });
    }
}

// Initialize everything when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize main modal
    const modal = ProductDetailsModal.getInstance();
    
    // Initialize enhancements
    setTimeout(() => {
        ImageLoader.loadImages();
        SmoothScroll.init();
        AnimationController.init();
        AnimationController.addHoverEffects();
    }, 100);
    
    // Listen for modal events
    document.addEventListener('productModal:modalOpened', function(e) {
        console.log('Product modal opened', e.detail);
        
        // Re-initialize enhancements for dynamic content
        setTimeout(() => {
            ImageLoader.loadImages();
            AnimationController.init();
        }, 50);
    });
    
    document.addEventListener('productModal:modalClosed', function(e) {
        console.log('Product modal closed', e.detail);
    });
});

// Export for global access
window.ProductDetailsModal = ProductDetailsModal;
