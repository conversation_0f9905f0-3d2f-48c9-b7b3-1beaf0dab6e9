<?php
// hide plugin
// function hide_plugin_trickspanda()
// {
//     global $wp_list_table;
//     $hidearr = array('advanced-custom-fields-pro/acf.php');
//     $myplugins = $wp_list_table->items;
//     foreach ($myplugins as $key => $val) {
//         if (in_array($key, $hidearr)) {
//             unset($wp_list_table->items[$key]);
//         }
//     }
// }
// add_action('pre_current_active_plugins', 'hide_plugin_trickspanda');



// Primary Menu walker
class PrimaryMenu extends Walker_Nav_Menu
{

    public $tree_type = array('post_type', 'taxonomy', 'custom');

    public $db_fields = array(
        'parent' => 'menu_item_parent',
        'id' => 'db_id',
    );

    public function start_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // Default class.
        $classes = array('menu-list');

        $class_names = implode(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
        $blank_class_names = '';

        // inject -------------------------------------------------------------------------------------------
        if ($depth == 0) {
            $output .= "{$n}{$indent}" . '<div class="sub-menu submenu-default">' . "<ul$class_names" . ">{$n}";
        }
        if ($depth == 1) {
            $output .= "{$n}{$indent}" . '<div class="sub-menu submenu-default">' . "<ul$class_names" . ">{$n}";
        }
        if ($depth >= 2) {
            $output .= "{$n}{$indent}" . '<div class="sub-menu submenu-default">' . "<ul$class_names" . ">{$n}";
        }
        // end inject -------------------------------------------------------------------------------------------
    }

    public function end_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // inject -------------------------------------------------------------------------------------------
        if ($depth == 0) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth == 1) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth >= 2) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        // end inject -------------------------------------------------------------------------------------------


    }

    public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = ($depth) ? str_repeat($t, $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // inject class -----------------------------------------------------------------------------------------------------------
        array_push($classes, "");
        // inject class -----------------------------------------------------------------------------------------------------------

        // inject class if has children -------------------------------------------------------------------------------------------
        if ($args->walker->has_children) {
            if ($depth == 0) {
                array_push($classes, "menu-items position-relative");
            }
            if ($depth == 1) {
                array_push($classes, "menu-items-2");
            }
            if ($depth == 2) {
                array_push($classes, "menu-items-2");
            }
        } else {
            array_push($classes, "");
        }
        // inject class if has children -------------------------------------------------------------------------------------------

        // check active menu --------------------------------------------------------------------------------------------------------
        if (in_array('current-menu-item', $classes)) {
            array_push($classes, "");
        }
        // check active menu --------------------------------------------------------------------------------------------------------

        $args = apply_filters('nav_menu_item_args', $args, $item, $depth);

        $class_names = implode(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
        $class_names = $class_names ? ' class=" ' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        $atts = array();
        $atts['title'] = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        if ('_blank' === $item->target && empty($item->xfn)) {
            $atts['rel'] = 'noopener';
        } else {
            $atts['rel'] = $item->xfn;
        }
        $atts['href'] = !empty($item->url) ? $item->url : '';
        $atts['aria-current'] = $item->current ? 'page' : '';
        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);

        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (is_scalar($value) && '' !== $value && false !== $value) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        /** This filter is documented in wp-includes/post-template.php */
        $title = apply_filters('the_title', $item->title, $item->ID);
        $title = apply_filters('nav_menu_item_title', $title, $item, $args, $depth);

        // inject class to link ------------------------------------------------------------------------------------------------------
        // inject class if has children
        $classeslink = 'class=""';
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $classeslink = 'class="item-link"';
            }
            if ($depth == 1) {
                $classeslink = 'class="menu-link-text link text_black-2"';
            }
            if ($depth == 2) {
                $classeslink = 'class="menu-link-text link text_black-2"';
            }
        } else {
            if ($depth == 0) {
                $classeslink = 'class="item-link"';
            }
            if ($depth == 1) {
                $classeslink = 'class="menu-link-text link text_black-2"';
            }
            if ($depth == 2) {
                $classeslink = 'class="menu-link-text link text_black-2"';
            }
        }
        // inject class to link ------------------------------------------------------------------------------------------------------
        $item_output = $args->before;


        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject style
        if ($depth >= 1) {
            if ($args->walker->has_children) {
                $item_output .= '<a ' . $attributes . $classeslink . '>';
            } else {
                $item_output .= '<a ' . $attributes . $classeslink . '>';
            }
        } else {
            $item_output .= '<a ' . $attributes . $classeslink . '>';
        }
        // inject style to link ------------------------------------------------------------------------------------------------------

        $item_output .= $args->link_before . $title . $args->link_after;

        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject after if has children
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '';
                $item_output .= '<i class="icon icon-arrow-down"></i> </a>';
            }
            if ($depth == 1) {
                $item_output .= '';
                $item_output .= '</a>';
            }
            if ($depth == 2) {
                $item_output .= '';
                $item_output .= '</a>';
            }
            if ($depth == 3) {
                $item_output .= '';
                $item_output .= '</a>';
            }
        } else {
            $item_output .= '';
            $item_output .= '</a>';
        }
        // inject class to link ------------------------------------------------------------------------------------------------------


        $item_output .= $args->after;
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    public function end_el(&$output, $item, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }

        $output .= "</li>{$n}";
    }
}

// Mobile Menu walker
class MobileMenu extends Walker_Nav_Menu
{
    public $tree_type = array('post_type', 'taxonomy', 'custom');

    public $db_fields = array(
        'parent' => 'menu_item_parent',
        'id' => 'db_id',
    );

    public function start_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // Default class.
        $classes = array('sub-nav-menu');

        $class_names = implode(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . ' "' : '';
        $blank_class_names = '';

        // inject -------------------------------------------------------------------------------------------
        // Use the current_item_id from $args that was set in start_el()
        $item_id = isset($args->current_item_id) ? esc_attr($args->current_item_id) : uniqid();
        if ($depth == 0) {
            array_push($classes, "");
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        if ($depth == 1) {
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        if ($depth >= 2) {
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        // end inject -------------------------------------------------------------------------------------------
    }

    public function end_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // inject -------------------------------------------------------------------------------------------
        if ($depth == 0) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth == 1) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth >= 2) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        // end inject -------------------------------------------------------------------------------------------


    }

    public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = ($depth) ? str_repeat($t, $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // inject class -----------------------------------------------------------------------------------------------------------
        array_push($classes, "");
        // inject class -----------------------------------------------------------------------------------------------------------

        // inject class if has children -------------------------------------------------------------------------------------------
        if ($args->walker->has_children) {
            if ($depth == 0) {
                array_push($classes, "nav-mb-item");
            }
            if ($depth == 1) {
                array_push($classes, "");
            }
            if ($depth == 2) {
                array_push($classes, "");
            }
        } else {
            if ($depth == 0) {
                array_push($classes, "nav-mb-item");
            }
            if ($depth == 1) {
                array_push($classes, "");
            }
            if ($depth == 2) {
                array_push($classes, "");
            }
        }
        // inject class if has children -------------------------------------------------------------------------------------------

        // check active menu --------------------------------------------------------------------------------------------------------
        if (in_array('current-menu-item', $classes)) {
            array_push($classes, "");
        }
        // check active menu --------------------------------------------------------------------------------------------------------

        $args = apply_filters('nav_menu_item_args', $args, $item, $depth);

        $class_names = implode(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
        $class_names = $class_names ? ' class=" ' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        $atts = array();
        $atts['title'] = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        if ('_blank' === $item->target && empty($item->xfn)) {
            $atts['rel'] = 'noopener';
        } else {
            $atts['rel'] = $item->xfn;
        }
        $atts['href'] = !empty($item->url) ? $item->url : '';
        $atts['aria-current'] = $item->current ? 'page' : '';
        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);

        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (is_scalar($value) && '' !== $value && false !== $value) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        /** This filter is documented in wp-includes/post-template.php */
        $title = apply_filters('the_title', $item->title, $item->ID);
        $title = apply_filters('nav_menu_item_title', $title, $item, $args, $depth);

        // inject class to link ------------------------------------------------------------------------------------------------------
        // inject class if has children
        $classeslink = 'class=""';
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
            if ($depth == 1) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
            if ($depth == 2) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
        } else {
            if ($depth == 0) {
                $classeslink = 'class="mb-menu-link current"';
            }
            if ($depth == 1) {
                $classeslink = 'class="mb-menu-link current"';
            }
            if ($depth == 2) {
                $classeslink = 'class="mb-menu-link current"';
            }
        }
        // inject class to link ------------------------------------------------------------------------------------------------------
        $item_output = $args->before;


        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject style
        $args->current_item_id = $item->ID;
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 1) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 2) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 3) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
        } else {
            $item_output .= '<a ' . $attributes . $classeslink . '>';
        }
        // inject style to link ------------------------------------------------------------------------------------------------------

        $item_output .= $args->link_before . $title . $args->link_after;

        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject after if has children
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 1) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 2) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 3) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
        } else {
            $item_output .= '';
            $item_output .= '</a>';
        }
        // inject class to link ------------------------------------------------------------------------------------------------------


        $item_output .= $args->after;
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    public function end_el(&$output, $item, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }

        $output .= "</li>{$n}";
    }
}

// Footer Menu walker
class FooterMenu extends Walker_Nav_Menu
{
    public $tree_type = array('post_type', 'taxonomy', 'custom');

    public $db_fields = array(
        'parent' => 'menu_item_parent',
        'id' => 'db_id',
    );

    public function start_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // Default class.
        $classes = array('sub-menu');

        $class_names = implode(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
        $blank_class_names = '';
        // inject -------------------------------------------------------------------------------------------
        if ($depth == 0) {
            $output .= "{$n}{$indent}<ul$class_names" . ">{$n}";
        }
        if ($depth == 1) {
            $output .= "{$n}{$indent}<ul$blank_class_names" . ">{$n}";
        }
        if ($depth >= 2) {
            $output .= "{$n}{$indent}<ul$blank_class_names" . ">{$n}";
        }
        // end inject -------------------------------------------------------------------------------------------
    }

    public function end_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        $output .= "$indent</ul>{$n}";
    }

    public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = ($depth) ? str_repeat($t, $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // inject class -----------------------------------------------------------------------------------------------------------
        array_push($classes, "");
        // inject class -----------------------------------------------------------------------------------------------------------

        // inject class if has children -------------------------------------------------------------------------------------------
        if ($args->walker->has_children) {
            if ($depth == 0) {
                array_push($classes, "");
            }
            if ($depth == 1) {
                array_push($classes, "");
            }
            if ($depth == 2) {
                array_push($classes, "");
            }
        } else {
            array_push($classes, "");
        }
        // inject class if has children -------------------------------------------------------------------------------------------

        // check active menu --------------------------------------------------------------------------------------------------------
        if (in_array('current-menu-item', $classes)) {
            array_push($classes, "");
        }
        // check active menu --------------------------------------------------------------------------------------------------------

        $args = apply_filters('nav_menu_item_args', $args, $item, $depth);

        $class_names = implode(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
        $class_names = $class_names ? ' class=" ' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        $atts = array();
        $atts['title'] = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        if ('_blank' === $item->target && empty($item->xfn)) {
            $atts['rel'] = 'noopener';
        } else {
            $atts['rel'] = $item->xfn;
        }
        $atts['href'] = !empty($item->url) ? $item->url : '';
        $atts['aria-current'] = $item->current ? 'page' : '';
        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);

        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (is_scalar($value) && '' !== $value && false !== $value) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        /** This filter is documented in wp-includes/post-template.php */
        $title = apply_filters('the_title', $item->title, $item->ID);
        $title = apply_filters('nav_menu_item_title', $title, $item, $args, $depth);

        // inject class to link ------------------------------------------------------------------------------------------------------
        // inject class if has children
        $classeslink = 'class=""';
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $classeslink = 'class="footer-menu_item"';
            }
            if ($depth == 1) {
                $classeslink = 'class="footer-menu_item"';
            }
            if ($depth == 2) {
                $classeslink = 'class="footer-menu_item"';
            }
        } else {
            if ($depth == 0) {
                $classeslink = 'class="footer-menu_item"';
            }
            if ($depth == 1) {
                $classeslink = 'class="footer-menu_item"';
            }
            if ($depth == 2) {
                $classeslink = 'class="footer-menu_item"';
            }
        }
        // inject class to link ------------------------------------------------------------------------------------------------------
        $item_output = $args->before;


        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject style
        if ($depth >= 1) {
            if ($args->walker->has_children) {
                $item_output .= '<a ' . $attributes . $classeslink . '>';
            } else {
                $item_output .= '<a ' . $attributes . $classeslink . '>';
            }
        } else {
            $item_output .= '<a ' . $attributes . $classeslink . '>';
        }
        // inject style to link ------------------------------------------------------------------------------------------------------

        $item_output .= $args->link_before . $title . $args->link_after;

        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject after if has children
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '';
                $item_output .= '</a>';
            }
            if ($depth == 1) {
                $item_output .= '';
                $item_output .= '</a>';
            }
            if ($depth == 2) {
                $item_output .= '';
                $item_output .= '</a>';
            }
            if ($depth == 3) {
                $item_output .= '';
                $item_output .= '</a>';
            }
        } else {
            $item_output .= '';
            $item_output .= '</a>';
        }
        // inject class to link ------------------------------------------------------------------------------------------------------


        $item_output .= $args->after;
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    public function end_el(&$output, $item, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }

        $output .= "</li>{$n}";
    }
}

// Quick Menu walker
class QuickMenu extends Walker_Nav_Menu
{
    public $tree_type = array('post_type', 'taxonomy', 'custom');

    public $db_fields = array(
        'parent' => 'menu_item_parent',
        'id' => 'db_id',
    );

    public function start_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // Default class.
        $classes = array('sub-nav-menu');

        $class_names = implode(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . ' "' : '';
        $blank_class_names = '';

        // inject -------------------------------------------------------------------------------------------
        // Use the current_item_id from $args that was set in start_el()
        $item_id = isset($args->current_item_id) ? esc_attr($args->current_item_id) : uniqid();
        if ($depth == 0) {
            array_push($classes, "");
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        if ($depth == 1) {
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        if ($depth >= 2) {
            $output .= "{$n}{$indent}" . '<div id="dropdown-menu-' . $item_id . '" class="collapse" style>' . "<ul$class_names" . ">{$n}";
        }
        // end inject -------------------------------------------------------------------------------------------
    }

    public function end_lvl(&$output, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = str_repeat($t, $depth);

        // inject -------------------------------------------------------------------------------------------
        if ($depth == 0) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth == 1) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        if ($depth >= 2) {
            $output .= "$indent</ul>{$n}" . "</div>";
        }
        // end inject -------------------------------------------------------------------------------------------


    }

    public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }
        $indent = ($depth) ? str_repeat($t, $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // inject class -----------------------------------------------------------------------------------------------------------
        array_push($classes, "");
        // inject class -----------------------------------------------------------------------------------------------------------

        // inject class if has children -------------------------------------------------------------------------------------------
        if ($args->walker->has_children) {
            if ($depth == 0) {
                array_push($classes, "tf-quicklink-item");
            }
            if ($depth == 1) {
                array_push($classes, "");
            }
            if ($depth == 2) {
                array_push($classes, "");
            }
        } else {
            if ($depth == 0) {
                array_push($classes, "tf-quicklink-item");
            }
            if ($depth == 1) {
                array_push($classes, "");
            }
            if ($depth == 2) {
                array_push($classes, "");
            }
        }
        // inject class if has children -------------------------------------------------------------------------------------------

        // check active menu --------------------------------------------------------------------------------------------------------
        if (in_array('current-menu-item', $classes)) {
            array_push($classes, "");
        }
        // check active menu --------------------------------------------------------------------------------------------------------

        $args = apply_filters('nav_menu_item_args', $args, $item, $depth);

        $class_names = implode(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args, $depth));
        $class_names = $class_names ? ' class=" ' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args, $depth);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names . '>';

        $atts = array();
        $atts['title'] = !empty($item->attr_title) ? $item->attr_title : '';
        $atts['target'] = !empty($item->target) ? $item->target : '';
        if ('_blank' === $item->target && empty($item->xfn)) {
            $atts['rel'] = 'noopener';
        } else {
            $atts['rel'] = $item->xfn;
        }
        $atts['href'] = !empty($item->url) ? $item->url : '';
        $atts['aria-current'] = $item->current ? 'page' : '';
        $atts = apply_filters('nav_menu_link_attributes', $atts, $item, $args, $depth);

        $attributes = '';
        foreach ($atts as $attr => $value) {
            if (is_scalar($value) && '' !== $value && false !== $value) {
                $value = ('href' === $attr) ? esc_url($value) : esc_attr($value);
                $attributes .= ' ' . $attr . '="' . $value . '"';
            }
        }

        /** This filter is documented in wp-includes/post-template.php */
        $title = apply_filters('the_title', $item->title, $item->ID);
        $title = apply_filters('nav_menu_item_title', $title, $item, $args, $depth);

        // inject class to link ------------------------------------------------------------------------------------------------------
        // inject class if has children
        $classeslink = 'class=""';
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
            if ($depth == 1) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
            if ($depth == 2) {
                $classeslink = 'class="mb-menu-link current collapsed"';
            }
        } else {
            if ($depth == 0) {
                $classeslink = 'class="mb-menu-link current"';
            }
            if ($depth == 1) {
                $classeslink = 'class="mb-menu-link current"';
            }
            if ($depth == 2) {
                $classeslink = 'class="mb-menu-link current"';
            }
        }
        // inject class to link ------------------------------------------------------------------------------------------------------
        $item_output = $args->before;


        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject style
        $args->current_item_id = $item->ID;
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 1) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 2) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
            if ($depth == 3) {
                $item_output .= '<a ' . $attributes . $classeslink . ' data-bs-toggle="collapse" aria-expanded="false" aria-controls="dropdown-menu-five" data-bs-target="#dropdown-menu-' . esc_attr($item->ID) . '">';
            }
        } else {
            $item_output .= '<a ' . $attributes . $classeslink . '>';
        }
        // inject style to link ------------------------------------------------------------------------------------------------------

        $item_output .= $args->link_before . $title . $args->link_after;

        // inject after to link ------------------------------------------------------------------------------------------------------
        // inject after if has children
        if ($args->walker->has_children) {
            if ($depth == 0) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 1) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 2) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
            if ($depth == 3) {
                $item_output .= '';
                $item_output .= '</span><span class="btn-open-sub"></span></a>';
            }
        } else {
            $item_output .= '';
            $item_output .= '</a>';
        }
        // inject class to link ------------------------------------------------------------------------------------------------------


        $item_output .= $args->after;
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    public function end_el(&$output, $item, $depth = 0, $args = null)
    {
        if (isset($args->item_spacing) && 'discard' === $args->item_spacing) {
            $t = '';
            $n = '';
        } else {
            $t = "\t";
            $n = "\n";
        }

        $output .= "</li>{$n}";
    }
}

// Option Page
if (function_exists('acf_add_options_page')) {

    acf_add_options_page(
        array(
            'page_title' => 'Theme General Settings',
            'menu_title' => 'Theme Settings',
            'menu_slug' => 'theme-general-settings',
            'capability' => 'edit_posts',
            'redirect' => false
        )
    );
}

//next prev post link
function wpdocs_add_post_link($html)
{
    $html = str_replace('<a ', '<a class="font-haas_mediu" ', $html);
    return $html;
}
add_filter('next_post_link', 'wpdocs_add_post_link');
add_filter('previous_post_link', 'wpdocs_add_post_link');


// woocommerce function

function ajaxfrontend()
{
    ?>
    <script type="text/javascript">
        var ajax_object = {
            "ajaxurl": "<?php echo admin_url('admin-ajax.php'); ?>"
        };
    </script>
    <?php
}
add_action('wp_head', 'ajaxfrontend');

function ajax_showquickadd()
{
    // Get the product ID from the request
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    if ($product_id > 0) {
        // Load product details
        $product = wc_get_product($product_id);

        // Only proceed if the product exists
        if ($product) {
            $product_price = $product->get_price(); // Get the base price of the product
            ?>

            <div class="modal-content" id="quick-add-to-cart-content" data-pg-collapsed>
                <div class="header">
                    <span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                </div>
                <div class="font-haas_mediu wrap">
                    <div class="tf-product-info-item pe-3">
                        <div class="image">
                            <?php echo wp_get_attachment_image($product->get_image_id(), 'medium'); ?>
                        </div>
                        <div class="content">
                            <a href="<?php echo get_permalink($product_id); ?>" class="fs-5">
                                <?php echo esc_html($product->get_name()); ?>
                            </a>
                            <div class="tf-product-info-price">
                                <div class="price fs-6" id="base-price"><?php echo wc_price($product_price); ?></div>
                            </div>
                        </div>
                    </div>

                    <div class="tf-product-info-variant-picker mb-3">
                        <div class="variant-picker-item">
                            <form id="variantform">
                                <div class="d-flex flex-row gap-15">

                                    <?php
                                    if ($product->is_type('variable')) {
                                        // Product is variable
                                        $attributes = $product->get_variation_attributes();
                                        $variations = $product->get_available_variations();
                                        // Store variation prices
                                        $variation_prices = [];
                                        foreach ($variations as $variation) {
                                            $variation_id = $variation['variation_id'];
                                            $attribute_settings = [];

                                            // Save each attribute's values
                                            foreach ($variation['attributes'] as $attribute_name => $attribute_value) {
                                                $attribute_settings[$attribute_name] = $attribute_value;
                                            }

                                            // Save price and image for this variation
                                            $variation_prices[implode("_", $attribute_settings)] = [
                                                'price' => $variation['display_price'], // Updated price based on variation
                                                'image' => $variation['image']['src'],
                                                'product_id' => $variation['variation_id'],
                                                'data' => $attribute_settings
                                            ];
                                        }

                                        // Display select input for each attribute
                                        foreach ($attributes as $attribute_name => $options) {
                                            echo '<fieldset class="mb-0  w-100">';
                                            echo '<label class="text-capitalize form-label" for="' . esc_attr($attribute_name) . '">' . wc_attribute_label($attribute_name) . '</label>';
                                            echo '<select class="text-capitalize form-select shadow-none border-dark" aria-label="Default select" style="border-radius: 4px !important;" name="attribute_' . esc_attr($attribute_name) . '" onchange="updatePrice()">';

                                            foreach ($options as $option) {
                                                echo '<option class="text-capitalize" value="' . esc_attr($option) . '">' . esc_html($option) . '</option>';
                                            }

                                            echo '</select>';
                                            echo '</fieldset>';
                                        }
                                    }
                                    ?>

                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="tf-product-info-quantity mb_15" data-pg-collapsed>
                        <div class="quantity-title fw-6">Quantity</div>
                        <div class="d-flex flex-row w-100 gap-15">
                            <div class="w-100">
                                <div class="wg-quantity w-100">
                                    <span class="btn-quantity minus-btn" onclick="updateQuantity(-1)">-</span>
                                    <input type="text" id="product-quantity" name="number" value="1" oninput="updatePrice()">
                                    <span class="btn-quantity plus-btn" onclick="updateQuantity(1)">+</span>
                                </div>
                            </div>
                            <div class="w-100">
                                <?php
                                if ($product && $product->is_type('simple')) {
                                    ?>
                                    <a data-quantity="1" href="javascript:void(0);"
                                        class="w-100 tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart"
                                        id="buy-it-now" data-product-id="<?php echo esc_attr($product_id); ?>"
                                        data-product-price="<?php echo esc_attr($product_price); ?>">
                                        <span>Buy it Now &nbsp;</span>
                                        <span class="ecomus-svg-icon ecomus-svg-icon--arrow-top"><svg aria-hidden="true" role="img"
                                                focusable="false" xmlns="http://www.w3.org/2000/svg" width="8" height="8"
                                                viewBox="0 0 8 8" fill="currentColor" class="hdt-inline-block hdt-icon hdt-icon-2">
                                                <path
                                                    d="M0.861539 8L0 7.13846L5.90769 1.23077H0.615385V0H8V7.38462H6.76923V2.09231L0.861539 8Z"
                                                    fill="currentColor"></path>
                                            </svg></span>
                                    </a>
                                    <?php
                                }
                                if ($product && $product->is_type('variable')) {
                                    ?>
                                    <a data-quantity="1" href="javascript:void(0);"
                                        class="w-100 tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart"
                                        id="buy-it-now" data-product-id="<?php echo esc_attr($product_id); ?>"
                                        data-product-price="<?php echo esc_attr($product_price); ?>">
                                        <span>Buy it Now &nbsp;</span>
                                        <span class="ecomus-svg-icon ecomus-svg-icon--arrow-top"><svg aria-hidden="true" role="img"
                                                focusable="false" xmlns="http://www.w3.org/2000/svg" width="8" height="8"
                                                viewBox="0 0 8 8" fill="currentColor" class="hdt-inline-block hdt-icon hdt-icon-2">
                                                <path
                                                    d="M0.861539 8L0 7.13846L5.90769 1.23077H0.615385V0H8V7.38462H6.76923V2.09231L0.861539 8Z"
                                                    fill="currentColor"></path>
                                            </svg></span>
                                    </a>
                                    <?php
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    <div id="cart-alert" class="mb_15" style="display: none;"><strong>Success!</strong> Product added to cart.</div>
                    <div class="tf-product-info-buy-button" data-pg-collapsed>
                        <form>
                            <?php
                            if ($product && $product->is_type('simple')) {
                                ?>
                                <a onclick="addtocartlink(event)" data-quantity="1" href="#"
                                    class="addtocart-link tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart add_to_cart_button ajax_add_to_cart"
                                    id="add-to-cart-button" data-product-id="<?php echo esc_attr($product_id); ?>"
                                    data-product-price="<?php echo esc_attr($product_price); ?>">
                                    <span>Add to cart -&nbsp;</span>
                                    <span class="tf-qty-price" id="total-price"><?php echo wc_price($product_price); ?></span>
                                </a>
                                <?php
                            }
                            if ($product && $product->is_type('variable')) {
                                ?>
                                <a onclick="addtocartlink(event)" data-quantity="1" href="#"
                                    class="addtocart-link tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart add_to_cart_button ajax_add_to_cart"
                                    id="add-to-cart-button" data-product-id="<?php echo esc_attr($product_id); ?>"
                                    data-product-price="<?php echo esc_attr($product_price); ?>">
                                    <span>Add to cart -&nbsp;</span>
                                    <span class="tf-qty-price" id="total-price"><?php echo wc_price($product_price); ?></span>
                                </a>
                                <?php
                            }
                            ?>
                        </form>
                    </div>
                </div>
            </div>

            <script>
                var variationPrices = <?php echo json_encode($variation_prices); ?>; // Get price data from PHP

                function updatePrice() {
                    // Get quantity value
                    var quantity = parseInt(document.getElementById('product-quantity').value);
                    var totalPrice = quantity * getTotalPrice()[0]; // Calculate total price
                    document.getElementById('add-to-cart-button').setAttribute('data-product-id', getTotalPrice()[1]);


                    // Update the total price element
                    document.getElementById('total-price').innerText = 'Rp' + totalPrice.toLocaleString('id-ID', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    });
                    addtocart()
                }

                function getTotalPrice() {
                    var selectedOptions = {};
                    var price = parseFloat(<?php echo json_encode($product_price); ?>); // Default to base price
                    var selectedKey = ""; // For variable products

                    if (variationPrices == null) {
                        price = <?php echo json_encode($product_price); ?>;
                        product_id = <?php echo json_encode($product_id); ?>;
                    } else {
                        // Gather selected attributes
                        document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                            selectedOptions[select.name] = select.value;
                        });

                        // Create the selected key
                        if (Object.keys(selectedOptions).length > 0) {
                            selectedKey = Object.values(selectedOptions).join("_"); // Create the selected key
                        }

                        Object.entries(variationPrices).forEach(([name, value]) => {
                            // Check if the variationKeyParts match the baseKeyParts
                            if (selectedKey.includes(name)) {
                                price = value.price; // Get matched price
                                product_id = value.product_id
                            }
                        });

                        // Check if the designated key exists in variationPrices
                        if (variationPrices[selectedOptions]) {
                            price = variationPrices[selectedOptions].price; // Use price from the color selection
                            product_id = variationPrices[selectedOptions].id;
                        }
                    }

                    return [price, product_id]; // Return the found price or base price
                }


                function updateQuantity(delta) {
                    var quantityInput = document.getElementById('product-quantity');
                    var addtocartbtn = document.getElementById('add-to-cart-button');
                    var currentQuantity = parseInt(quantityInput.value);
                    var newQuantity = currentQuantity + delta;

                    // Ensure quantity does not go below 1
                    if (newQuantity < 1) newQuantity = 1;

                    quantityInput.value = newQuantity;
                    addtocartbtn.setAttribute('data-quantity', newQuantity);
                    updatePrice(); // Update total price after quantity change
                }

                // Attach event listeners to all selects for attribute changes
                document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                    select.addEventListener('change', updatePrice);
                });
                updatePrice();

                function addtocart() {
                    var addtocartbtn = document.getElementById('add-to-cart-button');
                    var buyitnow = document.getElementById('buy-it-now');

                    // Base URL for adding to the cart
                    var url = '<?php echo home_url(); ?>/?add-to-cart=' + addtocartbtn.dataset.productId + '&quantity=' + addtocartbtn.dataset.quantity;

                    // Collect selected attributes
                    var selectedAttributes = [];

                    // Loop through each select input that holds product attributes
                    document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                        var attributeValue = select.value;
                        var attributeName = select.name; // e.g. attribute_pa_color

                        if (attributeValue) {
                            // Append the attribute to the URL
                            selectedAttributes.push(encodeURIComponent(attributeName) + '=' + encodeURIComponent(attributeValue));
                        }
                    });

                    // Append the attributes to the URL if any are selected
                    if (selectedAttributes.length > 0) {
                        url += '&' + selectedAttributes.join('&'); // Combine all attributes into the URL
                    }

                    // Prepare buy URL
                    var buyurl = '?add-to-cart=' + addtocartbtn.dataset.productId + '&quantity=' + addtocartbtn.dataset.quantity;
                    if (selectedAttributes.length > 0) {
                        buyurl += '&' + selectedAttributes.join('&'); // Include attributes
                    }

                    // Set the data-link attribute for the add to cart button
                    addtocartbtn.setAttribute('data-link', url);

                    // Set the href for the buy it now button
                    buyitnow.setAttribute('href', 'checkout/' + buyurl);
                }

            </script>
            <?php
        }
    }
    // End AJAX request
    wp_die();
}
add_action('wp_ajax_showquickadd', 'ajax_showquickadd');
add_action('wp_ajax_nopriv_showquickadd', 'ajax_showquickadd');

function ajax_showquickview()
{
    // Get the product ID from the request
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    if ($product_id > 0) {
        // Load product details
        $product = wc_get_product($product_id);

        // Only proceed if the product exists
        if ($product) {
            $product_price = $product->get_price(); // Get the base price of the product
            ?>

            <div class="modal-content" id="quick-view-content" data-pg-collapsed>
                <div class="header">
                    <span class="icon-close icon-close-popup" data-bs-dismiss="modal"></span>
                </div>
                <div class="wrap">
                    <div class="tf-product-media-wrap">
                        <div class="swiper tf-single-slide">
                            <div class="swiper-wrapper">
                                <div class="swiper-slide" data-pg-collapsed>
                                    <div class="item">
                                        <!-- Menampilkan gambar utama produk -->
                                        <?php echo wp_get_attachment_image($product->get_image_id(), 'large'); ?>
                                    </div>
                                </div>
                                <?php
                                // Mendapatkan ID dari gambar galeri produk
                                $gallery_image_ids = $product->get_gallery_image_ids();

                                // Memeriksa apakah ada gambar di galeri
                                if ($gallery_image_ids) {
                                    // Loop melalui gambar galeri dan menampilkannya
                                    foreach ($gallery_image_ids as $gallery_image_id) {
                                        ?>
                                        <div class="swiper-slide" data-pg-collapsed>
                                            <div class="item">
                                                <!-- Menampilkan gambar dari galeri -->
                                                <?php echo wp_get_attachment_image($gallery_image_id, 'large'); ?>
                                            </div>
                                        </div>
                                        <?php
                                    }
                                }
                                ?>

                            </div>
                            <div class="swiper-button-next button-style-arrow single-slide-prev"></div>
                            <div class="swiper-button-prev button-style-arrow single-slide-next"></div>
                        </div>
                    </div>
                    <div class="position-relative tf-product-info-wrap">
                        <div class="font-haas_mediu tf-product-info-list">
                            <div class="tf-product-info-title">
                                <h5>
                                    <a class="font-druk link"
                                        href="<?php echo get_permalink($product_id); ?>"><?php echo esc_html($product->get_name()); ?></a>
                                    </a>
                                </h5>
                            </div>
                            <div class="tf-product-info-badges font-haas_mediu">
                                <?php
                                if ($product->is_on_sale()) {
                                    // Get the sale start and end dates
                                    $sale_start_date = $product->get_date_on_sale_from();
                                    $sale_end_date = $product->get_date_on_sale_to();

                                    // Format the dates
                                    $formatted_start_date = $sale_start_date ? $sale_start_date->date_i18n('F j, Y') : 'Start date not set';
                                    $formatted_end_date = $sale_end_date ? $sale_end_date->date_i18n('F j, Y') : 'No end date';

                                    // Get the regular and sale price
                                    $regular_price = $product->get_regular_price();
                                    $sale_price = $product->get_sale_price();

                                    // Calculate the discount percentage
                                    if ($regular_price && $sale_price) {
                                        $discount_percentage = round((($regular_price - $sale_price) / $regular_price) * 100);

                                        // Output the discount percentage
                                        echo '<div class="product-status-content">';
                                        echo '<i class="icon-lightning"></i>';
                                        echo '<p class="fw-6">Save ' . esc_html($discount_percentage) . '%</p>';
                                        if ($formatted_start_date || $formatted_end_date) {
                                            echo '<i class="icon-lightning"></i>';
                                            echo '<p class="fw-6">Sale period end: ' . esc_html($formatted_end_date);
                                            echo '</p>';
                                        }
                                        echo '</div>';
                                    }
                                } else {
                                    // Output message if the product is not on sale
                                    echo '<div class="product-status-content">';
                                    echo '<i class="icon-lightning"></i>';
                                    echo '<p class="fw-6">Grab fast! ' . rand(5, 10) . ' people have this in their carts.</p>';
                                    echo '</div>';
                                }
                                ?>
                            </div>
                            <div class="tf-product-info-price">
                                <div class="price font-haas_mediu"><?php echo wc_price($product_price); ?></div>
                            </div>
                            <div class="tf-product-description">
                                <?php
                                $short_description = $product->get_short_description();
                                if (!empty($short_description)) {
                                    echo wpautop($short_description); // wpautop adds paragraph tags around the text
                                } else {
                                    echo '<p>No short description available.</p>';
                                }
                                ?>
                            </div>

                            <div class="tf-product-info-variant-picker mb-3">
                                <div class="variant-picker-item">
                                    <form id="variantform">
                                        <div class="d-flex flex-row gap-15">

                                            <?php
                                            if ($product->is_type('variable')) {
                                                // Product is variable
                                                $attributes = $product->get_variation_attributes();
                                                $variations = $product->get_available_variations();
                                                // Store variation prices
                                                $variation_prices = [];
                                                foreach ($variations as $variation) {
                                                    $variation_id = $variation['variation_id'];
                                                    $attribute_settings = [];

                                                    // Save each attribute's values
                                                    foreach ($variation['attributes'] as $attribute_name => $attribute_value) {
                                                        $attribute_settings[$attribute_name] = $attribute_value;
                                                    }

                                                    // Save price and image for this variation
                                                    $variation_prices[implode("_", $attribute_settings)] = [
                                                        'price' => $variation['display_price'], // Updated price based on variation
                                                        'image' => $variation['image']['src'],
                                                        'product_id' => $variation['variation_id']
                                                    ];
                                                }

                                                // Display select input for each attribute
                                                foreach ($attributes as $attribute_name => $options) {
                                                    echo '<fieldset class="mb-0 w-50 pe-2">';
                                                    echo '<label class="text-capitalize form-label" for="' . esc_attr($attribute_name) . '">' . wc_attribute_label($attribute_name) . '</label>';
                                                    echo '<select class="text-capitalize form-select shadow-none border-dark" aria-label="Default select" style="border-radius: 4px !important;" name="attribute_' . esc_attr($attribute_name) . '" onchange="updatePrice()">';

                                                    foreach ($options as $option) {
                                                        echo '<option class="text-capitalize" value="' . esc_attr($option) . '">' . esc_html($option) . '</option>';
                                                    }

                                                    echo '</select>';
                                                    echo '</fieldset>';
                                                }
                                            }
                                            ?>

                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="tf-product-info-quantity mb_15" data-pg-collapsed>
                                <div class="quantity-title fw-6">Quantity</div>
                                <div class="d-flex flex-row w-100 gap-15">
                                    <div class="w-100">
                                        <div class="wg-quantity w-100">
                                            <span class="btn-quantity minus-btn" onclick="updateQuantity(-1)">-</span>
                                            <input type="text" id="product-quantity" name="number" value="1"
                                                oninput="updatePrice()">
                                            <span class="btn-quantity plus-btn" onclick="updateQuantity(1)">+</span>
                                        </div>
                                    </div>
                                    <div class="w-100">
                                        <?php
                                        if ($product && $product->is_type('simple')) {
                                            ?>
                                            <a data-quantity="1" href="javascript:void(0);"
                                                class="w-100 tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart"
                                                id="buy-it-now" data-product-id="<?php echo esc_attr($product_id); ?>"
                                                data-product-price="<?php echo esc_attr($product_price); ?>">
                                                <span>Buy it Now &nbsp;</span>
                                                <span class="ecomus-svg-icon ecomus-svg-icon--arrow-top"><svg aria-hidden="true"
                                                        role="img" focusable="false" xmlns="http://www.w3.org/2000/svg" width="8"
                                                        height="8" viewBox="0 0 8 8" fill="currentColor"
                                                        class="hdt-inline-block hdt-icon hdt-icon-2">
                                                        <path
                                                            d="M0.861539 8L0 7.13846L5.90769 1.23077H0.615385V0H8V7.38462H6.76923V2.09231L0.861539 8Z"
                                                            fill="currentColor"></path>
                                                    </svg></span>
                                            </a>
                                            <?php
                                        }
                                        if ($product && $product->is_type('variable')) {
                                            ?>
                                            <a data-quantity="1" href="javascript:void(0);"
                                                class="w-100 tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart"
                                                id="buy-it-now" data-product-id="<?php echo esc_attr($variation_id); ?>"
                                                data-product-price="<?php echo esc_attr($product_price); ?>">
                                                <span>Buy it Now &nbsp;</span>
                                                <span class="ecomus-svg-icon ecomus-svg-icon--arrow-top"><svg aria-hidden="true"
                                                        role="img" focusable="false" xmlns="http://www.w3.org/2000/svg" width="8"
                                                        height="8" viewBox="0 0 8 8" fill="currentColor"
                                                        class="hdt-inline-block hdt-icon hdt-icon-2">
                                                        <path
                                                            d="M0.861539 8L0 7.13846L5.90769 1.23077H0.615385V0H8V7.38462H6.76923V2.09231L0.861539 8Z"
                                                            fill="currentColor"></path>
                                                    </svg></span>
                                            </a>
                                            <?php
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <div id="cart-alert" class="mb_15" style="display: none;"><strong>Success!</strong> Product added to
                                cart.</div>
                            <div class="tf-product-info-buy-button" data-pg-collapsed>
                                <form>
                                    <?php
                                    if ($product && $product->is_type('simple')) {
                                        ?>
                                        <a onclick="addtocartlink(event)" data-quantity="1" href="#"
                                            class="addtocart-link tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart add_to_cart_button ajax_add_to_cart"
                                            id="add-to-cart-button" data-product-id="<?php echo esc_attr($product_id); ?>"
                                            data-product-price="<?php echo esc_attr($product_price); ?>">
                                            <span>Add to cart -&nbsp;</span>
                                            <span class="tf-qty-price" id="total-price"><?php echo wc_price($product_price); ?></span>
                                        </a>
                                        <?php
                                    }
                                    if ($product && $product->is_type('variable')) {
                                        ?>
                                        <a onclick="addtocartlink(event)" data-quantity="1" href="#"
                                            class="addtocart-link tf-btn btn-fill justify-content-center fw-6 fs-16 flex-grow-1 animate-hover-btn btn-add-to-cart add_to_cart_button ajax_add_to_cart"
                                            id="add-to-cart-button" data-product-id="<?php echo esc_attr($product_id); ?>"
                                            data-product-price="<?php echo esc_attr($product_price); ?>">
                                            <span>Add to cart -&nbsp;</span>
                                            <span class="tf-qty-price" id="total-price"><?php echo wc_price($product_price); ?></span>
                                        </a>
                                        <?php
                                    }
                                    ?>
                                </form>
                            </div>

                            <div class="mb_15">
                                <a href="<?php echo get_permalink($product_id); ?>"
                                    class="tf-btn fw-6 btn-line font-haas_mediu">View full details<i
                                        class="icon icon-arrow1-top-left"></i></a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <script>
                if ($(".tf-single-slide").length > 0) {
                    var swiper = new Swiper(".tf-single-slide", {
                        slidesPerView: 1,
                        spaceBetween: 0,
                        navigation: {
                            clickable: true,
                            nextEl: ".single-slide-prev",
                            prevEl: ".single-slide-next",
                        },
                    });
                }

                var variationPrices = <?php echo json_encode($variation_prices); ?>; // Get price data from PHP

                function updatePrice() {
                    // Get quantity value
                    var quantity = parseInt(document.getElementById('product-quantity').value);
                    var totalPrice = quantity * getTotalPrice()[0]; // Calculate total price
                    document.getElementById('add-to-cart-button').setAttribute('data-product-id', getTotalPrice()[1]);


                    // Update the total price element
                    document.getElementById('total-price').innerText = 'Rp' + totalPrice.toLocaleString('id-ID', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    });
                    addtocart()
                }

                function getTotalPrice() {
                    var selectedOptions = {};
                    var price = parseFloat(<?php echo json_encode($product_price); ?>); // Default to base price
                    var selectedKey = ""; // For variable products

                    if (variationPrices == null) {
                        price = <?php echo json_encode($product_price); ?>;
                        product_id = <?php echo json_encode($product_id); ?>;
                    } else {
                        // Gather selected attributes
                        document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                            selectedOptions[select.name] = select.value;
                        });

                        // Create the selected key
                        if (Object.keys(selectedOptions).length > 0) {
                            selectedKey = Object.values(selectedOptions).join("_"); // Create the selected key
                        }

                        Object.entries(variationPrices).forEach(([name, value]) => {
                            // Check if the variationKeyParts match the baseKeyParts
                            if (selectedKey.includes(name)) {
                                price = value.price; // Get matched price
                            }
                        });

                        // Check if the designated key exists in variationPrices
                        if (variationPrices[selectedOptions]) {
                            price = variationPrices[selectedOptions].price; // Use price from the color selection
                            product_id = variationPrices[selectedOptions].id;
                        }
                    }
                    return [price, product_id]; // Return the found price or base price
                }


                function updateQuantity(delta) {
                    var quantityInput = document.getElementById('product-quantity');
                    var addtocartbtn = document.getElementById('add-to-cart-button');
                    var currentQuantity = parseInt(quantityInput.value);
                    var newQuantity = currentQuantity + delta;

                    // Ensure quantity does not go below 1
                    if (newQuantity < 1) newQuantity = 1;

                    quantityInput.value = newQuantity;
                    addtocartbtn.setAttribute('data-quantity', newQuantity);
                    updatePrice(); // Update total price after quantity change
                }

                // Attach event listeners to all selects for attribute changes
                document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                    select.addEventListener('change', updatePrice);
                });
                updatePrice();

                function addtocart() {
                    var addtocartbtn = document.getElementById('add-to-cart-button');
                    var buyitnow = document.getElementById('buy-it-now');

                    // Base URL for adding to the cart
                    var url = '<?php echo home_url(); ?>/?add-to-cart=' + addtocartbtn.dataset.productId + '&quantity=' + addtocartbtn.dataset.quantity;

                    // Collect selected attributes
                    var selectedAttributes = [];

                    // Loop through each select input that holds product attributes
                    document.querySelectorAll('select[name^="attribute_"]').forEach(function (select) {
                        var attributeValue = select.value;
                        var attributeName = select.name; // e.g. attribute_pa_color

                        if (attributeValue) {
                            // Append the attribute to the URL
                            selectedAttributes.push(encodeURIComponent(attributeName) + '=' + encodeURIComponent(attributeValue));
                        }
                    });

                    // Append the attributes to the URL if any are selected
                    if (selectedAttributes.length > 0) {
                        url += '&' + selectedAttributes.join('&'); // Combine all attributes into the URL
                    }

                    // Prepare buy URL
                    var buyurl = '?add-to-cart=' + addtocartbtn.dataset.productId + '&quantity=' + addtocartbtn.dataset.quantity;
                    if (selectedAttributes.length > 0) {
                        buyurl += '&' + selectedAttributes.join('&'); // Include attributes
                    }

                    // Set the data-link attribute for the add to cart button
                    addtocartbtn.setAttribute('data-link', url);

                    // Set the href for the buy it now button
                    buyitnow.setAttribute('href', 'checkout/' + buyurl);
                }

            </script>
            <?php
        }
    }
    // End AJAX request
    wp_die();
}
add_action('wp_ajax_showquickview', 'ajax_showquickview');
add_action('wp_ajax_nopriv_showquickview', 'ajax_showquickview');


function remove_from_cart()
{
    // Periksa apakah ID produk dikirim
    if (isset($_POST['product_id'])) {
        $product_id = intval($_POST['product_id']);

        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            // Periksa apakah ID produk atau ID variasi sama
            if ($cart_item['product_id'] == $product_id || $cart_item['variation_id'] == $product_id) {
                WC()->cart->remove_cart_item($cart_item_key);
            }
        }

        // Kirim respons sukses
        wp_send_json_success();
    } else {
        // Kirim respons error jika ID produk tidak ada
        wp_send_json_error('Product ID not provided.');
    }
}


add_action('wp_ajax_remove_from_cart', 'remove_from_cart');
add_action('wp_ajax_nopriv_remove_from_cart', 'remove_from_cart'); // Untuk pengguna yang belum login

function get_cart_count()
{
    $cart_count = WC()->cart->get_cart_contents_count();
    echo json_encode(array('cart_count' => $cart_count));
    wp_die(); // Required to terminate immediately and return a response
}

add_action('wp_ajax_get_cart_count', 'get_cart_count');
add_action('wp_ajax_nopriv_get_cart_count', 'get_cart_count');

// Fungsi untuk merender ulang konten keranjang
function render_mini_cart_ajax()
{
    ob_start(); // Mulai output buffering

    // Periksa apakah ada item dalam keranjang
    if (WC()->cart->get_cart_contents_count() > 0) {
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
            // Ambil informasi produk
            $product = $cart_item['data'];
            $product_id = $product->get_id();
            $product_name = $product->get_name();
            $product_price = $product->get_price();
            $product_variation = isset($cart_item['variation']) ? $cart_item['variation'] : [];
            $product_image_id = $product->get_image_id();
            $product_image = wp_get_attachment_image($product_image_id, 'medium'); // Ganti ukuran jika perlu
            $quantity = $cart_item['quantity'];

            // Jika produk memiliki variasi, ambil nama variasi
            $variation_name = '';
            if (!empty($product_variation)) {
                foreach ($product_variation as $attribute_name => $attribute_value) {
                    $variation_name .= wc_attribute_label($attribute_name) . ': ' . esc_html($attribute_value) . ' ';
                }
            }

            // Tampilkan produk dalam keranjang
            ?>
            <div class="tf-mini-cart-item" data-product-id="<?php echo esc_attr($product_id); ?>" data-pg-collapsed>
                <div class="tf-mini-cart-image"> <a href="<?php echo esc_url(get_permalink($product_id)); ?>">
                        <?php echo $product_image; ?> </a> </div>
                <div class="tf-mini-cart-info">
                    <a class="title link"
                        href="<?php echo esc_url(get_permalink($product_id)); ?>"><?php echo esc_html($product_name); ?></a>
                    <div class="price fw-6"><?php echo wp_kses_post(wc_price($product_price)); ?></div>
                    <div class="tf-mini-cart-btns">
                        <div class="wg-quantity small">
                            <span class="btn-quantity minus-btn" data-product-id="<?php echo esc_attr($product_id); ?>">-</span>
                            <input type="text" name="number" value="<?php echo esc_attr($quantity); ?>">
                            <span class="btn-quantity plus-btn" data-product-id="<?php echo esc_attr($product_id); ?>">+</span>
                        </div>
                        <a class="tf-mini-cart-remove" data-product-id="<?php echo esc_attr($product_id); ?>">Remove</a>
                    </div>
                </div>
            </div>
            <?php
        }
    } else {
        echo '<div class="tf-mini-cart-item">';
        echo '<p class="ps-3">Your cart is empty.</p>';
        echo '</div>';
    }

    $output = ob_get_clean(); // Dapatkan output dan bersihkan buffer
    wp_send_json_success($output); // Kirim output dalam format JSON
}

// Hook fungsi ke AJAX
add_action('wp_ajax_render_mini_cart', 'render_mini_cart_ajax');
add_action('wp_ajax_nopriv_render_mini_cart', 'render_mini_cart_ajax');


// Fungsi untuk mengembalikan subtotal keranjang melalui AJAX
function get_cart_subtotal_ajax()
{
    // Periksa jika keranjang tidak kosong
    if (WC()->cart->get_cart_contents_count() > 0) {
        $subtotal = WC()->cart->get_cart_subtotal();
        wp_send_json_success($subtotal);
    } else {
        wp_send_json_success('0');
    }

    wp_die(); // Hentikan eksekusi setelah mengirim respon AJAX
}
add_action('wp_ajax_get_cart_subtotal', 'get_cart_subtotal_ajax');
add_action('wp_ajax_nopriv_get_cart_subtotal', 'get_cart_subtotal_ajax');

// Fungsi untuk memperbarui kuantitas produk di keranjang
function update_cart_quantity()
{
    if (isset($_POST['product_id']) && isset($_POST['quantity'])) {
        $product_id = intval($_POST['product_id']);
        $quantity = intval($_POST['quantity']);

        // Memastikan kuantitas tidak kurang dari 1
        if ($quantity < 1) {
            wp_send_json_error('Quantity must be at least 1.');
            wp_die();
        }

        // Cek apakah produk sudah ada di keranjang
        foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {

            if ($cart_item['product_id'] == $product_id or $cart_item['variation_id'] == $product_id) {
                // Update kuantitas produk
                WC()->cart->set_quantity($cart_item_key, $quantity, true); // Menambahkan refresh_totals ke true
                break;
            }
        }

        // Menghitung subtotal terbaru
        $cart_subtotal = WC()->cart->get_cart_subtotal();
        $cart_count = WC()->cart->get_cart_contents_count(); // Jumlah item di keranjang

        // Mengirimkan subtotal dan jumlah item sebagai respon
        wp_send_json_success(array(
            'cart_subtotal' => $cart_subtotal,
            'cart_count' => $cart_count,
            'message' => 'Quantity updated',
            'data' => [$product_id, $hasil]
        ));
    } else {
        wp_send_json_error('Invalid product ID or quantity.');
    }

    wp_die();
}

add_action('wp_ajax_update_cart_quantity', 'update_cart_quantity');
add_action('wp_ajax_nopriv_update_cart_quantity', 'update_cart_quantity');

// Product review
function display_product_reviews($num_reviews = 5, $ratings = [4, 5])
{
    // Get all products
    $args = [
        'post_type' => 'product',
        'posts_per_page' => -1, // Get all products
    ];

    $products = get_posts($args);
    $filtered_reviews = []; // Array to hold reviews with specified ratings

    if ($products) {
        foreach ($products as $product_post) {
            // Ensure we get the WC_Product object
            $product = wc_get_product($product_post->ID); // Convert WP_Post to WC_Product

            // Get the reviews for this product with specified ratings
            $comments = get_comments([
                'post_id' => $product->get_id(), // Ensure we use the product ID
                'status' => 'approve', // Only show approved comments (reviews)
                'type' => 'review', // Only get reviews
                'meta_key' => 'rating', // Filter by rating
                'meta_value' => $ratings, // Ratings to filter
                'meta_compare' => 'IN' // Compare against multiple values
            ]);

            // If the product has reviews, store them
            if ($comments) {
                foreach ($comments as $comment) {
                    $rating = get_comment_meta($comment->comment_ID, 'rating', true); // Get the rating meta
                    // Only accept ratings from the specified array
                    if (in_array($rating, $ratings)) {
                        $filtered_reviews[] = [
                            'reviewer' => esc_html($comment->comment_author),
                            'review_text' => esc_html($comment->comment_content),
                            'review_date' => esc_html(date('F j, Y', strtotime($comment->comment_date))),
                            'rating' => $rating,
                            'product_name' => esc_html($product->get_name()), // Get product name correctly
                        ];
                        // Limit to the maximum specified number of reviews
                        if (count($filtered_reviews) >= $num_reviews) {
                            break 2; // Break both loops if we have required reviews
                        }
                    }
                }
            }
        }

        // Output the filtered reviews using your desired template
        if (!empty($filtered_reviews)) {
            echo '<div class="swiper-wrapper">'; // Swiper wrapper
            foreach ($filtered_reviews as $review) {
                ?>
                <div class="swiper-slide" data-pg-collapsed>
                    <div class="testimonial-item style-box rounded-0 wow fadeInUp" data-wow-delay="0s">
                        <div class="author">
                            <div class="name font-haas_mediu"><?php echo $review['reviewer']; ?></div>
                            <div class="metas font-haas_mediu"><i class="icon-check"></i> Verified customer</div>
                        </div>
                        <div class="rating color-black">
                            <?php
                            // Display stars based on rating
                            for ($i = 0; $i < 5; $i++) {
                                echo '<i class="icon-start"></i>'; // Display star icon
                            }
                            ?>
                        </div>
                        <div class="heading font-haas_mediu"><?php echo esc_html($review['product_name']); ?></div>
                        <div class="text line-clamp-3"><?php echo $review['review_text']; ?></div>
                    </div>
                </div>
                <?php
            }
            echo '</div>'; // Close swiper-wrapper
        } else {
            echo '<p>No reviews found with ratings of ' . implode(', ', $ratings) . '.</p>'; // Message if no matching reviews
        }
    } else {
        echo '<p>No products found.</p>'; // Message if no products exist
    }
}

function enqueue_custom_scripts()
{
    // Enqueue model-viewer as a module script
    wp_enqueue_script_module(
        'model-viewer-script',
        get_theme_file_uri('/js/model-viewer.min.js'),
        array(), // Dependencies (if any)
        null,    // Version number (null means no version)
        true      // Load in footer
    );

    // Enqueue zoom as a module script
    wp_enqueue_script_module(
        'zoom-script',
        get_theme_file_uri('/js/zoom.js'),
        array(), // Dependencies (if any)
        null,    // Version number (null means no version)
        true      // Load in footer
    );

    // Enqueue zoom as a module script
    wp_enqueue_script(
        'custom',
        get_theme_file_uri('/js/custom.js'),
        array(), // Dependencies (if any)
        null,    // Version number (null means no version)
        true      // Load in footer
    );
}
add_action('wp_enqueue_scripts', 'enqueue_custom_scripts');


add_filter('woocommerce_product_query', 'custom_sale_status_filter_post_in');

function custom_sale_status_filter_post_in($q)
{
    if (!empty($_GET['filter_sale_status']) && in_array('onsale', explode(',', $_GET['filter_sale_status']))) {
        // Menggunakan fungsi WooCommerce untuk mendapatkan semua ID produk yang sedang sale
        $product_ids_on_sale = wc_get_product_ids_on_sale();

        // Get current post__in args
        $post__in = $q->get('post__in');

        // If there are other post__in arguments, merge them
        if (!empty($post__in)) {
            $post__in = array_intersect($post__in, $product_ids_on_sale);
        } else {
            $post__in = $product_ids_on_sale;
        }

        // Update the post__in parameter
        $q->set('post__in', $post__in);

        // Penting: Hapus meta_query untuk stock_status jika ada
        $meta_query = $q->get('meta_query');
        if (!empty($meta_query)) {
            foreach ($meta_query as $key => $query) {
                if (isset($query['key']) && $query['key'] === '_stock_status') {
                    unset($meta_query[$key]);
                }
            }
            $q->set('meta_query', $meta_query);
        }
    }
    return $q;
}

function custom_product_search_filter_post_in($q)
{
    if (!empty($_GET['search_product'])) {
        $search_keyword = sanitize_text_field($_GET['search_product']);

        // Query untuk mendapatkan produk berdasarkan kata kunci dengan batasan jumlah produk
        $args = [
            'post_type'      => 'product',
            'post_status'    => 'publish',
            'posts_per_page' => 20, // Batasi hasil pencarian untuk menghindari memory limit
            's'              => $search_keyword,
            'fields'         => 'ids',
            'paged'          => (get_query_var('paged')) ? get_query_var('paged') : 1,
        ];
        
        $query = new WP_Query($args);
        $search_results = wp_list_pluck($query->posts, 'ID');
        wp_reset_postdata();
        
        // Get current post__in args
        $post__in = $q->get('post__in');
        
        // If there are other post__in arguments, merge them
        if (!empty($post__in)) {
            $post__in = array_intersect($post__in, $search_results);
        } else {
            $post__in = $search_results;
        }
        
        // Update the post__in parameter
        $q->set('post__in', $post__in);
    }
    return $q;
}

add_action('pre_get_posts', 'custom_product_search_filter_post_in');


function verify_doku_webhook_signature($headers, $body, $secret_key)
{
    // Mendapatkan nilai dari headers
    $client_id = $headers['client_id'][0] ?? '';
    $request_id = $headers['request_id'][0] ?? '';
    $request_timestamp = $headers['request_timestamp'][0] ?? '';
    $received_signature = $headers['signature'][0] ?? '';

    // Pastikan signature menggunakan format "HMACSHA256="
    if (strpos($received_signature, 'HMACSHA256=') !== 0) {
        return false;
    }

    // Ekstrak nilai signature
    $received_signature = str_replace('HMACSHA256=', '', $received_signature);

    // Hitung Digest Value (hash body payload)
    $digest_value = base64_encode(hash('sha256', $body, true));

    // Buat komponen signature
    $request_target = '/wp-json/doku/v1/notification'; // Ganti dengan path webhook Anda
    $component_signature = "Client-Id:{$client_id}\n" .
        "Request-Id:{$request_id}\n" .
        "Request-Timestamp:{$request_timestamp}\n" .
        "Request-Target:{$request_target}\n" .
        "Digest:{$digest_value}";

    // Hitung HMAC-SHA256 signature
    $calculated_signature = base64_encode(hash_hmac('sha256', $component_signature, $secret_key, true));

    // Bandingkan signature yang dihitung dengan yang diterima
    return hash_equals($calculated_signature, $received_signature);
}

function handle_doku_webhook(WP_REST_Request $request)
{
    $secret_key = 'SK-vu2a7dKO6hGm9Dr1JcIj'; // Ganti dengan Secret Key dari DOKU
    $headers = $request->get_headers();
    $body = $request->get_body();

    // Verifikasi signature
    if (!verify_doku_webhook_signature($headers, $body, $secret_key)) {
        return new WP_REST_Response('Invalid signature ' . $headers['signature'][0], 403);
    }

    // Jika valid, proses payload
    $data = json_decode($body, true);
    if (isset($data['order']['invoice_number'], $data['transaction']['status'])) {
        $invoice_number = sanitize_text_field($data['order']['invoice_number']);
        $transaction_status = sanitize_text_field($data['transaction']['status']);

        if ($transaction_status === 'SUCCESS') {
            $order = wc_get_order($invoice_number);
            if ($order) {
                $order = wc_get_order($order);
                $order->update_status('processing', __('Payment received, order updated via DOKU webhook', 'woocommerce'));
                return new WP_REST_Response('Order status updated successfully', 200);
            }
        }
    }

    return new WP_REST_Response('Invalid data ' . $data['order']['invoice_number'] . '----' . $order, 400);
}


add_action('rest_api_init', function () {
    // https://domain.com/wp-json/doku/v1/notification
    register_rest_route('doku/v1', '/notification', array(
        'methods' => 'POST',
        'callback' => 'handle_doku_webhook',
        'permission_callback' => '__return_true',
    ));
});
