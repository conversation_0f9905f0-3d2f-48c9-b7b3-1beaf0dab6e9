{"files": {"home-skateboard.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.svg.lib", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.insight.events", "pg.image.overlay", "pg.css.grid", "bs5", "pg.html", "pg.components"]}, "wp-index.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.bs5.lib", "pg.insight.events", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "bs5", "pg.html", "pine.cone.lib", "pg.components"]}, "wp-404.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "pg.insight.events", "bs5", "pg.html", "pg.components"]}, "wp-single.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "pg.insight.events", "bs5", "pg.html", "pg.components"]}, "wp-archive.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "pg.insight.events", "wordpress.pinegrow", "bs5", "pg.html", "pine.cone.lib", "pg.components"]}, "wp-page.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "pg.insight.events", "bs5", "pg.html", "pg.components"]}, "wp-search.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "pg.insight.events", "wordpress.pinegrow", "bs5", "pg.html", "pine.cone.lib", "pg.components"]}, "shop-left-sidebar.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.svg.lib", "pg.insight.events", "wordpress.pinegrow", "pg.css.grid", "pg.image.overlay", "pg.code-validator", "pg.project.items", "pg.asset.manager", "bs5", "pg.html", "pg.components"]}, "product-detail.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.svg.lib", "pg.asset.manager", "pg.project.items", "pg.insight.events", "pg.code-validator", "wordpress.pinegrow", "pg.css.grid", "pg.image.overlay", "bs5", "pg.html", "pg.components"]}, "view-cart.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.svg.lib", "pg.asset.manager", "pg.project.items", "pg.insight.events", "pg.code-validator", "wordpress.pinegrow", "pg.css.grid", "pg.image.overlay", "bs5", "pg.html", "pg.components"]}, "wp-cart.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.svg.lib", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.insight.events", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "bs5", "pg.html", "pg.components"]}, "wp-product-single.html": {"frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.insight.events", "pg.image.overlay", "pg.css.grid", "pg.svg.lib", "wordpress.pinegrow", "bs5", "pg.html", "pine.cone.lib", "pg.components"]}, "woocommerce\\cart\\cart.php": {"frameworks": ["pinegrow", "pg.insight.events", "pg.svg.lib", "wordpress.pinegrow", "pg.css.grid", "pg.image.overlay", "pg.php", "pg.code-validator", "pg.project.items", "pg.asset.manager", "pg.html", "pg.components"]}, "wp-shop.html": {"frameworks": ["pinegrow", "pg.bs5.lib", "pg.asset.manager", "pg.project.items", "pg.code-validator", "pg.insight.events", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "wordpress.pinegrow", "bs5", "pg.html", "pg.components"]}}, "open-pages": ["index.html", "wp-index.html"], "urls": {"index.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "home-08.html": {"open-with-wrapper": false}, "home-men.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "home-skateboard.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-index.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "blog-grid.html": {"open-with-wrapper": false}, "blog-list.html": {"open-with-wrapper": false}, "blog-sidebar-right.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "brands-v2.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "checkout.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "compare.html": {"open-with-wrapper": false}, "delivery-return.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "faq-1.html": {"open-with-wrapper": false}, "my-account.html": {"open-with-wrapper": false}, "my-account-wishlist.html": {"open-with-wrapper": false}, "invoice.html": {"open-with-wrapper": false}, "our-store.html": {"open-with-wrapper": false}, "payment-confirmation.html": {"open-with-wrapper": false}, "product-detail.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "product-drawer-sidebar.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "product-grid-1.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "register.html": {"open-with-wrapper": false}, "shop-link.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "shop-default.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "store-locations.html": {"open-with-wrapper": false}, "shop-fullwidth.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "shop-filter-sidebar.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "shop-filter-hidden.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "about-us.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "view-cart.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wishlist.html": {"open-with-wrapper": false}, "terms-conditions.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-404.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}, "blog-detail.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-single.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}, "wp-archive.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-page.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}, "wp-search.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}, "woocommerce/cart/proceed-to-checkout-button.php": {"open-with-wrapper": false}, "woocommerce/cart/cart-empty.php": {"open-with-wrapper": false}, "shop-left-sidebar.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "woocommerce/cart/cart.php": {"open-with-wrapper": false, "open-page-views": [{"w": 1024, "h": 0}]}, "my-account-address.html": {"open-with-wrapper": false}, "wp-cart.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-product-single.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "contact-1.html": {"open-with-wrapper": false}, "functions_backup.php": {"open-with-wrapper": false}, "_pgexport/footer.php": {"open-with-wrapper": false}, "_pgexport/parts/footer.php": {"open-with-wrapper": false}, "_pgexport/index.php": {"open-with-wrapper": false}, "_pgexport/inc/wc_pg_helpers.php": {"open-with-wrapper": false}, "_pgexport/inc/wp_pg_helpers.php": {"open-with-wrapper": false}, "_pgexport/inc/custom.php": {"open-with-wrapper": false}, "product-photoswipe-popup.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "wp-shop.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "shop-collection-sub.html": {"open-with-wrapper": false, "open-page-views": [{"w": 1280, "h": 0}]}, "inc/custom.php": {"open-with-wrapper": false}, "woocommerce/emails/payment-completed-email.php": {"open-with-wrapper": false}, "woocommerce/checkout/form-billing.php": {"open-with-wrapper": false}}, "breakpoints": ["576px", "768px", "850px", "992px", "1025px", "1150px", "1200px", "1400px", "1441px", "1470px", "1600px", "1660px"], "frameworks": ["pinegrow", "pg.asset.manager", "pg.bs5.lib", "pg.project.items", "pg.code-validator", "pg.svg.lib", "pg.image.overlay", "pg.css.grid", "pg.insight.events", "wordpress.pinegrow", "bs5", "pg.html", "pine.cone.lib", "pg.components"], "wp-theme-info": {"dir": "E:\\LOCAL WEBSITE\\cablester\\app\\public\\wp-content\\themes\\cablester", "name": "cablester", "slug": "cablester", "url": "http://rawstudio.id", "project_version": "1.0.871", "auto_increment_version": "1", "page": "wp-index.html", "themejson": "true", "wpseo": "true", "wpjquery": "false", "generator": null, "images": {"1": {"id": "product_carousel", "name": "Product Carousel", "width": "246", "height": "322", "crop": "true"}}, "blocks_type": "native-auto", "pg_version": "8.2", "blocks_editor_styles": [], "project_type": "theme", "blocks_inline_svg": "false", "blocks_load_if_rendered": "false", "block_category_register": "false", "wc_enabled": "true"}, "wp-insight": {"menus": ["footer_menu_1", "footer_menu_2", "footer_menu_3", "mobile_menu", "primary", "quick_menu", "social"], "taxonomies": ["category", "post_tag"], "customizer_sections": [], "customizer_controls": [], "post_types": ["attachment", "page", "post"], "template_parts": ["parts/footer", "parts/header"], "sidebars": [], "master_pages": [], "image_sizes": ["full", "large", "medium", "medium_large", "post-thumbnail", "product_carousel", "thumbnail"], "custom_funcs": [], "block_categories": []}, "wp-files": {"images": "false", "woocommerce/checkout": "true", "woocommerce": "true", "": "true"}, "recent-classes": ["text_white", "bg_dark", "bg_black", "mb-4", "mb-5", "mb-3", "woonotice", "woon<PERSON><PERSON>r", "rounded-0", "border-danger", "border", "border-1", "font-haas_mediu", "mt-5", "pb-0", "center", "align-items-center", "align-content-lg-center", "align-content-center", "text-center", "px-4", "py-3", "p-2", "p-5", "me-2", "me-5", "wraper_selected", "line-clamp-3", "position-static", "z-1", "font-haas_black", "text-decoration-line-through", "font-haas_light", "content-single", "mb-2", "font-druk", "font-haas_thin", "mt_5", "pt-5"], "active-design-provider": "bs5", "ai-project-brief": null}