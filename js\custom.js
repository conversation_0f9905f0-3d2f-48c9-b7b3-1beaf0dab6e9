function quickadd(event, p) {
  event.preventDefault();
  var productId = p;
  // Make AJAX request to load product details
  $.ajax({
    url: ajax_object.ajaxurl, // WooCommerce AJAX URL
    type: "POST",
    data: {
      action: "showquickadd", // The action hook to handle
      product_id: productId,
    },
    success: function (response) {
      // Load the response (product content) into the modal
      $("#quick-view-content").empty();
      $("#quick-add-to-cart-content").empty();
      $("#quick-add-to-cart-content").html(response);
      $("#quick_add").modal("show");
    },
    error: function (err) {
      console.log(err);
    },
  });
}

function quickview(event, p) {
  event.preventDefault();
  var productId = p;
  // Make AJAX request to load product details
  $.ajax({
    url: ajax_object.ajaxurl, // WooCommerce AJAX URL
    type: "POST",
    data: {
      action: "showquickview", // The action hook to handle
      product_id: productId,
    },
    success: function (response) {
      // Load the response (product content) into the modal
      $("#quick-add-to-cart-content").empty();
      $("#quick-view-content").empty();
      $("#quick-view-content").html(response);
      $("#quick_view").modal("show");
    },
    error: function (err) {
      console.log(err);
    },
  });
}

function addtocartlink(event) {
  event.preventDefault(); // Prevent the default link behavior (navigation)
  var url = $("#add-to-cart-button").attr("data-link");
  $.ajax({
    url: url, // WooCommerce AJAX URL
    type: "POST",
    success: function () {
      $.ajax({
        url: ajax_object.ajaxurl, // WooCommerce AJAX URL
        type: "POST",
        data: {
          action: "get_cart_count",
        },
        success: function (response) {
          var data = JSON.parse(response);
          $(".count-box").text(data.cart_count);
          showCartAlert();
          updateMiniCart();
        },
        error: function (err) {
          console.log(err);
        },
      });
    },
    error: function (err) {
      console.log(err);
    },
  });
}

function getcartcount() {
  $.ajax({
    url: ajax_object.ajaxurl, // WooCommerce AJAX URL
    type: "POST",
    data: {
      action: "get_cart_count",
    },
    success: function (response) {
      var data = JSON.parse(response);
      $(".count-box").text(data.cart_count);
      $(".toolbar-count").text(data.cart_count);
    },
    error: function (err) {
      console.log(err);
    },
  });
}

function showCartAlert() {
  var $alertElement = $("#cart-alert");

  // Reset the alert in case it was dismissed previously
  $alertElement.removeClass("show").hide();

  // Show the alert
  $alertElement.show().addClass("show");

  // Automatically hide the alert after 3 seconds (3000 milliseconds)
  setTimeout(function () {
    $alertElement.fadeOut(); // Just hide it instead of closing/destroying it
  }, 3000); // Adjust the time as needed
}

$(document).on("click", ".tf-mini-cart-remove", function () {
  var productId = $(this).data("product-id"); // Ambil ID produk dari data attribute

  $.ajax({
    url: ajax_object.ajaxurl, // URL untuk AJAX
    type: "POST",
    data: {
      action: "remove_from_cart", // Nama action untuk PHP
      product_id: productId, // Kirim ID produk
    },
    success: function (response) {
      // Tindakan yang diambil setelah produk dihapus
      if (response.success) {
        // Hapus item dari DOM jika berhasil
        $('.tf-mini-cart-item[data-product-id="' + productId + '"]').remove();
        // Update subtotal atau total cart jika diperlukan
        updateMiniCart();
      } else {
        alert("Failed to remove the product from the cart.");
      }
    },
    error: function (err) {
      console.log(err);
      alert("An error occurred while removing the product from the cart.");
    },
  });
});

function updateMiniCart() {
  $.ajax({
    url: ajax_object.ajaxurl, // admin-ajax.php URL
    type: "POST",
    data: {
      action: "render_mini_cart", // Aksi yang dipanggil di PHP
    },
    success: function (response) {
      if (response.success) {
        // Ganti konten mini cart dengan hasil AJAX
        $(".tf-mini-cart-items").html(response.data);
        getcartcount();
        updateCartSubtotal();
      } else {
        console.log("Failed to update cart");
      }
    },
    error: function () {
      console.log("Error updating cart");
    },
  });
}

function updateCartSubtotal() {
  $.ajax({
    url: ajax_object.ajaxurl, // URL AJAX WordPress
    type: "POST",
    data: {
      action: "get_cart_subtotal", // Nama action yang sesuai dengan PHP
    },
    success: function (response) {
      if (response.success) {
        // Update subtotal di tempat yang sesuai di HTML
        $("#minicart_subtotal").html(response.data);
      } else {
        $("#minicart_subtotal").text("0");
      }
    },
    error: function () {
      console.log("Error in AJAX request.");
    },
  });
}

// Fungsi untuk memperbarui kuantitas di keranjang
function updateCartQuantity(productId, newQuantity) {
  $.ajax({
    url: ajax_object.ajaxurl, // URL AJAX WordPress
    type: "POST",
    data: {
      action: "update_cart_quantity",
      product_id: productId,
      quantity: newQuantity,
    },
    success: function (response) {
      if (response.success) {
        getcartcount();
        updateCartSubtotal();
      } else {
        alert("Error: " + response.data);
      }
    },
    error: function () {
      alert("Error while updating cart.");
    },
  });
}

// Klik tombol plus
$(document).on(
  "click",
  ".tf-mini-cart-item .btn-quantity.plus-btn",
  function () {
    let productId = $(this).data("product-id");
    let quantityInput = $(this).siblings('input[name="number"]');
    let newQuantity = parseInt(quantityInput.val()) + 1;

    quantityInput.val(newQuantity); // Update nilai input
    updateCartQuantity(productId, newQuantity); // Kirim request AJAX
  }
);

// Klik tombol minus
$(document).on(
  "click",
  ".tf-mini-cart-item .btn-quantity.minus-btn",
  function () {
    let productId = $(this).data("product-id");
    let quantityInput = $(this).siblings('input[name="number"]');
    let currentQuantity = parseInt(quantityInput.val());

    if (currentQuantity > 1) {
      // Pastikan kuantitas tidak kurang dari 1
      let newQuantity = currentQuantity - 1;
      quantityInput.val(newQuantity); // Update nilai input
      updateCartQuantity(productId, newQuantity); // Kirim request AJAX
    }
  }
);

function handleCheckboxChange(taxonomy) {
  const form = document.getElementById("facet-filter-form");
  const checkboxes = form.querySelectorAll(
    `input[class*="tf-check-${taxonomy}"]`
  );
  const filterKey = `filter_${taxonomy.replace("pa_", "")}`;

  // Mengumpulkan nilai checkbox yang dicentang
  const checkedValues = [];
  checkboxes.forEach((checkbox) => {
    if (checkbox.checked) {
      checkedValues.push(checkbox.value);
    }
  });

  // Jika ada nilai yang dicentang, update atau tambahkan parameter
  if (checkedValues.length > 0) {
    const params = new URLSearchParams(window.location.search);
    params.set(filterKey, checkedValues.join(","));
    params.set(`query_type_${taxonomy.replace("pa_", "")}`, "and");

    // Update URL dan reload halaman
    window.location.search = params.toString();
  } else {
    // Jika tidak ada yang dicentang, hapus parameter
    const params = new URLSearchParams(window.location.search);
    params.delete(filterKey);
    params.delete(`query_type_${taxonomy.replace("pa_", "")}`);

    // Update URL dan reload halaman
    window.location.search = params.toString();
  }
}

function clearFilters(taxonomy) {
  const filterKey = `filter_${taxonomy.replace("pa_", "")}`;
  const params = new URLSearchParams(window.location.search);

  // Hapus parameter filter dan query_type
  params.delete(filterKey);
  params.delete(`query_type_${taxonomy.replace("pa_", "")}`);

  // Update URL dan reload halaman
  window.location.search = params.toString();
}

function clearAllFilters() {
  // Menghapus semua parameter dari URL
  window.location.search = "";
}

function handlePriceChange() {
  const rangeInput = document.querySelectorAll(".range-input input");
  const minVal = parseInt(rangeInput[0].value);
  const maxVal = parseInt(rangeInput[1].value);

  // Update URL and refresh page
  const currentUrl = new URL(window.location.href);
  const params = new URLSearchParams(currentUrl.search);

  params.set("min_price", minVal);
  params.set("max_price", maxVal);

  // Update URL
  window.location.search = params.toString();
}

function resetPriceFilter() {
  const currentUrl = new URL(window.location.href);
  const params = new URLSearchParams(currentUrl.search);

  // Remove price parameters
  params.delete("min_price");
  params.delete("max_price");

  // Update URL
  window.location.search = params.toString();
}

$(document).ready(function () {
  $(document)
    .ajaxStart(function () {
      // Tampilkan loading bar
      $("#ajax-loading-bar").css("width", "0").show();

      // Tambahkan overlay untuk mencegah interaksi
      $("body").append(
        '<div id="ajax-overlay" style="position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:9999;"></div>'
      );

      // Animasi loading bar
      setTimeout(function () {
        $("#ajax-loading-bar").css("width", "50%"); // Loading awal
      }, 100);
    })
    .ajaxComplete(function () {
      // Sembunyikan loading bar setelah AJAX selesai
      $("#ajax-loading-bar").css("width", "100%");

      setTimeout(function () {
        $("#ajax-loading-bar").fadeOut();
        // Hapus overlay setelah AJAX selesai
        $("#ajax-overlay").remove();
      }, 500); // Tambahkan sedikit delay agar transisi lebih halus
    });

  // Ketika AJAX selesai
  $(document).ajaxStop(function () {
    $("#ajax-loading-bar").css("width", "100%"); // Selesai loading
    setTimeout(function () {
      $("#ajax-loading-bar").addClass("complete");
      setTimeout(function () {
        $("#ajax-loading-bar").hide().removeClass("complete");
      }, 500); // Waktu untuk menghilangkan garis setelah selesai
    }, 300);
  });
  updateMiniCart();

  if ($(".tf-sw-testimonial").length > 0) {
    // Collect data from the testimonial element
    var preview = $(".tf-sw-testimonial").data("preview");
    var tablet = $(".tf-sw-testimonial").data("tablet");
    var mobile = $(".tf-sw-testimonial").data("mobile");
    var spacingLg = $(".tf-sw-testimonial").data("space-lg");
    var spacingMd = $(".tf-sw-testimonial").data("space-md");

    // Initialize Swiper
    var swiper = new Swiper(".tf-sw-testimonial", {
      slidesPerView: mobile,
      spaceBetween: spacingMd,
      speed: 1000,
      pagination: {
        el: ".sw-pagination-testimonial",
        clickable: true,
      },
      navigation: {
        clickable: true,
        nextEl: ".nav-prev-testimonial",
        prevEl: ".nav-next-testimonial",
      },
      breakpoints: {
        768: {
          slidesPerView: tablet,
          spaceBetween: spacingLg,
        },
        1150: {
          slidesPerView: preview,
          spaceBetween: spacingLg,
        },
      },
    });
  }

  if ($(".tf-sw-product-sell-2").length > 0) {
    var preview = $(".tf-sw-product-sell-2").data("preview");
    var tablet = $(".tf-sw-product-sell-2").data("tablet");
    var mobile = $(".tf-sw-product-sell-2").data("mobile");
    var spacingLg = $(".tf-sw-product-sell-2").data("space-lg");
    var spacingMd = $(".tf-sw-product-sell-2").data("space-md");
    var perGroup = $(".tf-sw-product-sell-2").data("pagination");
    var perGroupMd = $(".tf-sw-product-sell-2").data("pagination-md");
    var perGroupLg = $(".tf-sw-product-sell-2").data("pagination-lg");

    var swiper = new Swiper(".tf-sw-product-sell-2", {
      slidesPerView: mobile,
      spaceBetween: spacingMd,
      speed: 1000,
      pagination: {
        el: ".sw-pagination-product-2",
        clickable: true,
      },
      slidesPerGroup: perGroup,
      navigation: {
        clickable: true,
        nextEl: ".nav-prev-product-2",
        prevEl: ".nav-next-product-2",
      },
      breakpoints: {
        768: {
          slidesPerView: tablet,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupMd,
        },
        1150: {
          slidesPerView: preview,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupLg,
        },
      },
    });
  }

  if ($(".tf-sw-product-sell-3").length > 0) {
    var preview = $(".tf-sw-product-sell-3").data("preview");
    var tablet = $(".tf-sw-product-sell-3").data("tablet");
    var mobile = $(".tf-sw-product-sell-3").data("mobile");
    var spacingLg = $(".tf-sw-product-sell-3").data("space-lg");
    var spacingMd = $(".tf-sw-product-sell-3").data("space-md");
    var perGroup = $(".tf-sw-product-sell-3").data("pagination");
    var perGroupMd = $(".tf-sw-product-sell-3").data("pagination-md");
    var perGroupLg = $(".tf-sw-product-sell-3").data("pagination-lg");

    var swiper = new Swiper(".tf-sw-product-sell-3", {
      slidesPerView: mobile,
      spaceBetween: spacingMd,
      speed: 1000,
      pagination: {
        el: ".sw-pagination-product-3",
        clickable: true,
      },
      slidesPerGroup: perGroup,
      navigation: {
        clickable: true,
        nextEl: ".nav-prev-product-3",
        prevEl: ".nav-next-product-3",
      },
      breakpoints: {
        768: {
          slidesPerView: tablet,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupMd,
        },
        1150: {
          slidesPerView: preview,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupLg,
        },
      },
    });
  }

  if ($(".tf-sw-product-sell-4").length > 0) {
    var preview = $(".tf-sw-product-sell-4").data("preview");
    var tablet = $(".tf-sw-product-sell-4").data("tablet");
    var mobile = $(".tf-sw-product-sell-4").data("mobile");
    var spacingLg = $(".tf-sw-product-sell-4").data("space-lg");
    var spacingMd = $(".tf-sw-product-sell-4").data("space-md");
    var perGroup = $(".tf-sw-product-sell-4").data("pagination");
    var perGroupMd = $(".tf-sw-product-sell-4").data("pagination-md");
    var perGroupLg = $(".tf-sw-product-sell-4").data("pagination-lg");

    var swiper = new Swiper(".tf-sw-product-sell-4", {
      slidesPerView: mobile,
      spaceBetween: spacingMd,
      speed: 1000,
      pagination: {
        el: ".sw-pagination-product-4",
        clickable: true,
      },
      slidesPerGroup: perGroup,
      navigation: {
        clickable: true,
        nextEl: ".nav-prev-product-4",
        prevEl: ".nav-next-product-4",
      },
      breakpoints: {
        768: {
          slidesPerView: tablet,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupMd,
        },
        1150: {
          slidesPerView: preview,
          spaceBetween: spacingLg,
          slidesPerGroup: perGroupLg,
        },
      },
    });
  }

  // Save the original fetch function
  const originalFetch = window.fetch;
  // Override the global fetch function
  window.fetch = async function (...args) {
    const response = await originalFetch(...args);
    if (response.ok && response.url.includes("/wp-json/wc/store/v1/batch")) {
      getcartcount();
    }
    return response;
  };

  // price on change
  const rangeInput = document.querySelectorAll(".range-input input");
  const progress = document.querySelector(".progress-price");
  const minPrice = document.querySelector(".min-price");
  const maxPrice = document.querySelector(".max-price");
  let priceGap = 1;

  rangeInput.forEach((input) => {
    input.addEventListener("input", (e) => {
      let minVal = parseInt(rangeInput[0].value);
      let maxVal = parseInt(rangeInput[1].value);

      if (maxVal - minVal < priceGap) {
        if (e.target.className === "range-min") {
          rangeInput[0].value = maxVal - priceGap;
          minVal = maxVal - priceGap;
        } else {
          rangeInput[1].value = minVal + priceGap;
          maxVal = minVal + priceGap;
        }
      }

      // Update display values
      minPrice.textContent = minVal;
      maxPrice.textContent = maxVal;

      // Calculate progress bar position
      const percent1 = (minVal / maxVal) * 100;
      const percent2 = (maxVal / maxVal) * 100;
      progress.style.left = percent1 + "%";
      progress.style.right = 100 - percent2 + "%";
    });
  });

  // update price range on change
  const minVal = parseInt(rangeInput[0].value);
  const maxVal = parseInt(rangeInput[1].value);

  if (maxVal - minVal < priceGap) {
    if (e.target.className === "range-min") {
      rangeInput[0].value = maxVal - priceGap;
      minVal = maxVal - priceGap;
    } else {
      rangeInput[1].value = minVal + priceGap;
      maxVal = minVal + priceGap;
    }
  }
  minPrice.textContent = minVal;
  maxPrice.textContent = maxVal;

  // Calculate progress bar position
  const percent1 = (minVal / maxVal) * 100;
  const percent2 = (maxVal / maxVal) * 100;
  progress.style.left = percent1 + "%";
  progress.style.right = 100 - percent2 + "%";

  const mediaQuery = window.matchMedia("(max-width: 1200px)");
  handleMediaQueryChange(mediaQuery);
  mediaQuery.addEventListener("change", handleMediaQueryChange);

  // reset all
  const resetall = document.getElementById("reset-all");
  const params = new URLSearchParams(window.location.search);
  if (params.toString() !== "") {
    resetall.classList.remove("d-none");
  } else {
    resetall.classList.add("d-none");
  }
  resetall.addEventListener("click", function (event) {
    event.preventDefault();
    clearAllFilters();
  });
});

function handleMediaQueryChange(event) {
  if (event.matches) {
    // Media query matches (max-width: 1200px)
    $(".wrap-sidebar-mobile").empty();
  } else {
    $(".sidebar-mobile-append").empty();
  }
}
