<?php
/**
 * Privacy Policy Modal for Checkout
 *
 * This template displays a privacy policy modal that appears on checkout page
 * and must be accepted before proceeding.
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include external CSS and JS files
$css_file = __DIR__ . '/privacy-modal.css';
$js_file = __DIR__ . '/privacy-modal.js';

// Get home URL for redirect
$home_url = function_exists('home_url') ? home_url() : '/';
$privacy_url = function_exists('get_privacy_policy_url') ? get_privacy_policy_url() : '#';
?>

<!-- Privacy Policy Modal -->
<div id="privacy-modal" class="privacy-modal" style="display: none;">
    <div class="privacy-modal-content">
        <div class="privacy-modal-header">
            <h3><?php echo get_field('modal_title', 'option') ?></h3>
        </div>

        <div class="privacy-modal-body">
            <p><?php echo get_field('modal_description', 'option') ?></p>

            <div class="privacy-policy-content">
                <?php echo get_field('modal_content', 'option') ?>
            </div>
        </div>

        <div class="privacy-modal-footer">
            <label class="privacy-checkbox-container">
                <input type="checkbox" id="privacy-agreement" name="privacy_agreement" required>
                <span class="checkmark"></span>
                I have read and agree to the privacy policy
            </label>

            <div class="privacy-modal-buttons">
                <button type="button" id="privacy-decline" class="button button-secondary">
                    Decline
                </button>
                <button type="button" id="privacy-accept" class="button button-primary" disabled>
                    Accept & Continue
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Include external CSS if available -->
<?php if (file_exists($css_file)): ?>
    <link rel="stylesheet" href="<?php echo dirname($_SERVER['REQUEST_URI']) . '/privacy-modal.css'; ?>">
<?php endif; ?>

<style>
    /* Privacy Modal Styles */
    .privacy-modal {
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
    }

    .privacy-modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 0;
        border-radius: 8px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .privacy-modal-header {
        padding: 20px 25px 15px;
        border-bottom: 1px solid #eee;
    }

    .privacy-modal-header h3 {
        margin: 0;
        font-size: 1.4em;
        color: #333;
    }

    .privacy-modal-body {
        padding: 20px 25px;
        line-height: 1.6;
    }

    .privacy-policy-content {
        background-color: #f9f9f9;
        padding: 15px;
        border-radius: 5px;
        margin: 15px 0;
        max-height: 200px;
        overflow-y: auto;
    }

    .privacy-policy-content ul {
        margin: 10px 0;
        padding-left: 20px;
    }

    .privacy-policy-content li {
        margin-bottom: 5px;
    }

    .privacy-modal-footer {
        padding: 15px 25px 25px;
        border-top: 1px solid #eee;
    }

    .privacy-checkbox-container {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        cursor: pointer;
        font-size: 14px;
    }

    .privacy-checkbox-container input[type="checkbox"] {
        margin-right: 10px;
        transform: scale(1.2);
    }

    .privacy-modal-buttons {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }

    .privacy-modal-buttons .button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .privacy-modal-buttons .button-secondary {
        background-color: #f1f1f1;
        color: #333;
    }

    .privacy-modal-buttons .button-secondary:hover {
        background-color: #e1e1e1;
    }

    .privacy-modal-buttons .button-primary {
        background-color: #0073aa;
        color: white;
    }

    .privacy-modal-buttons .button-primary:hover:not(:disabled) {
        background-color: #005a87;
    }

    .privacy-modal-buttons .button-primary:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .privacy-modal-content {
            margin: 10% auto;
            width: 95%;
            max-height: 85vh;
        }

        .privacy-modal-header,
        .privacy-modal-body,
        .privacy-modal-footer {
            padding-left: 20px;
            padding-right: 20px;
        }

        .privacy-modal-buttons {
            flex-direction: column;
        }

        .privacy-modal-buttons .button {
            width: 100%;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Check if privacy policy has been accepted in this session
        const privacyAccepted = sessionStorage.getItem('privacy_policy_accepted');

        if (!privacyAccepted) {
            // Show modal after a short delay
            setTimeout(function () {
                document.getElementById('privacy-modal').style.display = 'block';
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            }, 500);
        }

        const modal = document.getElementById('privacy-modal');
        const checkbox = document.getElementById('privacy-agreement');
        const acceptBtn = document.getElementById('privacy-accept');
        const declineBtn = document.getElementById('privacy-decline');

        // Enable/disable accept button based on checkbox
        checkbox.addEventListener('change', function () {
            acceptBtn.disabled = !this.checked;
        });

        // Accept button click
        acceptBtn.addEventListener('click', function () {
            if (checkbox.checked) {
                // Store acceptance in session storage
                sessionStorage.setItem('privacy_policy_accepted', 'true');

                // Hide modal
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';

                // Optional: Add a hidden input to the checkout form
                const checkoutForm = document.querySelector('form.checkout');
                if (checkoutForm) {
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'privacy_policy_accepted';
                    hiddenInput.value = '1';
                    checkoutForm.appendChild(hiddenInput);
                }
            }
        });

        // Decline button click
        declineBtn.addEventListener('click', function () {
            // Redirect to homepage or show message
            if (confirm(
                'You must accept the privacy policy to proceed with checkout. Would you like to return to the homepage?'
            )) {
                window.location.href = '<?php echo $home_url; ?>';
            }
        });

        // Prevent closing modal by clicking outside
        modal.addEventListener('click', function (e) {
            if (e.target === modal) {
                // Optional: show a message that they need to accept
                alert('Please read and accept the privacy policy to continue.');
            }
        });
    });
</script>