#wc-block-components-totals-coupon__form > button,
div.wc-block-components-shipping-calculator > form > button,
div.wc-block-cart__submit-container > a {
  font-family: "haas_mediu", serif;
  text-decoration: none;
  width: 100% !important;
  justify-content: center !important;
  border-radius: 3px !important;
  transition: all 0.3s ease;
  will-change: background-color, color, border;
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  padding: 14px 24px;
  cursor: pointer;
  background-color: var(--main);
  border: 1px solid var(--main);
  color: var(--white);
}

#wc-block-components-totals-coupon__input-0,
.wc-blocks-components-select__select,
.wc-block-components-text-input.is-active input[type="text"] {
  font-family: inherit !important;
  text-decoration: none !important;
  color: var(--main) !important;
  width: 100% !important;
  justify-content: center !important;
  border-radius: 3px !important;
  transition: all 0.3s ease !important;
  will-change: background-color, color, border !important;
  pointer-events: auto !important;
  overflow: hidden !important;
  font-size: 14px !important;
  line-height: 16px !important;
  font-weight: 500 !important;
  box-sizing: border-box !important;
  padding: 24px 9px 10px 9px !important;
  display: inline-flex !important;
  align-items: center !important;
  cursor: pointer !important;
  background-color: var(--white) !important;
  border: 1px solid var(--main) !important;
}

.wc-blocks-components-select .wc-blocks-components-select__container {
  border: none !important;
}

.wc-block-components-form
  .wc-block-components-text-input
  input[type="email"]:focus,
.wc-block-components-form
  .wc-block-components-text-input
  input[type="number"]:focus,
.wc-block-components-form
  .wc-block-components-text-input
  input[type="password"]:focus,
.wc-block-components-form
  .wc-block-components-text-input
  input[type="tel"]:focus,
.wc-block-components-form
  .wc-block-components-text-input
  input[type="text"]:focus,
.wc-block-components-form
  .wc-block-components-text-input
  input[type="url"]:focus,
.wc-block-components-text-input input[type="email"]:focus,
.wc-block-components-text-input input[type="number"]:focus,
.wc-block-components-text-input input[type="password"]:focus,
.wc-block-components-text-input input[type="tel"]:focus,
.wc-block-components-text-input input[type="text"]:focus,
.wc-block-components-text-input input[type="url"]:focus {
  box-shadow: none !important;
}

.is-large .wc-block-components-sidebar .wc-block-components-panel,
.is-large .wc-block-components-sidebar .wc-block-components-totals-coupon,
.is-large .wc-block-components-sidebar .wc-block-components-totals-item {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.is-large.wc-block-cart
  .wc-block-components-sidebar
  .wc-block-components-shipping-calculator,
.is-large.wc-block-cart
  .wc-block-components-sidebar
  .wc-block-components-shipping-rates-control__package:not(.wc-block-components-panel) {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.is-large.wc-block-cart .wc-block-cart__totals-title,
.wc-block-cart-items__header {
  font-family: "druk", serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  letter-spacing: normal !important;
}

.wc-block-components-totals-coupon .wc-block-components-panel__button,
.wc-block-components-totals-item,
.wc-block-components-product-name,
.price.wc-block-components-product-price,
.wc-block-components-product-metadata,
.wc-block-components-panel__button {
  font-size: 16px !important;
  font-family: "haas_mediu", serif !important;
  letter-spacing: normal !important;
}

.content-single ul li {
  list-style-type: none !important;
}

.wc-block-components-totals-item__description,
.wc-block-components-product-metadata__description {
  font-family: "haas_light", serif !important;
}

.wc-block-cart-item__total {
  padding-right: 0px !important;
}

.wc-block-cart-item__image {
  padding-left: 0px !important;
}
.is-large.wc-block-cart .wc-block-cart-items th:last-child {
  padding-right: 0px !important;
}

.wc-block-components-quantity-selector
  .wc-block-components-quantity-selector__button,
.wc-block-components-quantity-selector
  input.wc-block-components-quantity-selector__input {
  width: 127px !important;
  background-color: rgb(242, 242, 242) !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  border-style: none !important;
  font-size: 14px !important;
  font-weight: inherit !important;
  opacity: 1 !important;
}

.wc-block-components-quantity-selector
  input.wc-block-components-quantity-selector__input:focus,
.wc-block-components-quantity-selector
  .wc-block-components-quantity-selector__button:focus {
  box-shadow: none !important;
}

.has-text-align-center a,
div.wc-block-checkout__actions_row > button {
  font-family: "haas_mediu", serif;
  text-decoration: none;
  width: 10% !important;
  justify-content: center !important;
  border-radius: 3px !important;
  transition: all 0.3s ease;
  will-change: background-color, color, border;
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  padding: 14px 24px;
  cursor: pointer;
  background-color: var(--main);
  border: 1px solid var(--main);
  color: var(--white);
}

div.wc-block-checkout__actions_row > button {
  width: 100% !important;
}
body:not(.woocommerce-block-theme-has-button-styles)
  .wc-block-components-button:not(.is-link):focus {
  box-shadow: none !important;
}

.is-large .wp-block-woocommerce-checkout-order-summary-block {
  border: none !important;
}

.wc-block-components-title.wc-block-components-title {
  font-size: 20px !important;
  line-height: normal !important;
}

.wc-block-components-radio-control--highlight-checked
  .wc-block-components-radio-control-accordion-option--checked-option-highlighted,
.wc-block-components-radio-control--highlight-checked
  label.wc-block-components-radio-control__option--checked-option-highlighted {
  border-radius: 4px !important;
  box-shadow: none !important;
  border: 1px solid !important;
}

div.wc-block-components-checkout-step__container > p {
  margin-bottom: 10px !important;
  margin-top: 5px !important;
  font-size: 16px !important;
  line-height: normal !important;
}

/* order */

p.woocommerce-notice.woocommerce-notice--success.woocommerce-thankyou-order-received {
  padding: 20px !important;
  text-align: center !important;
  color: var(--bs-black) !important;
  font-family: "haas_mediu" !important;
  font-size: 20px !important;
}

.wc-bacs-bank-details-heading,
.wc-bacs-bank-details-account-name,
h2.woocommerce-column__title {
  margin: 0px !important;
  font-size: 20px !important;
  line-height: normal !important;
}

ul.woocommerce-order-overview.woocommerce-thankyou-order-details.order_details {
  border: 1px solid !important;
  border-radius: 4px !important;
  display: flex !important;
  justify-content: space-between !important;
  overflow: scroll;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

section.woocommerce-bacs-bank-details,
section.woocommerce-order-details {
  border: 1px solid;
  border-radius: 4px;
  margin-bottom: 25px;
}

h2.wc-bacs-bank-details-heading,
h2.woocommerce-order-details__title {
  border-bottom: 1px solid;
  padding: 9px 12px;
  text-align: center;
  font-size: 20px;
  line-height: normal;
  margin: 0px !important;
}

h3.wc-bacs-bank-details-account-name {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 9px 12px !important;
}

ul.wc-bacs-bank-details.order_details.bacs_details {
  margin: 0px;
  display: flex !important;
  justify-content: space-between !important;
  overflow: scroll;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  padding: 9px 15px;
}

.content-single .woocommerce ul {
  padding: 0px !important;
}

.woocommerce .woocommerce-customer-details address {
  border: 1px solid;
  border-bottom-width: 1px;
  border-right-width: 1px;
  text-align: left;
  width: 100%;
  border-radius: 4px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  padding: 15px;
}

.woocommerce .woocommerce-customer-details .woocommerce-column__title {
  margin-top: 0;
  padding: 9px 15px;
  text-align: center;
  border: 1px solid;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: navajowhite;
}

.woocommerce-column.woocommerce-column--1.woocommerce-column--billing-address {
  margin-bottom: 25px;
}

.woocommerce ul.order_details li {
  padding-left: 15px;
}

section.woocommerce-order-details {
  margin-bottom: 25px !important;
}

/* myaccount */
.woocommerce-MyAccount-navigation ul {
  border: 1px solid;
  border-radius: 4px;
  font-family: "haas_mediu" !important;
  font-size: 16px !important;
  border-bottom: none;
}

li.woocommerce-MyAccount-navigation-link {
  border-bottom: 1px solid;
  margin: 0px !important;
  padding: 9px 15px;
}

li.woocommerce-MyAccount-navigation-link.is-active {
  background-color: var(--bs-gray-400);
}
nav.woocommerce-MyAccount-navigation {
  margin-bottom: 25px;
}

/* notice */

.woocommerce-message,
.woocommerce-info {
  border: 1px solid;
  border-radius: 4px;
  background-color: unset !important;
}

.woocommerce a.button,
.woocommerce-notices-wrapper .woocommerce-message a,
.woocommerce-notices-wrapper .woocommerce-info a {
  background-color: var(--main) !important;
  border: 1px solid var(--main) !important;
  color: var(--white) !important;
  font-family: "haas_mediu";
  font-size: 14px;
  padding: 14px 24px;
}

/* orders */
table.woocommerce-orders-table.woocommerce-MyAccount-orders.shop_table.shop_table_responsive.my_account_orders.account-orders-table {
  border: 1px solid;
}

/* billing */
.woocommerce-MyAccount-content address {
  border: 1px solid;
  border-bottom-width: 1px;
  border-right-width: 1px;
  text-align: left;
  width: 100%;
  border-radius: 0px;
  padding: 15px;
  margin-bottom: 25px;
}

header.woocommerce-Address-title.title h2,
.woocommerce-MyAccount-content h2 {
  font-size: 20px;
  line-height: normal;
  border-bottom: 1px solid;
  padding: 9px 15px;
  margin-top: 0px;
}

.woocommerce-account .addresses .title .edit {
  float: left;
  margin-bottom: 10px;
  background-color: unset !important;
  border: 1px solid var(--main) !important;
  border-radius: 4px;
  color: unset !important;
  font-family: "haas_mediu";
  font-size: 14px;
  padding: 9px 15px;
  width: 100%;
}

.woocommerce {
  color: var(--main);
}
.woocommerce form .form-row label {
  font-family: "haas_mediu" !important;
  font-size: 16px !important;
  color: var(--main);
}
.woocommerce form .form-row input.input-text,
.woocommerce form .form-row textarea,
.select2-container--default .select2-selection--single {
  border: 1px solid var(--main) !important;
  border-radius: 4px;
  color: #2b2d2f;
  min-height: 40px;
  font-size: 14px;
}

.select2-container--default .select2-selection--single span {
  padding-top: 5px;
  padding-left: 15px !important;
}

.woocommerce form .form-row .woocommerce-input-wrapper {
  color: #2b2d2f;
}

.woocommerce form button {
  background-color: var(--main) !important;
  border: 1px solid var(--main) !important;
  color: var(--white) !important;
  font-family: "haas_mediu";
  font-size: 14px;
  padding: 14px 24px;
}

.woocommerce form .form-row em {
  color: #2b2d2f;
  min-height: 40px;
  font-size: 14px;
}

.woocommerce:where(body:not(.woocommerce-uses-block-theme)) div.product .stock {
  color: #000000 !important;
}

.woocommerce-variation.single_variation {
  margin-bottom: 0px;
  font-family: "haas_mediu";
  margin-top: 15px;
  display: flex;
  justify-content: space-between;
  border: 1px solid;
  border-radius: 4px;
}
.woocommerce-variation-description, .woocommerce-variation-price, .woocommerce-variation-availability {
  text-align: left;
  width: 100%;
  border-right: 1px solid;
  padding: 10px 15px;
}

.woocommerce-variation-availability {
  border-right: none;
}

span.woocommerce-Price-amount.amount {
  color: #000000;
}

.wg-quantity input::-webkit-outer-spin-button,
.wg-quantity input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.wg-quantity input[type=number]{
  -moz-appearance: textfield;
}

.woocommerce div.product form.cart div.quantity {
  float: unset;
  width: 100%;
}


a.button.wc-forward {
  background-color: unset !important;
  border: none !important;
  color: var(--main) !important;
  padding-left: 5px;
}

.woonotice {
  border: 1px solid var(--main);
  border-radius: 4px;
}

.woonoticeerr {
  border: 1px solid var(--bs-danger);
  border-radius: 4px;
}

.woonoticeinf {
  border: 1px solid var(--bs-success);
  border-radius: 4px;
}

.quantity.wg-quantity {
  width: 100%;
}

button.pswp__button {
  background-image: unset !important;
}

.pswp__item {
  background-color: var(--main);
}

.pswp__button--arrow .pswp__icn {
  width: 30px;
  height: 30px;
}

.pswp__icn {
  fill: var(--main);
}

.pswp__icn .pswp__icn-shadow {
  stroke: var(--white);
}

.pswp__counter {
  color: var(--main) !important;
  text-shadow: 1px 1px 0px var(--white) !important;
}

.widget-content-tab h2 {
  font-size: 24px;
  line-height: normal;
  margin-bottom: 20px;
  font-family: 'haas_mediu';
}

.wc-block-components-product-badge {
  border-radius: 0px !important;
}

.wc-block-components-quantity-selector:after {
  border: none !important;
}

.woocommerce #reviews #comments ol.commentlist li .comment-text {
  border-radius: 0px !important;
}

.woocommerce .comments-area form button {
  background-color: unset !important;
  border: unset !important;
}

.woocommerce #review_form #respond textarea {
  border-radius: 4px !important;
}

.woocommerce #review_form #respond .form-submit input {
  background: black !important;
  color: white !important;
  border-radius: 4px !important;
}

.countdown-box.soldout {
  border-radius: 0px !important;
  border: solid 2px black;
  font-family: "haas_mediu", serif;
}

.nav-sw.round,.button-style-arrow {
  border-radius: 4px !important;
}

.modalDemo .modal-content {
  border-radius: 0px !important;
}

.tf-main-area-page p {
  color: black !important;
}

.woocommerce table.shop_table {
  border-radius: 0px !important;
  padding: 20px !important;
}

.woocommerce-MyAccount-navigation ul {
  border-radius: 0px !important;
}


.wp-block-woocommerce-cart .wp-block-woocommerce-cart-cross-sells-block .wp-block-heading {
  margin-bottom: 24px !important;
  font-family: 'druk'!important;
  font-size: 28px !important;
  text-transform: uppercase !important;
}

.wp-block-woocommerce-cart .wp-block-woocommerce-cart-cross-sells-block .cross-sells-product .wc-block-components-product-button__button {
  border-radius: 4px !important;
  background-color: black !important;
  font-family: 'haas_mediu' !important;
  font-size: 14px !important;
  padding: 14px 24px !important;
}


.wc-block-components-product-sale-badge {
  background: #dc3545 !important;
  border: none !important;
  border-radius: 0px !important;
  color: #ffffff !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  padding: 0 10px !important;
  text-transform: capitalize !important;
  font-family: 'Albert Sans' !important;
}

.wp-block-woocommerce-cart .wp-block-woocommerce-cart-cross-sells-block .cross-sells-product div .wc-block-components-product-image {
  border: 2px solid black !important;
}

.widget-facet .current-scrollbar {
  margin-bottom: 20px !important;
}

.widget-facet {
  border-bottom: 1px solid var(--line) !important;
}

.widget-facet .facet-title {
  text-transform: capitalize;
}

.woocommerce-no-products-found {
  display: block !important;
  position: absolute !important;
  width: -webkit-fill-available !important;
  margin-right: 25px !important;
}

.woocommerce .tf-mini-search-frm button {
  background-color: inherit !important;
  border: inherit !important;
  color: inherit !important;
  font-family: inherit !important;
  font-size: inherit !important;
  padding: inherit !important;
}

.canvas-filter .widget-facet:last-child {
  margin-bottom: 26px !important;
}

.woocommerce:where(body:not(.woocommerce-uses-block-theme)) div.product p.price, .woocommerce:where(body:not(.woocommerce-uses-block-theme)) div.product span.price {
    color: inherit !important;
}


#order_review p{
  margin-top: 0px;
}

.form-row > label, button#place_order{
  font-weight: 500;
}

.wc-blocks-components-select .wc-blocks-components-select__container {
    height: 4.125em !important;
}