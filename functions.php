<?php
if ( ! function_exists( 'cablester_setup' ) ) :

function cablester_setup() {

    /*
     * Make theme available for translation.
     * Translations can be filed in the /languages/ directory.
     */
    /* Pinegrow generated Load Text Domain Begin */
    load_theme_textdomain( 'cablester', get_template_directory() . '/languages' );
    /* Pinegrow generated Load Text Domain End */

    // Add default posts and comments RSS feed links to head.
    add_theme_support( 'automatic-feed-links' );

    /*
     * Let WordPress manage the document title.
     */
    add_theme_support( 'title-tag' );
    
    /*
     * Enable support for Post Thumbnails on posts and pages.
     */
    add_theme_support( 'post-thumbnails' );
    //Support custom logo
    add_theme_support( 'custom-logo' );

    // Add menus.
    register_nav_menus( array(
        'primary' => __( 'Primary Menu', 'cablester' ),
        'social'  => __( 'Social Links Menu', 'cablester' ),
    ) );

/*
     * Register custom menu locations
     */
    /* Pinegrow generated Register Menus Begin */

    register_nav_menu(  'footer_menu_1', __( 'Footer Menu 1', 'cablester' )  );

    register_nav_menu(  'footer_menu_2', __( 'Footer Menu 2', 'cablester' )  );

    register_nav_menu(  'footer_menu_3', __( 'Footer Menu 3', 'cablester' )  );

    register_nav_menu(  'mobile_menu', __( 'Mobile Menu', 'cablester' )  );

    register_nav_menu(  'quick_menu', __( 'Quick Link', 'cablester' )  );

    /* Pinegrow generated Register Menus End */
    
/*
    * Set image sizes
     */
    /* Pinegrow generated Image Sizes Begin */

add_image_size( 'product_carousel', 246, 322, true );
    /* Pinegrow generated Image Sizes End */
    
    /*
     * Switch default core markup for search form, comment form, and comments
     * to output valid HTML5.
     */
    add_theme_support( 'html5', array(
        'search-form', 'comment-form', 'comment-list', 'gallery', 'caption'
    ) );

    /*
     * Enable support for Post Formats.
     */
    add_theme_support( 'post-formats', array(
        'aside', 'image', 'video', 'quote', 'link', 'gallery', 'status', 'audio', 'chat'
    ) );

    /*
     * Enable support for Page excerpts.
     */
     add_post_type_support( 'page', 'excerpt' );
}
endif; // cablester_setup

add_action( 'after_setup_theme', 'cablester_setup' );


if ( ! function_exists( 'cablester_init' ) ) :

function cablester_init() {

    
    // Use categories and tags with attachments
    register_taxonomy_for_object_type( 'category', 'attachment' );
    register_taxonomy_for_object_type( 'post_tag', 'attachment' );

    /*
     * Register custom post types. You can also move this code to a plugin.
     */
    /* Pinegrow generated Custom Post Types Begin */

    /* Pinegrow generated Custom Post Types End */
    
    /*
     * Register custom taxonomies. You can also move this code to a plugin.
     */
    /* Pinegrow generated Taxonomies Begin */

    /* Pinegrow generated Taxonomies End */

}
endif; // cablester_setup

add_action( 'init', 'cablester_init' );


if ( ! function_exists( 'cablester_custom_image_sizes_names' ) ) :

function cablester_custom_image_sizes_names( $sizes ) {

    /*
     * Add names of custom image sizes.
     */
    /* Pinegrow generated Image Sizes Names Begin */

return array_merge( $sizes, array(
        'product_carousel' => __( 'Product Carousel' )
) );

    /* Pinegrow generated Image Sizes Names End */
    return $sizes;
}
add_action( 'image_size_names_choose', 'cablester_custom_image_sizes_names' );
endif;// cablester_custom_image_sizes_names



if ( ! function_exists( 'cablester_widgets_init' ) ) :

function cablester_widgets_init() {

    /*
     * Register widget areas.
     */
    /* Pinegrow generated Register Sidebars Begin */

    /* Pinegrow generated Register Sidebars End */
}
add_action( 'widgets_init', 'cablester_widgets_init' );
endif;// cablester_widgets_init



if ( ! function_exists( 'cablester_customize_register' ) ) :

function cablester_customize_register( $wp_customize ) {
    // Do stuff with $wp_customize, the WP_Customize_Manager object.

    /* Pinegrow generated Customizer Controls Begin */

    /* Pinegrow generated Customizer Controls End */

}
add_action( 'customize_register', 'cablester_customize_register' );
endif;// cablester_customize_register


if ( ! function_exists( 'cablester_enqueue_scripts' ) ) :
    function cablester_enqueue_scripts() {

        /* Pinegrow generated Enqueue Scripts Begin */

    wp_deregister_script( 'cablester-bootstrap' );
    wp_enqueue_script( 'cablester-bootstrap', get_template_directory_uri() . '/js/bootstrap.min.js', [], '1.0.870', true);

    wp_deregister_script( 'jquery' );
    wp_enqueue_script( 'jquery', get_template_directory_uri() . '/js/jquery.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-swiperbundle' );
    wp_enqueue_script( 'cablester-swiperbundle', get_template_directory_uri() . '/js/swiper-bundle.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-carousel' );
    wp_enqueue_script( 'cablester-carousel', get_template_directory_uri() . '/js/carousel.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-countdown' );
    wp_enqueue_script( 'cablester-countdown', get_template_directory_uri() . '/js/count-down.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-bootstrapselect' );
    wp_enqueue_script( 'cablester-bootstrapselect', get_template_directory_uri() . '/js/bootstrap-select.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-lazysize' );
    wp_enqueue_script( 'cablester-lazysize', get_template_directory_uri() . '/js/lazysize.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-drift' );
    wp_enqueue_script( 'cablester-drift', get_template_directory_uri() . '/js/drift.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-wow' );
    wp_enqueue_script( 'cablester-wow', get_template_directory_uri() . '/js/wow.min.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-multiplemodal' );
    wp_enqueue_script( 'cablester-multiplemodal', get_template_directory_uri() . '/js/multiple-modal.js', [], '1.0.870', true);

    wp_deregister_script( 'cablester-main' );
    wp_enqueue_script( 'cablester-main', get_template_directory_uri() . '/js/main.js', [], '1.0.870', true);

    /* Pinegrow generated Enqueue Scripts End */

        /* Pinegrow generated Enqueue Styles Begin */

    wp_deregister_style( 'cablester-fonts' );
    wp_enqueue_style( 'cablester-fonts', get_template_directory_uri() . '/fonts/fonts.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-fonticons' );
    wp_enqueue_style( 'cablester-fonticons', get_template_directory_uri() . '/fonts/font-icons.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-bootstrap' );
    wp_enqueue_style( 'cablester-bootstrap', get_template_directory_uri() . '/css/bootstrap.min.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-driftbasic' );
    wp_enqueue_style( 'cablester-driftbasic', get_template_directory_uri() . '/css/drift-basic.min.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-photoswipe' );
    wp_enqueue_style( 'cablester-photoswipe', get_template_directory_uri() . '/css/photoswipe.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-swiperbundle' );
    wp_enqueue_style( 'cablester-swiperbundle', get_template_directory_uri() . '/css/swiper-bundle.min.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-animate' );
    wp_enqueue_style( 'cablester-animate', get_template_directory_uri() . '/css/animate.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-woocommerce' );
    wp_enqueue_style( 'cablester-woocommerce', get_template_directory_uri() . '/woocommerce.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-styles' );
    wp_enqueue_style( 'cablester-styles', get_template_directory_uri() . '/css/styles.css', [], '1.0.870', 'all');

    wp_deregister_style( 'cablester-style' );
    wp_enqueue_style( 'cablester-style', get_bloginfo('stylesheet_url'), [], '1.0.870', 'all');

    /* Pinegrow generated Enqueue Styles End */

    }
    add_action( 'wp_enqueue_scripts', 'cablester_enqueue_scripts' );
endif;

function pgwp_sanitize_placeholder($input) { return $input; }
/*
 * Resource files included by Pinegrow.
 */
/* Pinegrow generated Include Resources Begin */
require_once "inc/custom.php";
if( !class_exists( 'PG_Helper_v2' ) ) { require_once "inc/wp_pg_helpers.php"; }
if( !class_exists( 'PG_WC_Helper' ) ) { require_once "inc/wc_pg_helpers.php"; }
if( !class_exists( 'PG_Pagination' ) ) { require_once "inc/wp_pg_pagination.php"; }

    /* Pinegrow generated Include Resources End */

/* Setting up theme supports options */

function cablester_setup_theme_supports() {
    // Don't edit anything between the following comments.
    /* Pinegrow generated Theme Supports Begin */

    add_theme_support( 'woocommerce' );
    add_theme_support( 'wc-product-gallery-zoom' );
    add_theme_support( 'wc-product-gallery-lightbox' );
    add_theme_support( 'wc-product-gallery-slider' );
    /* Pinegrow generated Theme Supports End */
}
add_action('after_setup_theme', 'cablester_setup_theme_supports');

/* End of setting up theme supports options */


/* Setting up WooCommerce filters */
/* Pinegrow generated WooCommerce Filters Begin */

    /* This filter lets us multiple variants of the same template name. It also handles locating the templates that are present in the theme or plugin. */        
    add_filter( 'wc_get_template', function( $template, $template_name, $args, $template_path, $default_path ) {
        global $pg_wc_use_template, $pg_wc_use_template_cache_cablester;
        if(!isset($pg_wc_use_template_cache_cablester)) $pg_wc_use_template_cache_cablester = array();
        
        if( !empty($pg_wc_use_template) ) {
            $template_variant = trailingslashit( get_template_directory() ) . 'woocommerce/' . str_replace( '.php', '-'.$pg_wc_use_template.'.php', $template_name);
            $template_key = $template_name . '-' . $pg_wc_use_template;
        } else {
            $template_key = $template_name;
            $template_variant = trailingslashit( get_template_directory() ) . 'woocommerce/' . $template_name;
        }
            
        if(isset($pg_wc_use_template_cache_cablester[ $template_key ])) {
            if($pg_wc_use_template_cache_cablester[ $template_key ]) {
                $template = $template_variant;
            }
        } else if(file_exists($template_variant)) {
            $template = $template_variant;
            $pg_wc_use_template_cache_cablester[ $template_key ] = true;
        } else {
            $pg_wc_use_template_cache_cablester[ $template_key ] = false;
        }
 
        return $template;
    }, 10, 5 );  
            
    /* Pinegrow generated WooCommerce Filters End */
/* End Setting up WooCommerce filters */

?>