/**
 * Privacy Policy Modal JavaScript
 * Handles modal functionality for checkout privacy policy agreement
 */

class PrivacyModal {
    constructor() {
        this.modal = null;
        this.checkbox = null;
        this.acceptBtn = null;
        this.declineBtn = null;
        this.isAccepted = false;
        
        this.init();
    }
    
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        // Get modal elements
        this.modal = document.getElementById('privacy-modal');
        this.checkbox = document.getElementById('privacy-agreement');
        this.acceptBtn = document.getElementById('privacy-accept');
        this.declineBtn = document.getElementById('privacy-decline');
        
        if (!this.modal) {
            console.warn('Privacy modal not found');
            return;
        }
        
        // Check if already accepted in this session
        this.isAccepted = sessionStorage.getItem('privacy_policy_accepted') === 'true';
        
        if (!this.isAccepted) {
            this.showModal();
        }
        
        this.bindEvents();
    }
    
    bindEvents() {
        if (!this.modal) return;
        
        // Checkbox change event
        if (this.checkbox) {
            this.checkbox.addEventListener('change', () => this.toggleAcceptButton());
        }
        
        // Accept button click
        if (this.acceptBtn) {
            this.acceptBtn.addEventListener('click', () => this.acceptPrivacy());
        }
        
        // Decline button click
        if (this.declineBtn) {
            this.declineBtn.addEventListener('click', () => this.declinePrivacy());
        }
        
        // Prevent closing modal by clicking outside
        this.modal.addEventListener('click', (e) => this.handleModalClick(e));
        
        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeydown(e));
        
        // Prevent form submission if privacy not accepted
        this.preventCheckoutSubmission();
    }
    
    showModal() {
        if (!this.modal) return;
        
        // Show modal with animation
        this.modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        
        // Focus on checkbox for accessibility
        setTimeout(() => {
            if (this.checkbox) {
                this.checkbox.focus();
            }
        }, 300);
        
        // Add modal shown class for additional styling
        setTimeout(() => {
            this.modal.classList.add('modal-shown');
        }, 10);
    }
    
    hideModal() {
        if (!this.modal) return;
        
        this.modal.classList.add('modal-hiding');
        
        setTimeout(() => {
            this.modal.style.display = 'none';
            this.modal.classList.remove('modal-shown', 'modal-hiding');
            document.body.style.overflow = 'auto';
        }, 300);
    }
    
    toggleAcceptButton() {
        if (!this.acceptBtn || !this.checkbox) return;
        
        this.acceptBtn.disabled = !this.checkbox.checked;
        
        // Add visual feedback
        if (this.checkbox.checked) {
            this.acceptBtn.classList.add('enabled');
        } else {
            this.acceptBtn.classList.remove('enabled');
        }
    }
    
    acceptPrivacy() {
        if (!this.checkbox || !this.checkbox.checked) {
            this.showMessage('Please check the agreement checkbox first.', 'warning');
            return;
        }
        
        // Store acceptance
        sessionStorage.setItem('privacy_policy_accepted', 'true');
        this.isAccepted = true;
        
        // Add hidden input to checkout form
        this.addHiddenInputToForm();
        
        // Hide modal
        this.hideModal();
        
        // Show success message
        this.showMessage('Privacy policy accepted. You can now proceed with checkout.', 'success');
        
        // Trigger custom event
        this.triggerEvent('privacyAccepted');
    }
    
    declinePrivacy() {
        const message = this.getLocalizedText('decline_message', 
            'You must accept the privacy policy to proceed with checkout. Would you like to return to the homepage?');
            
        if (confirm(message)) {
            // Redirect to homepage
            window.location.href = this.getHomeUrl();
        }
    }
    
    handleModalClick(e) {
        if (e.target === this.modal) {
            // Show reminder message instead of closing
            this.showMessage('Please read and accept the privacy policy to continue.', 'info');
            
            // Add shake animation
            const content = this.modal.querySelector('.privacy-modal-content');
            if (content) {
                content.classList.add('shake');
                setTimeout(() => content.classList.remove('shake'), 500);
            }
        }
    }
    
    handleKeydown(e) {
        if (!this.modal || this.modal.style.display === 'none') return;
        
        // Escape key - show reminder instead of closing
        if (e.key === 'Escape') {
            e.preventDefault();
            this.showMessage('Please accept the privacy policy to continue.', 'info');
        }
        
        // Tab key - trap focus within modal
        if (e.key === 'Tab') {
            this.trapFocus(e);
        }
    }
    
    trapFocus(e) {
        const focusableElements = this.modal.querySelectorAll(
            'input, button, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (e.shiftKey && document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
        }
    }
    
    preventCheckoutSubmission() {
        const checkoutForm = document.querySelector('form.checkout, form.woocommerce-checkout');
        if (!checkoutForm) return;
        
        checkoutForm.addEventListener('submit', (e) => {
            if (!this.isAccepted) {
                e.preventDefault();
                this.showModal();
                this.showMessage('Please accept the privacy policy before proceeding.', 'warning');
                return false;
            }
        });
    }
    
    addHiddenInputToForm() {
        const checkoutForm = document.querySelector('form.checkout, form.woocommerce-checkout');
        if (!checkoutForm) return;
        
        // Remove existing input if any
        const existingInput = checkoutForm.querySelector('input[name="privacy_policy_accepted"]');
        if (existingInput) {
            existingInput.remove();
        }
        
        // Add new hidden input
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'privacy_policy_accepted';
        hiddenInput.value = '1';
        checkoutForm.appendChild(hiddenInput);
    }
    
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.getElementById('privacy-modal-message');
        
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = 'privacy-modal-message';
            messageEl.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 4px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(messageEl);
        }
        
        // Set message and style based on type
        messageEl.textContent = message;
        messageEl.className = `privacy-message privacy-message-${type}`;
        
        const colors = {
            success: '#28a745',
            warning: '#ffc107',
            error: '#dc3545',
            info: '#17a2b8'
        };
        
        messageEl.style.backgroundColor = colors[type] || colors.info;
        
        // Show message
        setTimeout(() => {
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(0)';
        }, 10);
        
        // Hide message after 4 seconds
        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(100%)';
        }, 4000);
    }
    
    triggerEvent(eventName, data = {}) {
        const event = new CustomEvent(eventName, {
            detail: { ...data, timestamp: Date.now() }
        });
        document.dispatchEvent(event);
    }
    
    getLocalizedText(key, fallback) {
        // This would integrate with WordPress localization in a real implementation
        const texts = window.privacyModalTexts || {};
        return texts[key] || fallback;
    }
    
    getHomeUrl() {
        return window.privacyModalConfig?.homeUrl || '/';
    }
}

// Add shake animation CSS
const shakeCSS = `
.privacy-modal-content.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
`;

// Inject shake CSS
const style = document.createElement('style');
style.textContent = shakeCSS;
document.head.appendChild(style);

// Initialize modal when script loads
new PrivacyModal();
