<?php
/**
 * Product Details Modal for Checkout
 *
 * This template displays a modal with product details and images
 * that can be opened via a button next to the place order button.
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include external CSS and JS files
$css_file = __DIR__ . '/product-details-modal.css';
$js_file = __DIR__ . '/product-details-modal.js';

// Get cart items for the modal
$cart_items = function_exists('WC') && WC()->cart ? WC()->cart->get_cart() : array();
?>

<!-- Include external CSS if available -->
<?php if (file_exists($css_file)): ?>
    <style>
        <?php include $css_file; ?>
    </style>
<?php endif; ?>

<!-- Product Details Button -->
<div class="product-details-button-container" style="margin-bottom: 15px;">
    <button type="button" id="show-product-details" class="button product-details-btn">
        <i class="icon-eye"></i> View Product Details
    </button>
</div>

<!-- Product Details Modal -->
<div id="product-details-modal" class="product-modal" style="display: none;">
    <div class="product-modal-content">
        <div class="product-modal-header">
            <h3>Order Details</h3>
            <button type="button" class="product-modal-close" id="close-product-modal">
                <span>&times;</span>
            </button>
        </div>

        <div class="product-modal-body">
            <?php if (!empty($cart_items)): ?>
                <div class="product-details-list">
                    <?php foreach ($cart_items as $cart_item_key => $cart_item): ?>
                        <?php
                        $_product = isset($cart_item['data']) ? $cart_item['data'] : null;

                        if (!$_product || !method_exists($_product, 'exists') || !$_product->exists() || $cart_item['quantity'] <= 0) {
                            continue;
                        }

                        $product_id = method_exists($_product, 'get_id') ? $_product->get_id() : 0;
                        $product_name = method_exists($_product, 'get_name') ? $_product->get_name() : 'Product';
                        $product_price = function_exists('WC') && WC()->cart ? WC()->cart->get_product_price($_product) : '$0.00';
                        $product_subtotal = function_exists('WC') && WC()->cart ? WC()->cart->get_product_subtotal($_product, $cart_item['quantity']) : '$0.00';
                        $product_image = method_exists($_product, 'get_image') ? $_product->get_image('medium') : '<img src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'150\' height=\'150\'%3E%3Crect width=\'150\' height=\'150\' fill=\'%23f0f0f0\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' text-anchor=\'middle\' dy=\'.3em\' fill=\'%23999\'%3ENo Image%3C/text%3E%3C/svg%3E" alt="No Image">';
                        $product_permalink = method_exists($_product, 'is_visible') && $_product->is_visible() && method_exists($_product, 'get_permalink') ? $_product->get_permalink($cart_item) : '';
                        ?>

                        <div class="product-detail-item">
                            <div class="product-image">
                                <?php if ($product_permalink): ?>
                                    <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                        <?php echo $product_image; ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo $product_image; ?>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <h4 class="product-name">
                                    <?php if ($product_permalink): ?>
                                        <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                            <?php echo wp_kses_post($product_name); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo wp_kses_post($product_name); ?>
                                    <?php endif; ?>
                                </h4>

                                <div class="product-meta">
                                    <div class="product-price">
                                        <span class="label">Price:</span>
                                        <span class="value"><?php echo $product_price; ?></span>
                                    </div>

                                    <div class="product-quantity">
                                        <span class="label">Quantity:</span>
                                        <span class="value"><?php echo $cart_item['quantity']; ?></span>
                                    </div>

                                    <div class="product-subtotal">
                                        <span class="label">Subtotal:</span>
                                        <span class="value"><?php echo $product_subtotal; ?></span>
                                    </div>
                                </div>

                                <?php
                                // Display product attributes/variations
                                $item_data = function_exists('wc_get_formatted_cart_item_data') ? wc_get_formatted_cart_item_data($cart_item) : '';
                                if ($item_data):
                                    ?>
                                    <div class="product-attributes">
                                        <?php echo $item_data; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (method_exists($_product, 'get_short_description') && $_product->get_short_description()): ?>
                                    <div class="product-description">
                                        <p><?php echo function_exists('wp_kses_post') ? wp_kses_post($_product->get_short_description()) : strip_tags($_product->get_short_description()); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>

                <!-- Order Summary -->
                <div class="order-summary">
                    <h4>Order Summary</h4>
                    <div class="summary-row">
                        <span class="label">Subtotal:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_cart_subtotal() : '$0.00'; ?></span>
                    </div>

                    <?php if (function_exists('WC') && WC()->cart && WC()->cart->get_cart_tax()): ?>
                        <div class="summary-row">
                            <span class="label">Tax:</span>
                            <span
                                class="value"><?php echo function_exists('wc_price') ? wc_price(WC()->cart->get_cart_tax()) : '$0.00'; ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (function_exists('WC') && WC()->cart && method_exists(WC()->cart, 'needs_shipping') && WC()->cart->needs_shipping()): ?>
                        <div class="summary-row">
                            <span class="label">Shipping:</span>
                            <span class="value"><?php echo WC()->cart->get_cart_shipping_total(); ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="summary-row total">
                        <span class="label">Total:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_total() : '$0.00'; ?></span>
                    </div>
                </div>

            <?php else: ?>
                <div class="empty-cart">
                    <p>Your cart is empty.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="product-modal-footer">
            <button type="button" class="button button-secondary" id="close-product-modal-footer">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Include external JavaScript if available -->
<?php if (file_exists($js_file)): ?>
    <script>
        <?php include $js_file; ?>
    </script>
<?php endif; ?>