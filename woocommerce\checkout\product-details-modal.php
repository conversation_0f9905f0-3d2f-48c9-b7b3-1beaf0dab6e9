<?php
/**
 * Product Details Modal for Checkout
 *
 * This template displays a modal with product details and images
 * that can be opened via a button next to the place order button.
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include external CSS and JS files
$css_file = __DIR__ . '/product-details-modal.css';
$js_file = __DIR__ . '/product-details-modal.js';

// Get cart items for the modal
$cart_items = function_exists('WC') && WC()->cart ? WC()->cart->get_cart() : array();
?>

<!-- Include external CSS if available -->
<?php if (file_exists($css_file)): ?>
    <link rel="stylesheet" href="<?php echo dirname($_SERVER['REQUEST_URI']) . '/product-details-modal.css'; ?>">
<?php endif; ?>

<!-- Product Details Button -->
<div class="product-details-button-container" style="margin-bottom: 15px;">
    <button type="button" id="show-product-details" class="button product-details-btn">
        <i class="icon-eye"></i> View Product Details
    </button>
</div>

<!-- Product Details Modal -->
<div id="product-details-modal" class="product-modal" style="display: none;">
    <div class="product-modal-content">
        <div class="product-modal-header">
            <h3>Order Details</h3>
            <button type="button" class="product-modal-close" id="close-product-modal">
                <span>&times;</span>
            </button>
        </div>

        <div class="product-modal-body">
            <?php if (!empty($cart_items)): ?>
                <div class="product-details-list">
                    <?php foreach ($cart_items as $cart_item_key => $cart_item): ?>
                        <?php
                        $_product = isset($cart_item['data']) ? $cart_item['data'] : null;

                        if (!$_product || !method_exists($_product, 'exists') || !$_product->exists() || $cart_item['quantity'] <= 0) {
                            continue;
                        }

                        $product_id = method_exists($_product, 'get_id') ? $_product->get_id() : 0;
                        $product_name = method_exists($_product, 'get_name') ? $_product->get_name() : 'Product';
                        $product_price = function_exists('WC') && WC()->cart ? WC()->cart->get_product_price($_product) : '$0.00';
                        $product_subtotal = function_exists('WC') && WC()->cart ? WC()->cart->get_product_subtotal($_product, $cart_item['quantity']) : '$0.00';
                        $product_image = method_exists($_product, 'get_image') ? $_product->get_image('medium') : '<img src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'150\' height=\'150\'%3E%3Crect width=\'150\' height=\'150\' fill=\'%23f0f0f0\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' text-anchor=\'middle\' dy=\'.3em\' fill=\'%23999\'%3ENo Image%3C/text%3E%3C/svg%3E" alt="No Image">';
                        $product_permalink = method_exists($_product, 'is_visible') && $_product->is_visible() && method_exists($_product, 'get_permalink') ? $_product->get_permalink($cart_item) : '';
                        ?>

                        <div class="product-detail-item">
                            <div class="product-image">
                                <?php if ($product_permalink): ?>
                                    <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                        <?php echo $product_image; ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo $product_image; ?>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <h4 class="product-name">
                                    <?php if ($product_permalink): ?>
                                        <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                            <?php echo wp_kses_post($product_name); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo wp_kses_post($product_name); ?>
                                    <?php endif; ?>
                                </h4>

                                <div class="product-meta">
                                    <div class="product-price">
                                        <span class="label">Price:</span>
                                        <span class="value"><?php echo $product_price; ?></span>
                                    </div>

                                    <div class="product-quantity">
                                        <span class="label">Quantity:</span>
                                        <span class="value"><?php echo $cart_item['quantity']; ?></span>
                                    </div>

                                    <div class="product-subtotal">
                                        <span class="label">Subtotal:</span>
                                        <span class="value"><?php echo $product_subtotal; ?></span>
                                    </div>
                                </div>

                                <?php
                                // Display product attributes/variations
                                $item_data = function_exists('wc_get_formatted_cart_item_data') ? wc_get_formatted_cart_item_data($cart_item) : '';
                                if ($item_data):
                                    ?>
                                    <div class="product-attributes">
                                        <?php echo $item_data; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (method_exists($_product, 'get_short_description') && $_product->get_short_description()): ?>
                                    <div class="product-description">
                                        <p><?php echo function_exists('wp_kses_post') ? wp_kses_post($_product->get_short_description()) : strip_tags($_product->get_short_description()); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>

                <!-- Order Summary -->
                <div class="order-summary">
                    <h4>Order Summary</h4>
                    <div class="summary-row">
                        <span class="label">Subtotal:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_cart_subtotal() : '$0.00'; ?></span>
                    </div>

                    <?php if (function_exists('WC') && WC()->cart && WC()->cart->get_cart_tax()): ?>
                        <div class="summary-row">
                            <span class="label">Tax:</span>
                            <span
                                class="value"><?php echo function_exists('wc_price') ? wc_price(WC()->cart->get_cart_tax()) : '$0.00'; ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (function_exists('WC') && WC()->cart && method_exists(WC()->cart, 'needs_shipping') && WC()->cart->needs_shipping()): ?>
                        <div class="summary-row">
                            <span class="label">Shipping:</span>
                            <span class="value"><?php echo WC()->cart->get_cart_shipping_total(); ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="summary-row total">
                        <span class="label">Total:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_total() : '$0.00'; ?></span>
                    </div>
                </div>

            <?php else: ?>
                <div class="empty-cart">
                    <p>Your cart is empty.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="product-modal-footer">
            <button type="button" class="button button-secondary" id="close-product-modal-footer">
                Close
            </button>
        </div>
    </div>
</div>

<style>
    /* Product Details Button */
    .product-details-button-container {
        text-align: center;
    }

    .product-details-btn {
        background-color: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
        padding: 10px 20px !important;
        font-size: 14px !important;
        border-radius: 4px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .product-details-btn:hover {
        background-color: #e9ecef !important;
        border-color: #adb5bd !important;
    }

    .product-details-btn i {
        font-size: 16px;
    }

    /* Product Modal Styles */
    .product-modal {
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .product-modal-content {
        background-color: #fff;
        border-radius: 8px;
        width: 90%;
        max-width: 900px;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        animation: modalSlideIn 0.3s ease-out;
        position: relative;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-30px) scale(0.95);
        }

        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .product-modal-header {
        padding: 20px 25px 15px;
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .product-modal-header h3 {
        margin: 0;
        font-size: 1.4em;
        color: #333;
        font-weight: 600;
    }

    .product-modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #666;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .product-modal-close:hover {
        background-color: #e9ecef;
        color: #333;
    }

    .product-modal-body {
        padding: 20px 25px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .product-details-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .product-detail-item {
        display: flex;
        gap: 20px;
        padding: 15px;
        border: 1px solid #eee;
        border-radius: 8px;
        background-color: #fafafa;
    }

    .product-image {
        flex-shrink: 0;
        width: 120px;
    }

    .product-image img {
        width: 100%;
        height: auto;
        border-radius: 4px;
    }

    .product-info {
        flex: 1;
    }

    .product-name {
        margin: 0 0 10px 0;
        font-size: 1.1em;
        font-weight: 600;
    }

    .product-name a {
        color: #333;
        text-decoration: none;
    }

    .product-name a:hover {
        color: #0073aa;
    }

    .product-meta {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 8px;
        margin-bottom: 10px;
    }

    .product-meta>div {
        display: flex;
        justify-content: space-between;
    }

    .product-meta .label {
        font-weight: 500;
        color: #666;
    }

    .product-meta .value {
        font-weight: 600;
        color: #333;
    }

    .product-attributes {
        margin: 10px 0;
        font-size: 0.9em;
        color: #666;
    }

    .product-description {
        margin-top: 10px;
        font-size: 0.9em;
        color: #555;
        line-height: 1.4;
    }

    .order-summary {
        margin-top: 30px;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .order-summary h4 {
        margin: 0 0 15px 0;
        font-size: 1.2em;
        color: #333;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
    }

    .summary-row.total {
        border-top: 2px solid #ddd;
        margin-top: 10px;
        padding-top: 10px;
        font-weight: 600;
        font-size: 1.1em;
    }

    .product-modal-footer {
        padding: 15px 25px;
        border-top: 1px solid #eee;
        background-color: #f8f9fa;
        border-radius: 0 0 8px 8px;
        text-align: right;
    }

    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        color: #666;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .product-modal-content {
            width: 95%;
            max-height: 90vh;
        }

        .product-detail-item {
            flex-direction: column;
            gap: 15px;
        }

        .product-image {
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
        }

        .product-meta {
            grid-template-columns: 1fr;
        }

        .product-modal-header,
        .product-modal-body,
        .product-modal-footer {
            padding-left: 20px;
            padding-right: 20px;
        }
    }

    @media (max-width: 480px) {
        .product-modal-header h3 {
            font-size: 1.2em;
        }

        .product-details-btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const showBtn = document.getElementById('show-product-details');
        const modal = document.getElementById('product-details-modal');
        const closeBtn = document.getElementById('close-product-modal');
        const closeFooterBtn = document.getElementById('close-product-modal-footer');

        if (!showBtn || !modal) return;

        // Show modal
        showBtn.addEventListener('click', function (e) {
            e.preventDefault();
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Focus on close button for accessibility
            setTimeout(() => {
                if (closeBtn) closeBtn.focus();
            }, 300);
        });

        // Close modal functions
        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            showBtn.focus(); // Return focus to trigger button
        }

        // Close button events
        if (closeBtn) {
            closeBtn.addEventListener('click', closeModal);
        }

        if (closeFooterBtn) {
            closeFooterBtn.addEventListener('click', closeModal);
        }

        // Close on outside click
        modal.addEventListener('click', function (e) {
            if (e.target === modal) {
                closeModal();
            }
        });

        // Close on Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                closeModal();
            }
        });

        // Trap focus within modal
        modal.addEventListener('keydown', function (e) {
            if (e.key === 'Tab') {
                const focusableElements = modal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );

                const firstElement = focusableElements[0];
                const lastElement = focusableElements[focusableElements.length - 1];

                if (e.shiftKey && document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                } else if (!e.shiftKey && document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        });
    });
</script>

<!-- Include external JavaScript if available -->
<?php if (file_exists($js_file)): ?>
    <script src="<?php echo dirname($_SERVER['REQUEST_URI']) . '/product-details-modal.js'; ?>"></script>
<?php endif; ?>