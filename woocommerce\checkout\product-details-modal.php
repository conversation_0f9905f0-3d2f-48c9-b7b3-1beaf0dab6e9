<?php
/**
 * Product Details Modal for Checkout
 *
 * This template displays a modal with product details and images
 * that can be opened via a button next to the place order button.
 */

if (!defined('ABSPATH')) {
    exit;
}

// Include external CSS and JS files
$css_file = __DIR__ . '/product-details-modal.css';
$js_file = __DIR__ . '/product-details-modal.js';

// Get cart items for the modal
$cart_items = function_exists('WC') && WC()->cart ? WC()->cart->get_cart() : array();
?>

<!-- Modal CSS -->
<style>
    /* Product Details Button */
    .product-details-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        border: none !important;
        padding: 12px 24px !important;
        font-size: 14px !important;
        border-radius: 6px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 8px !important;
        margin-bottom: 10px !important;
    }

    .product-details-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
    }

    .product-details-btn i {
        font-size: 16px;
    }

    /* Product Modal */
    .product-modal {
        position: fixed !important;
        z-index: 9999999 !important;
        left: 0 !important;
        top: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        background-color: rgba(0, 0, 0, 0.8) !important;
        backdrop-filter: blur(3px) !important;
        display: none !important;
        align-items: center !important;
        justify-content: center !important;
        overflow: auto !important;
    }

    .product-modal[style*="flex"] {
        display: flex !important;
    }

    .product-modal.modal-visible {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .product-modal-content {
        background-color: #fff !important;
        border-radius: 12px !important;
        width: 90% !important;
        max-width: 900px !important;
        max-height: 85vh !important;
        overflow-y: auto !important;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3) !important;
        animation: modalSlideIn 0.3s ease-out !important;
        position: relative !important;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-30px) scale(0.95);
        }

        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .product-modal-header {
        padding: 20px 25px 15px !important;
        border-bottom: 1px solid #eee !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-radius: 12px 12px 0 0 !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .product-modal-header h3 {
        margin: 0 !important;
        font-size: 1.4em !important;
        color: #333 !important;
        font-weight: 600 !important;
    }

    .product-modal-close {
        background: none !important;
        border: none !important;
        font-size: 24px !important;
        cursor: pointer !important;
        color: #666 !important;
        padding: 0 !important;
        width: 30px !important;
        height: 30px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 50% !important;
        transition: all 0.3s ease !important;
    }

    .product-modal-close:hover {
        background-color: #e9ecef !important;
        color: #333 !important;
    }

    .product-modal-body {
        padding: 20px 25px !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
    }

    .product-details-list {
        display: flex !important;
        flex-direction: column !important;
        gap: 20px !important;
    }

    .product-detail-item {
        display: flex !important;
        gap: 20px !important;
        padding: 15px !important;
        border: 1px solid #eee !important;
        border-radius: 8px !important;
        background-color: #fafafa !important;
    }

    .product-image {
        flex-shrink: 0 !important;
        width: 120px !important;
    }

    .product-image img {
        width: 100% !important;
        height: auto !important;
        border-radius: 4px !important;
    }

    .product-info {
        flex: 1 !important;
    }

    .product-name {
        margin: 0 0 10px 0 !important;
        font-size: 1.1em !important;
        font-weight: 600 !important;
    }

    .product-meta {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
        gap: 8px !important;
        margin-bottom: 10px !important;
    }

    .product-meta>div {
        display: flex !important;
        justify-content: space-between !important;
    }

    .product-meta .label {
        font-weight: 500 !important;
        color: #666 !important;
    }

    .product-meta .value {
        font-weight: 600 !important;
        color: #333 !important;
    }

    .order-summary {
        margin-top: 30px !important;
        padding: 20px !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-radius: 8px !important;
        border: 1px solid #e9ecef !important;
    }

    .order-summary h4 {
        margin: 0 0 15px 0 !important;
        font-size: 1.2em !important;
        color: #333 !important;
    }

    .summary-row {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 8px !important;
        padding: 5px 0 !important;
    }

    .summary-row.total {
        border-top: 2px solid #ddd !important;
        margin-top: 10px !important;
        padding-top: 10px !important;
        font-weight: 600 !important;
        font-size: 1.1em !important;
    }

    .product-modal-footer {
        padding: 15px 25px !important;
        border-top: 1px solid #eee !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-radius: 0 0 12px 12px !important;
        text-align: right !important;
    }

    .empty-cart {
        text-align: center !important;
        padding: 40px 20px !important;
        color: #666 !important;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .product-modal-content {
            width: 95% !important;
            max-height: 90vh !important;
        }

        .product-detail-item {
            flex-direction: column !important;
            gap: 15px !important;
        }

        .product-image {
            width: 100% !important;
            max-width: 200px !important;
            margin: 0 auto !important;
        }
    }
</style>

<!-- Product Details Button -->
<div class="product-details-button-container" style="margin-bottom: 15px;">
    <button type="button" id="show-product-details" class="button product-details-btn">
        <i class="icon-eye"></i> View Product Details
    </button>
</div>

<!-- Product Details Modal -->
<div id="product-details-modal" class="product-modal" style="display: none;">
    <div class="product-modal-content">
        <div class="product-modal-header">
            <h3>Order Details</h3>
            <button type="button" class="product-modal-close" id="close-product-modal">
                <span>&times;</span>
            </button>
        </div>

        <div class="product-modal-body">
            <?php if (!empty($cart_items)): ?>
                <div class="product-details-list">
                    <?php foreach ($cart_items as $cart_item_key => $cart_item): ?>
                        <?php
                        $_product = isset($cart_item['data']) ? $cart_item['data'] : null;

                        if (!$_product || !method_exists($_product, 'exists') || !$_product->exists() || $cart_item['quantity'] <= 0) {
                            continue;
                        }

                        $product_id = method_exists($_product, 'get_id') ? $_product->get_id() : 0;
                        $product_name = method_exists($_product, 'get_name') ? $_product->get_name() : 'Product';
                        $product_price = function_exists('WC') && WC()->cart ? WC()->cart->get_product_price($_product) : '$0.00';
                        $product_subtotal = function_exists('WC') && WC()->cart ? WC()->cart->get_product_subtotal($_product, $cart_item['quantity']) : '$0.00';
                        $product_image = method_exists($_product, 'get_image') ? $_product->get_image('medium') : '<img src="data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'150\' height=\'150\'%3E%3Crect width=\'150\' height=\'150\' fill=\'%23f0f0f0\'/%3E%3Ctext x=\'50%25\' y=\'50%25\' text-anchor=\'middle\' dy=\'.3em\' fill=\'%23999\'%3ENo Image%3C/text%3E%3C/svg%3E" alt="No Image">';
                        $product_permalink = method_exists($_product, 'is_visible') && $_product->is_visible() && method_exists($_product, 'get_permalink') ? $_product->get_permalink($cart_item) : '';
                        ?>

                        <div class="product-detail-item">
                            <div class="product-image">
                                <?php if ($product_permalink): ?>
                                    <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                        <?php echo $product_image; ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo $product_image; ?>
                                <?php endif; ?>
                            </div>

                            <div class="product-info">
                                <h4 class="product-name">
                                    <?php if ($product_permalink): ?>
                                        <a href="<?php echo esc_url($product_permalink); ?>" target="_blank">
                                            <?php echo wp_kses_post($product_name); ?>
                                        </a>
                                    <?php else: ?>
                                        <?php echo wp_kses_post($product_name); ?>
                                    <?php endif; ?>
                                </h4>

                                <div class="product-meta">
                                    <div class="product-price">
                                        <span class="label">Price:</span>
                                        <span class="value"><?php echo $product_price; ?></span>
                                    </div>

                                    <div class="product-quantity">
                                        <span class="label">Quantity:</span>
                                        <span class="value"><?php echo $cart_item['quantity']; ?></span>
                                    </div>

                                    <div class="product-subtotal">
                                        <span class="label">Subtotal:</span>
                                        <span class="value"><?php echo $product_subtotal; ?></span>
                                    </div>
                                </div>

                                <?php
                                // Display product attributes/variations
                                $item_data = function_exists('wc_get_formatted_cart_item_data') ? wc_get_formatted_cart_item_data($cart_item) : '';
                                if ($item_data):
                                    ?>
                                    <div class="product-attributes">
                                        <?php echo $item_data; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (method_exists($_product, 'get_short_description') && $_product->get_short_description()): ?>
                                    <div class="product-description">
                                        <p><?php echo function_exists('wp_kses_post') ? wp_kses_post($_product->get_short_description()) : strip_tags($_product->get_short_description()); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                    <?php endforeach; ?>
                </div>

                <!-- Order Summary -->
                <div class="order-summary">
                    <h4>Order Summary</h4>
                    <div class="summary-row">
                        <span class="label">Subtotal:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_cart_subtotal() : '$0.00'; ?></span>
                    </div>

                    <?php if (function_exists('WC') && WC()->cart && WC()->cart->get_cart_tax()): ?>
                        <div class="summary-row">
                            <span class="label">Tax:</span>
                            <span
                                class="value"><?php echo function_exists('wc_price') ? wc_price(WC()->cart->get_cart_tax()) : '$0.00'; ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if (function_exists('WC') && WC()->cart && method_exists(WC()->cart, 'needs_shipping') && WC()->cart->needs_shipping()): ?>
                        <div class="summary-row">
                            <span class="label">Shipping:</span>
                            <span class="value"><?php echo WC()->cart->get_cart_shipping_total(); ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="summary-row total">
                        <span class="label">Total:</span>
                        <span
                            class="value"><?php echo function_exists('WC') && WC()->cart ? WC()->cart->get_total() : '$0.00'; ?></span>
                    </div>
                </div>

            <?php else: ?>
                <div class="empty-cart">
                    <p>Your cart is empty.</p>
                </div>
            <?php endif; ?>
        </div>

        <div class="product-modal-footer">
            <button type="button" class="button button-secondary" id="close-product-modal-footer">
                Close
            </button>
        </div>
    </div>
</div>

<!-- Simple JavaScript for modal functionality -->
<script>
    (function () {
        'use strict';

        function initProductModal() {
            console.log('Initializing Product Details Modal...');

            const showBtn = document.getElementById('show-product-details');
            const modal = document.getElementById('product-details-modal');
            const closeBtn = document.getElementById('close-product-modal');
            const closeFooterBtn = document.getElementById('close-product-modal-footer');

            console.log('Elements found:', {
                showBtn: !!showBtn,
                modal: !!modal,
                closeBtn: !!closeBtn,
                closeFooterBtn: !!closeFooterBtn
            });

            if (!showBtn) {
                console.error('Show button not found!');
                return;
            }

            if (!modal) {
                console.error('Modal not found!');
                return;
            }

            console.log('All elements found, binding events...');

            // Show modal
            showBtn.onclick = function (e) {
                e.preventDefault();
                console.log('Button clicked - showing modal');

                // Force show modal with multiple methods
                modal.style.display = 'flex';
                modal.style.visibility = 'visible';
                modal.style.opacity = '1';
                modal.classList.add('modal-visible');

                document.body.style.overflow = 'hidden';

                console.log('Modal display style:', modal.style.display);
                console.log('Modal computed style:', window.getComputedStyle(modal).display);
                console.log('Modal visibility:', window.getComputedStyle(modal).visibility);
                console.log('Modal z-index:', window.getComputedStyle(modal).zIndex);

                return false;
            };

            // Close modal function
            function closeModal() {
                console.log('Closing modal');
                modal.style.display = 'none';
                modal.style.visibility = 'hidden';
                modal.style.opacity = '0';
                modal.classList.remove('modal-visible');
                document.body.style.overflow = '';
                console.log('Modal closed successfully');
            }

            // Close button events
            if (closeBtn) {
                console.log('Binding close button event');
                closeBtn.onclick = function (e) {
                    e.preventDefault();
                    console.log('Close button clicked');
                    closeModal();
                    return false;
                };
            }

            if (closeFooterBtn) {
                console.log('Binding footer close button event');
                closeFooterBtn.onclick = function (e) {
                    e.preventDefault();
                    console.log('Footer close button clicked');
                    closeModal();
                    return false;
                };
            }

            // Close on outside click
            modal.onclick = function (e) {
                if (e.target === modal) {
                    console.log('Outside click detected - closing modal');
                    closeModal();
                }
            };

            // Close on Escape key
            document.onkeydown = function (e) {
                if (e.key === 'Escape' && modal.style.display === 'flex') {
                    console.log('Escape key pressed - closing modal');
                    closeModal();
                }
            };

            console.log('Product Details Modal initialized successfully!');
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initProductModal);
        } else {
            initProductModal();
        }

        // Also try to initialize after a short delay as fallback
        setTimeout(initProductModal, 1000);
    })();
</script>