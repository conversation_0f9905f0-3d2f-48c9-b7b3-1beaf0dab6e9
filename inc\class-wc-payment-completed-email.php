<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

class WC_Email_Payment_Completed extends WC_Email {

    public function __construct() {
        $this->id          = 'payment_completed';
        $this->title       = __('Payment Completed', 'woocommerce');
        $this->description = __('This email is sent when the payment is completed.', 'woocommerce');

        $this->heading     = __('Payment Completed', 'woocommerce');
        $this->subject     = __('Your order payment is completed - {order_number}', 'woocommerce');

        // Template paths
        $this->template_html  =  __DIR__ . 'woocommerce/emails/payment-completed-email.php';
        $this->template_plain =  __DIR__ . 'woocommerce/emails/plain/payment-completed-email.php';

        // Trigger action
        add_action('woocommerce_order_status_payment-completed_notification', array($this, 'trigger'));

        // Call parent constructor
        parent::__construct();

        // Set email recipient
        $this->recipient = $this->get_option('recipient', $this->get_recipient());
    }

    public function trigger( $order_id ) {
        if ( ! $order_id ) {
            return;
        }

        $this->object = wc_get_order( $order_id );

        if ( $this->object ) {
            $this->recipient = $this->object->get_billing_email();
            $this->placeholders = array(
                '{order_date}'   => wc_format_datetime( $this->object->get_date_created() ),
                '{order_number}' => $this->object->get_order_number(),
            );
        }

        if ( $this->is_enabled() && $this->get_recipient() ) {
            $this->send($this->get_recipient(), $this->get_subject(), $this->get_content(), $this->get_headers(), $this->get_attachments());
        }
    }

    public function get_content_html() {
        return wc_get_template_html($this->template_html, array(
            'order'         => $this->object,
            'email_heading' => $this->get_heading(),
            'sent_to_admin' => false,
            'plain_text'    => false,
            'email'         => $this,
        ));
    }

    public function get_content_plain() {
        return wc_get_template_html($this->template_plain, array(
            'order'         => $this->object,
            'email_heading' => $this->get_heading(),
            'sent_to_admin' => false,
            'plain_text'    => true,
            'email'         => $this,
        ));
    }

    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __('Enable/Disable', 'woocommerce'),
                'type'    => 'checkbox',
                'label'   => __('Enable this email notification', 'woocommerce'),
                'default' => 'yes',
            ),
            'recipient' => array(
                'title'       => __('Recipient(s)', 'woocommerce'),
                'type'        => 'text',
                'description' => __('Enter recipients (comma separated) for this email. Defaults to the customer email.', 'woocommerce'),
                'placeholder' => '',
                'default'     => 'Customer',
            ),
            'subject' => array(
                'title'       => __('Subject', 'woocommerce'),
                'type'        => 'text',
                'description' => __('This controls the email subject line. Leave blank to use the default subject.', 'woocommerce'),
                'placeholder' => '',
                'default'     => '',
            ),
            'heading' => array(
                'title'       => __('Email Heading', 'woocommerce'),
                'type'        => 'text',
                'description' => __('This controls the main heading contained within the email notification.', 'woocommerce'),
                'placeholder' => '',
                'default'     => '',
            ),
        );
    }
}
